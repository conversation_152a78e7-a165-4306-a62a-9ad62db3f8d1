// 导入字符串工具类，用于生成模板字符串
import { StringUtil } from '../../../util/StringUtil';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入Grid属性映射数据
import {
  columnsGapMapData,
  columnsTemplateMapData,
  operationModeMapData,
  rowsGapMapData,
  rowsTemplateMapData,
  scrollerMapData,
} from '../entity/GridAttributeMapping';

/**
 * Grid描述器类
 * 继承通用描述器，用于存储和管理Grid布局的所有属性配置
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class GridDescriptor extends CommonDescriptor {
  // 滚动控制器，用于控制Grid的滚动行为
  public scroller: Scroller = scrollerMapData.get('default')!.value;
  // 列间距，设置Grid列之间的间距
  public columnsGap: number = columnsGapMapData.get('default')!.value;
  // 行间距，设置Grid行之间的间距
  public rowsGap: number = rowsGapMapData.get('default')!.value;
  // 列模板，定义Grid的列布局模式
  public columnsTemplate: string = columnsTemplateMapData.get('default')!.value;
  // 行模板，定义Grid的行布局模式
  public rowsTemplate: string = rowsTemplateMapData.get('default')!.value;
  // 操作模式，控制Grid是否支持编辑操作（如拖拽）
  public operationMode: boolean = operationModeMapData.get('default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为Grid描述器的具体属性值
   * @param attributes 原始属性数组，包含所有Grid布局的配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理列间距属性
        case 'columnsGap':
          // 将字符串值转换为数字
          this.columnsGap = Number(attribute.currentValue);
          break;
        // 处理行间距属性
        case 'rowsGap':
          // 将字符串值转换为数字
          this.rowsGap = Number(attribute.currentValue);
          break;
        // 处理列数量属性
        case 'columnsNum':
          // 根据列数量生成对应的模板字符串
          this.columnsTemplate = StringUtil.getTemplateString(Number(attribute.currentValue));
          break;
        // 处理行数量属性
        case 'rowsNum':
          // 根据行数量生成对应的模板字符串
          this.rowsTemplate = StringUtil.getTemplateString(Number(attribute.currentValue));
          break;
        // 处理操作模式属性
        case 'operationMode':
          // 解析JSON字符串为布尔值
          this.operationMode = JSON.parse(attribute.currentValue);
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
// 导入通用布尔映射和通用字符串映射，用于标签页组件属性配置
import { CommonBoolMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

// 定义标签页值类型，支持标签栏位置或模糊样式
type TabsValue = BarPosition | BlurStyle;

/**
 * 标签页映射类
 * 用于存储标签页组件属性的代码字符串和实际值的映射关系
 */
class TabsMapping {
  // 代码字符串属性，用于代码生成
  public code: string;
  // 标签页值属性，存储实际的标签页属性值
  public value: TabsValue;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 标签页值，实际的标签页属性值
   */
  constructor(code: string, value: TabsValue) {
    // 初始化代码字符串
    this.code = code;
    // 初始化标签页值
    this.value = value;
  }
}

export const barPositionMapData: Map<string, TabsMapping> = new Map([
  ['Default', new TabsMapping('BarPosition.Start', BarPosition.Start)],
  ['Start', new TabsMapping('BarPosition.Start', BarPosition.Start)],
  ['End', new TabsMapping('BarPosition.End', BarPosition.End)],
]);

export const blurStyleMapData: Map<string, TabsMapping> = new Map([
  ['Default', new TabsMapping('BlurStyle.NONE', BlurStyle.NONE)],
  ['None', new TabsMapping('BlurStyle.NONE', BlurStyle.NONE)],
  ['Thin', new TabsMapping('BlurStyle.Thin', BlurStyle.Thin)],
  ['Regular', new TabsMapping('BlurStyle.Regular', BlurStyle.Regular)],
  ['Thick', new TabsMapping('BlurStyle.Thick', BlurStyle.Thick)],
  ['BackgroundThin', new TabsMapping('BlurStyle.BACKGROUND_THIN', BlurStyle.BACKGROUND_THIN)],
  ['BackgroundRegular', new TabsMapping('BlurStyle.BACKGROUND_REGULAR', BlurStyle.BACKGROUND_REGULAR)],
  ['BackgroundThick', new TabsMapping('BlurStyle.BACKGROUND_THICK', BlurStyle.BACKGROUND_THICK)],
  ['BackgroundUltraThick', new TabsMapping('BlurStyle.BACKGROUND_ULTRA_THICK', BlurStyle.BACKGROUND_ULTRA_THICK)],
  ['ComponentThin', new TabsMapping('BlurStyle.COMPONENT_THIN', BlurStyle.COMPONENT_THIN)],
  ['ComponentUltraThin', new TabsMapping('BlurStyle.COMPONENT_ULTRA_THIN', BlurStyle.COMPONENT_ULTRA_THIN)],
  ['ComponentRegular', new TabsMapping('BlurStyle.COMPONENT_REGULAR', BlurStyle.COMPONENT_REGULAR)],
  ['ComponentUltraThick', new TabsMapping('BlurStyle.COMPONENT_ULTRA_THICK', BlurStyle.COMPONENT_ULTRA_THICK)],
  ['ComponentThick', new TabsMapping('BlurStyle.COMPONENT_THICK', BlurStyle.COMPONENT_THICK)],
]);

export const fadingEdgeMapData: Map<string, CommonBoolMapping> = new Map([
  ['Default', new CommonBoolMapping('true', true)],
]);

export const verticalMapData: Map<string, CommonBoolMapping> = new Map([
  ['Default', new CommonBoolMapping('false', false)],
]);

export const barWidthMapData: Map<string, CommonStringMapping> = new Map([
  ['Default', new CommonStringMapping('56vp', '56vp')],
]);

export const barHeightMapData: Map<string, CommonStringMapping> = new Map([
  ['Default', new CommonStringMapping('30vp', '30vp')],
]);
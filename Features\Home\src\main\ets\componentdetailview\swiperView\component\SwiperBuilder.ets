// 导入详情页常量，包含轮播组件背景色等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入轮播属性修改器，用于修改轮播组件属性
import { SwiperAttributeModifier } from '../viewmodel/SwiperAttributeModifier';
// 导入轮播描述器类型，用于描述轮播组件配置
import type { SwiperDescriptor } from '../viewmodel/SwiperDescriptor';

/**
 * 获取轮播数据函数
 * 返回用于轮播展示的字符串数组
 * @returns 包含轮播项目的字符串数组
 */
function getSwiperData(): string[] {
  // 创建包含三个项目的数组
  const list: string[] = ['1', '2', '3'];
  return list;
}

/**
 * 轮播组件构建器函数
 * 用于构建轮播组件，包含多个可滑动的文本项目
 * @param $$ 描述器包装对象，包含轮播组件配置信息
 */
@Builder
export function SwiperBuilder($$: DescriptorWrapper) {
  // 创建列布局容器
  Column() {
    // 创建轮播组件
    Swiper() {
      // 遍历轮播数据，为每个项目创建文本组件
      ForEach(getSwiperData(), (item: string, _index: number) => {
        // 创建文本组件显示轮播项目内容
        Text(item)
          // 设置文本高度
          .height($r('app.float.swiper_height'))
          // 设置背景颜色
          .backgroundColor(DetailPageConstant.SWIPER_BACKGROUND_COLOR)
          // 设置文本居中对齐
          .textAlign(TextAlign.Center)
          // 设置字体大小
          .fontSize($r('sys.float.Body_L'))
      }, (item: string) => item)
    }
    // 设置轮播组件宽度为100%
    .width('100%')
    // 设置轮播组件高度为100%
    .height('100%')
    // 设置不循环播放
    .loop(false)
    // 设置属性修改器，用于动态修改轮播组件属性
    .attributeModifier(new SwiperAttributeModifier($$.descriptor as SwiperDescriptor))
  }
  // 设置列容器宽度为100%
  .width('100%')
  // 设置列容器高度为100%
  .height('100%')
  // 设置内容居中对齐
  .justifyContent(FlexAlign.Center)
  // 设置圆角半径
  .borderRadius($r('sys.float.corner_radius_level8'))
  // 启用裁剪
  .clip(true)
}
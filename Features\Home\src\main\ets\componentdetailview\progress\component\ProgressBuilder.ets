// 导入详情页常量，包含进度条相关的尺寸配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入进度条属性修改器，用于修改组件属性
import { ProgressAttributeModifier } from '../viewmodel/ProgressAttributeModifier';
// 导入进度条描述器类型，用于描述组件配置
import type { ProgressDescriptor } from '../viewmodel/ProgressDescriptor';

/**
 * 进度条构建器函数
 * 用于构建进度条组件，支持普通进度条和加载进度条两种类型
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function ProgressBuilder($$: DescriptorWrapper) {
  // 创建列布局容器
  Column() {
    // 根据进度条类型创建不同的组件
    if (($$.descriptor as ProgressDescriptor).kind === 'Progress') {
      // 创建普通进度条组件
      Progress({
        // 设置当前进度值
        value: ($$.descriptor as ProgressDescriptor).value,
        // 设置最大进度值
        total: DetailPageConstant.PROGRESS_MAX_VALUE,
        // 设置进度条类型
        type: ($$.descriptor as ProgressDescriptor).type,
      })
        // 根据进度条类型设置高度，胶囊型和线型使用不同高度
        .height(($$.descriptor as ProgressDescriptor).type === ProgressType.Capsule ?
        DetailPageConstant.PROGRESS_CAPSULE_HEIGHT : DetailPageConstant.PROGRESS_LINE_HEIGHT)
        // 设置属性修改器，用于动态修改组件属性
        .attributeModifier(new ProgressAttributeModifier($$.descriptor as ProgressDescriptor))
    } else {
      // 创建加载进度条组件
      LoadingProgress()
        // 设置加载进度条颜色
        .color(($$.descriptor as ProgressDescriptor).color)
        // 设置加载进度条尺寸为圆形
        .size({ width: DetailPageConstant.PROGRESS_CIRCLE_WIDTH, height: DetailPageConstant.PROGRESS_CIRCLE_WIDTH })
    }
  }
  // 设置列布局内边距
  .padding($r('sys.float.padding_level16'))
  // 设置列布局垂直对齐方式为居中
  .justifyContent(FlexAlign.Center)
}
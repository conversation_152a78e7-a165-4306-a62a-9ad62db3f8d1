// 导入手写组件和控制器，用于实现手写功能
import { HandwriteComponent, HandwriteController } from '@kit.Penkit';
// 导入业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入断点类型、全局信息模型和日志工具
import { BreakpointTypeEnum, GlobalInfoModel, Logger } from '@ohos/common';
// 导入组件详情管理器，用于管理组件详情视图
import { ComponentDetailManager } from '../../../viewmodel/ComponentDetailManager';
// 导入详情页面常量，用于获取配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';

// 日志标签，用于标识手写组件的日志
const TAG: string = '[HandWritingComponent]';
// 按钮偏移量常量
const BUTTON_OFFSET: number = 16;

/**
 * 手写组件结构体
 * 提供完整的手写功能界面，包括手写区域和关闭按钮
 */
@Component
export struct HandWritingComponent {
  // 全局信息模型，用于获取断点信息和状态栏高度
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 手写控制器，用于控制手写功能
  private controller: HandwriteController = new HandwriteController();
  // 初始化路径，用于保存和加载手写内容
  private initPath: string = 'savePath';
  // 关闭按钮的顶部位置
  private closeButtonTop: number = BUTTON_OFFSET;

  /**
   * 组件即将出现时的生命周期方法
   * 根据当前断点调整关闭按钮的位置
   */
  aboutToAppear(): void {
    // 根据断点类型设置关闭按钮的偏移量
    if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 大屏设备使用特定的顶部偏移
      this.closeButtonTop = DetailPageConstant.PEN_CLOSE_TOP;
    } else {
      // 小屏设备使用默认偏移
      this.closeButtonTop = BUTTON_OFFSET;
    }
  }

  /**
   * 组件即将消失时的生命周期方法
   * 在手写组件退出时调用保存接口
   */
  aboutToDisappear() {
    // 保存手写内容到指定路径
    try {
      this.controller?.save(this.initPath);
    } catch (err) {
      // 捕获并记录保存错误
      const error: BusinessError = err as BusinessError;
      Logger.error(TAG, `HandwriteController save error, the code is ${error.code}, the message is ${error.message}`);
    }
  }

  /**
   * 构建组件UI方法
   * 创建包含手写区域和关闭按钮的完整界面
   */
  build() {
    // 创建导航目标容器
    NavDestination() {
      // 创建堆叠布局，关闭按钮位于右上角
      Stack({ alignContent: Alignment.TopEnd }) {
        // 创建手写组件
        HandwriteComponent({
          // 传入手写控制器
          handwriteController: this.controller,
          // 设置初始化回调，加载已保存的手写内容
          onInit: () => {
            try {
              // 从指定路径加载手写内容
              this.controller?.load(this.initPath);
            } catch (err) {
              // 捕获并记录加载错误
              const error: BusinessError = err as BusinessError;
              Logger.error(TAG,
                `HandwriteController load error, the code is ${error.code}, the message is ${error.message}`);
            }
          }
        })
        // 创建圆形关闭按钮
        Button({ type: ButtonType.Circle }) {
          // 使用系统关闭图标
          SymbolGlyph($r('sys.symbol.xmark'))
            // 设置图标颜色为主要图标色
            .fontColor([$r('sys.color.icon_primary')])
            // 设置图标大小
            .fontSize($r('sys.float.ohos_id_textfield_icon_size'))
        }
        // 设置按钮尺寸
        .width($r('sys.float.ohos_id_button_height'))
        .height($r('sys.float.ohos_id_button_height'))
        // 设置按钮背景色
        .backgroundColor($r('sys.color.comp_background_tertiary'))
        // 设置按钮位置，考虑状态栏高度
        .margin({ top: this.closeButtonTop + this.globalInfoModel.statusBarHeight, right: $r('sys.float.padding_level8') })
        // 设置点击事件，关闭手写界面
        .onClick(() => {
          ComponentDetailManager.getInstance().getDetailViewModel('Penkit')?.pop();
        })
      }
      // 设置堆叠容器尺寸
      .width('100%')
      .height('100%')
    }
    // 隐藏导航标题栏
    .hideTitleBar(true)
    // 设置导航目标尺寸
    .height('100%')
    .width('100%')
  }
}

/**
 * PenKit视图构建器函数
 * 用于创建手写组件的实例
 */
@Builder
export function PenKitViewBuilder() {
  // 创建手写组件实例
  HandWritingComponent()
}
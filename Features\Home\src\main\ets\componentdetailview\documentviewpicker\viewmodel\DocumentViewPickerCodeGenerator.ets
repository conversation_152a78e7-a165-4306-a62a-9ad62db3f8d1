// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 文档视图选择器代码生成器类
 * 实现通用代码生成器接口，用于生成文档视图选择器相关的代码
 */
export class DocumentViewPickerCodeGenerator implements CommonCodeGenerator {
  /**
   * 生成文档视图选择器代码的方法
   * 根据传入的属性数组生成完整的文档视图选择器组件代码字符串
   * @param _attributes 原始属性数组（此处未使用，因为文档选择器组件不需要额外配置）
   * @returns 生成的文档视图选择器组件代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 返回生成的完整文档视图选择器组件代码字符串
    return `import { common } from '@kit.AbilityKit';
import { picker } from '@kit.CoreFileKit';
import type { BusinessError } from '@kit.BasicServicesKit';

@Component
struct DocumentViewPickerComponent {
  @State message: string = '';
  @State title: string = '';

  build() {
    Column() {
      Button('选择文件')
        .backgroundColor($r('sys.color.background_secondary'))
        .width(120)
        .height(40)
        .fontColor($r('sys.color.font_emphasize'))
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .onClick(() => {
          let context = getContext(this) as common.Context;
          try {
            const documentSelectOptions = new picker.DocumentSelectOptions();
            const documentPicker = new picker.DocumentViewPicker(context);
            documentPicker.select(documentSelectOptions).then((documentSelectResult: Array<string>) => {
              this.message = JSON.stringify(documentSelectResult);
              if (documentSelectResult.length === 0) {
                return;
              }
              this.getUIContext().showAlertDialog(
                {
                  title: '文件路径',
                  message: this.message,
                  autoCancel: true,
                  alignment: DialogAlignment.Center,
                  offset: { dx: 0, dy: -20 },
                  gridCount: 3,
                  width: 300,
                  height: 300,
                  cornerRadius: $r('sys.float.corner_radius_level7'),
                  borderWidth: 1,
                  borderStyle: BorderStyle.Dashed,
                  borderColor: Color.Blue,
                  backgroundColor: Color.White,
                  textStyle: { wordBreak: WordBreak.BREAK_ALL },
                  confirm: {
                    value: '确定',
                    action: () => {
                      console.log('Confirm button is clicked.');
                    },
                  },
                  onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
                    if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
                      dismissDialogAction.dismiss();
                    }
                    if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
                      dismissDialogAction.dismiss();
                    }
                  }
                }
              )
            }).catch((err: BusinessError) => {
              console.error(\`DocumentViewPicker.select failed with err: \${err.code}, \${err.message}\`);
            });
          } catch (error) {
            const err: BusinessError = error as BusinessError;
            console.error(\`DocumentViewPicker failed with err: \${err.code}, \${err.message}\`);
          }
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}`;
  }
}
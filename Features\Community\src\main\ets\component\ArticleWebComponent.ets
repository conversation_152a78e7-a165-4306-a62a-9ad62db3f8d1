// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入方舟Web工具包中的webview模块
import { webview } from '@kit.ArkWeb';
// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的各种工具和类型
import {
  BreakpointTypeEnum,
  CommonConstants,
  GlobalInfoModel,
  javascriptProxyPermission,
  LoadingStatus,
  Logger,
  NativeActionData,
  TopNavigationView,
  WebSheetBuilder,
  WebUtil,
  WindowUtil
} from '@ohos/common';
// 导入通用业务模块中的基础详情组件
import { BaseDetailComponent } from '@ohos/commonbusiness';
// 导入文章详情视图模型和相关类型
import {
  ArticleDetailViewModel,
  ExplorationDetailEventType,
  NativePageParam,
} from '../viewmodel/ArticleDetailViewModel';
// 导入探索详情状态
import { ExplorationDetailState } from '../viewmodel/ExplorationDetailState';

// 定义日志标签常量
const TAG = '[ArticleWebComponent]';

// 使用Component装饰器定义文章Web组件
@Component
export struct ArticleWebComponent {
  // 创建Web视图控制器
  webController: webview.WebviewController = new webview.WebviewController();
  // 使用Prop和Require装饰器定义视图模型属性
  @Prop @Require viewModel: ArticleDetailViewModel;
  // 使用Prop和Require装饰器定义详情URL属性
  @Prop @Require detailsUrl: string;
  // 使用Prop装饰器定义标签视图类型属性
  @Prop tabViewType: number = -1;
  // 使用Link装饰器定义加载状态属性
  @Link loadingStatus: LoadingStatus;
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp和Watch装饰器获取系统颜色模式并监听变化
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 使用State装饰器定义绑定表单显示状态
  @State bindSheetShow: boolean = false;
  // 使用State装饰器定义绑定表单源地址
  @State bindSheetSrc: string = '';
  // 使用State装饰器定义详情状态
  @State detailState: ExplorationDetailState = this.viewModel.getState();
  // 使用State装饰器定义模糊状态
  @State isBlur: boolean = false;
  // 使用State装饰器定义标题
  @State title: string = '';
  // 创建绑定表单源地址集合
  bindSheetSrcSet: Set<string> = new Set();
  // 定义Web URL类型
  webUrlType: number = 0;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 处理颜色模式变化
    this.handleColorModeChange();
    // 注册JavaScript函数
    this.registerJsFunction();
  }

  // 定义即将消失生命周期方法
  aboutToDisappear(): void {
    // 尝试清理Web节点
    try {
      // 遍历绑定表单源地址集合
      this.bindSheetSrcSet.forEach((item: string) => {
        // 移除Web节点
        WebUtil.removeNode(item);
      });
    } catch (error) {
      // 捕获错误并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录错误日志
      Logger.error(TAG, `Web load Data error. ${err.code}, ${err.message}`);
    }
  }

  // 定义处理Web滚动的方法
  handleWebScroll(yOffset: number) {
    // 根据滚动偏移量设置标题
    this.title = yOffset > CommonConstants.NAVIGATION_HEIGHT ? this.detailState.content.title : '';
    // 根据滚动偏移量设置模糊状态
    this.isBlur = yOffset > CommonConstants.SPACE_12;
  }

  // 定义处理颜色模式变化的方法
  handleColorModeChange() {
    // 判断是否为系统深色模式
    const isSystemDark: boolean = (this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this), isSystemDark);
  }

  // 定义注册JavaScript函数的方法
  registerJsFunction() {
    // 设置Web表单操作
    WebUtil.setWebSheetAction(this.detailsUrl, (src: string, type: number) => {
      // 设置Web URL类型
      this.webUrlType = type;
      // 如果绑定表单源地址集合中不包含该地址
      if (!this.bindSheetSrcSet.has(src)) {
        // 创建Web节点
        WebUtil.createWebNode(src, this.getUIContext(), NestedScrollMode.SELF_ONLY);
        // 将地址添加到集合中
        this.bindSheetSrcSet.add(src);
      }
      // 显示绑定表单
      this.bindSheetShow = true;
      // 设置绑定表单源地址
      this.bindSheetSrc = src;
    });
    // 设置跳转页面操作
    WebUtil.setJumpPageAction(this.detailsUrl,
      (type: string, id: number, currentIndex?: number, componentName?: string) => {
        // 发送跳转原生页面事件
        this.viewModel.sendEvent<NativePageParam>({
          type: ExplorationDetailEventType.JUMP_NATIVE_PAGE,
          param: {
            tabBarView: this.tabViewType,
            type,
            id,
            currentIndex,
            componentName,
          },
        });
      })
  }

  // 使用Builder装饰器定义Web内容构建器
  @Builder
  WebContentBuilder() {
    // 创建Web组件
    Web({ src: this.detailsUrl, controller: this.webController })
      // 禁用缩放访问
      .zoomAccess(false)
      // 启用文件访问
      .fileAccess(true)
      // 设置混合模式为无
      .mixedMode(MixedMode.None)
      // 禁用垂直滚动条访问
      .verticalScrollBarAccess(false)
      // 禁用水平滚动条访问
      .horizontalScrollBarAccess(false)
      // 启用图片访问
      .imageAccess(true)
      // 设置缓存模式为默认
      .cacheMode(CacheMode.Default)
      // 启用DOM存储访问
      .domStorageAccess(true)
      // 启用JavaScript访问
      .javaScriptAccess(true)
      // 设置JavaScript代理
      .javaScriptProxy({
        object: new NativeActionData(this.detailsUrl),
        name: 'nativeActionData',
        methodList: ['webSheet', 'jumpPage'],
        controller: this.webController,
        permission: javascriptProxyPermission,
      })
      // 禁用地理位置访问
      .geolocationAccess(false)
      // 设置背景颜色
      .backgroundColor($r('sys.color.background_secondary'))
      // 设置过度滚动模式为总是
      .overScrollMode(OverScrollMode.ALWAYS)
      // 设置深色模式为自动
      .darkMode(WebDarkMode.Auto)
      // 启用强制深色访问
      .forceDarkAccess(true)
      // 禁用拖放
      .allowDrop(null)
      // 设置页面结束事件
      .onPageEnd(() => {
        // 设置加载状态为成功
        this.loadingStatus = LoadingStatus.SUCCESS;
        // 设置信任列表
        WebUtil.setTrustList(this.detailsUrl);
      })
      // 设置加载拦截事件
      .onLoadIntercept((event: OnLoadInterceptEvent) => {
        // 获取请求URL
        const tempUrl = event.data.getRequestUrl();
        // 检查URL
        return WebUtil.checkUrl(tempUrl);
      })
      // 设置控制台事件
      .onConsole((event: OnConsoleEvent) => {
        // 记录错误日志
        Logger.error(TAG, event.message.getMessage())
        // 返回false
        return false;
      })
      // 设置SSL错误事件接收
      .onSslErrorEventReceive((event) => {
        // 记录SSL检查失败错误日志
        Logger.error(TAG, `SSL checked failed, error: ${event.error.toString()}`);
        // 处理取消
        event.handler.handleCancel();
      })
      // 设置控制器附加事件
      .onControllerAttached(() => {
        // 设置Web控制器
        WebUtil.setWebController(this.detailsUrl, this.webController);
        // 设置允许跨域访问的本地文件路径
        this.webController.setPathAllowingUniversalAccess([getContext().resourceDir]);
      })
      // 设置滚动事件
      .onScroll((event) => {
        // 处理Web滚动
        this.handleWebScroll(event.yOffset);
      })
      // 设置宽度为100%
      .width('100%')
      // 设置高度为100%
      .height('100%')
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建顶部导航视图
    TopNavigationView({
      topNavigationData: {
        title: this.title,
        isBlur: this.isBlur,
        isFullScreen: true,
        onBackClick: () => {
          // 调用详情状态的返回点击事件
          this.detailState.topNavigationData.onBackClick?.();
        }
      },
    })
  }

  // 定义构建方法
  build() {
    // 创建基础详情组件
    BaseDetailComponent({
      detailContentView: () => {
        // 调用Web内容构建器
        this.WebContentBuilder()
      },
      topTitleView: () => {
        // 调用顶部标题视图构建器
        this.TopTitleViewBuilder()
      },
      loadingStatus: this.loadingStatus,
    })
      // 设置背景颜色为透明
      .backgroundColor(Color.Transparent)
      // 设置宽度为100%
      .width('100%')
      // 设置高度为100%
      .height('100%')
      // 绑定表单
      .bindSheet(this.bindSheetShow,
        WebSheetBuilder(this.bindSheetSrc, this.webUrlType), {
          title: { title: '' },
          preferType: SheetType.CENTER,
          height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
            ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
            CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,
          width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SHEET_WIDTH_XL :
            undefined,
          onDisappear: () => {
            // 隐藏绑定表单
            this.bindSheetShow = false;
          },
        })
  }
}
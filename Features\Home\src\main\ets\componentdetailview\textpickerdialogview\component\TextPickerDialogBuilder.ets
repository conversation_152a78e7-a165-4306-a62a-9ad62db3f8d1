// 导入方舟UI工具包中的提示操作模块，用于显示对话框
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器，用于记录日志信息
import { Logger } from '@ohos/common';
// 导入详情页常量，包含文本选择器对话框的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入文本选择器对话框描述器类型，用于描述对话框配置
import type { TextPickerDialogDescriptor } from '../viewmodel/TextPickerDialogDescriptor';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[TextPickerDialogBuilder]';

@Builder
export function TextPickerDialogBuilder($$: DescriptorWrapper) {
  Column() {
    Button($r('app.string.text_picker_dialog_tip'))
      .margin($r('sys.float.padding_level10'))
      .buttonStyle(ButtonStyleMode.NORMAL)
      .fontWeight(FontWeight.Medium)
      .fontSize($r('sys.float.Body_L'))
      .fontColor($r('sys.color.font_emphasize'))
      .onClick(() => {
        TextPickerDialog.show({
          range: $r('app.strarray.text_picker_data'),
          defaultPickerItemHeight: ($$.descriptor as TextPickerDialogDescriptor).itemHeight,
          canLoop: ($$.descriptor as TextPickerDialogDescriptor).canLoop,
          onAccept: (value: TextPickerResult) => {
            try {
              promptAction.showToast({
                message: `Select ${value.value}`,
                duration: DetailPageConstant.LONG_DURATION
              });
            } catch (err) {
              const error: BusinessError = err as BusinessError;
              Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
            }
          },
          onCancel: () => {
            try {
              promptAction.showToast({
                message: 'Canceled',
                duration: DetailPageConstant.LONG_DURATION
              });
            } catch (err) {
              const error: BusinessError = err as BusinessError;
              Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
            }
          },
        });
      })
  }
  .width('100%')
}
// 导入ArkUI工具包中的长度度量类，用于设置组件尺寸
import { LengthMetrics } from '@kit.ArkUI';
// 导入详情页面常量，用于获取Flex布局的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入Flex描述器类型，用于获取Flex布局的属性配置
import type { FlexDescriptor } from '../viewmodel/FlexDescriptor';
// 导入元素数量枚举，用于控制Flex容器中子元素的显示数量
import { ElementsNums } from '../entity/FlexAttributeMapping';

/**
 * Flex布局构建器函数
 * 用于构建可配置的Flex布局组件，展示不同的Flex属性效果
 * @param $$ 描述器包装对象，包含Flex布局的配置信息
 */
@Builder
export function FlexBuilder($$: DescriptorWrapper) {
  // 创建Flex布局容器
  Flex({
    // 设置主轴和交叉轴的间距
    space: {
      // 主轴间距，使用详情页面常量中定义的Flex间距
      main: LengthMetrics.vp(DetailPageConstant.FLEX_SPACE),
      // 交叉轴间距，使用详情页面常量中定义的Flex间距
      cross: LengthMetrics.vp(DetailPageConstant.FLEX_SPACE),
    },
    // 设置Flex布局方向，从描述器中获取
    direction: ($$.descriptor as FlexDescriptor).direction,
    // 设置Flex换行方式，从描述器中获取
    wrap: ($$.descriptor as FlexDescriptor).wrap,
    // 设置主轴对齐方式，从描述器中获取
    justifyContent: ($$.descriptor as FlexDescriptor).justifyContent,
    // 设置交叉轴对齐方式，从描述器中获取
    alignItems: ($$.descriptor as FlexDescriptor).alignItems,
    // 设置多行内容对齐方式，从描述器中获取
    alignContent: ($$.descriptor as FlexDescriptor).alignContent,
  }) {
    // 第一个文本元素，显示数字"1"
    Text('1')
      // 设置元素尺寸，使用应用资源中定义的容器尺寸1
      .size({ width: $r('app.float.container_size_1'), height: $r('app.float.container_size_1') })
      // 设置字体颜色为主色调上的字体颜色
      .fontColor($r('sys.color.font_on_primary'))
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置文本居中对齐
      .textAlign(TextAlign.Center)
      // 设置边框圆角
      .border({ radius: $r('sys.float.corner_radius_level4') })
    // 第二个文本元素，显示数字"2"
    Text('2')
      // 设置元素尺寸，使用应用资源中定义的容器尺寸2
      .size({ width: $r('app.float.container_size_2'), height: $r('app.float.container_size_2') })
      // 设置字体颜色为主色调上的字体颜色
      .fontColor($r('sys.color.font_on_primary'))
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置文本居中对齐
      .textAlign(TextAlign.Center)
      // 设置边框圆角
      .border({ radius: $r('sys.float.corner_radius_level4') })
      // 设置自身对齐方式，从描述器中获取alignSelf属性
      .alignSelf(($$.descriptor as FlexDescriptor).alignSelf)
    // 第三个文本元素，显示数字"3"
    Text('3')
      // 设置元素尺寸，使用应用资源中定义的容器尺寸3
      .size({ width: $r('app.float.container_size_3'), height: $r('app.float.container_size_3') })
      // 设置字体颜色为主色调上的字体颜色
      .fontColor($r('sys.color.font_on_primary'))
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置文本居中对齐
      .textAlign(TextAlign.Center)
      // 设置边框圆角
      .border({ radius: $r('sys.float.corner_radius_level4') })
      // 根据元素数量配置控制可见性，当元素数量为4时显示，否则隐藏
      .visibility(($$.descriptor as FlexDescriptor).elements === ElementsNums.FOUR ? Visibility.Visible :
      Visibility.None)
    // 第四个文本元素，显示数字"4"
    Text('4')
      // 设置元素尺寸，使用应用资源中定义的容器尺寸4
      .size({ width: $r('app.float.container_size_4'), height: $r('app.float.container_size_4') })
      // 设置字体颜色为主色调上的字体颜色
      .fontColor($r('sys.color.font_on_primary'))
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置文本居中对齐
      .textAlign(TextAlign.Center)
      // 设置边框圆角
      .border({ radius: $r('sys.float.corner_radius_level4') })
      // 根据元素数量配置控制可见性，当元素数量为4时显示，否则隐藏
      .visibility(($$.descriptor as FlexDescriptor).elements === ElementsNums.FOUR ? Visibility.Visible :
      Visibility.None)
  }
  // 设置Flex容器高度，使用应用资源中定义的容器高度
  .height($r('app.float.container_height'))
  // 设置Flex容器宽度，使用应用资源中定义的容器宽度
  .width($r('app.float.container_width'))
  // 设置Flex容器内边距，使用系统资源中定义的3级内边距
  .padding($r('sys.float.padding_level3'))
  // 设置Flex容器边框样式
  .border({
    // 设置边框宽度，使用详情页面常量中定义的容器边框宽度
    width: DetailPageConstant.CONTAINER_BORDER,
    // 设置边框颜色为强调背景色
    color: $r('sys.color.comp_background_emphasize'),
    // 设置边框圆角，使用系统资源中定义的6级圆角半径
    radius: $r('sys.float.corner_radius_level6'),
  })
}
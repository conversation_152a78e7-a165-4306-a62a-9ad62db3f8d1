// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';
// 导入字体粗细映射数据
import { fontWeightMapData } from '../../../common/entity/CommonMapData';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../../viewmodel/CommonDescriptor';

/**
 * 日期选择器描述器类
 * 继承通用描述器，专门用于描述日期选择器组件的配置
 * 包含农历显示、选中文本样式和普通文本样式的配置信息
 */
@Observed
export class DatePickerDescriptor extends CommonDescriptor {
  // 是否显示农历，默认为false
  public lunar: boolean = false;
  // 选中文本样式配置
  public selectedTextStyle: PickerTextStyle = {
    // 选中文本颜色，使用应用默认颜色
    color: $r('app.color.date_picker_default_color'),
    // 选中文本字体配置
    font: {
      // 字体大小为20
      size: 20,
      // 字体粗细为中等
      weight: FontWeight.Medium,
    },
  };
  // 普通文本样式配置
  public textStyle: PickerTextStyle = {
    // 普通文本颜色，使用系统主要字体颜色
    color: $r('sys.color.font_primary'),
    // 普通文本字体配置
    font: {
      // 字体大小为16
      size: 16,
      // 字体粗细为常规
      weight: FontWeight.Regular,
    },
  };

  /**
   * 转换属性方法
   * 将原始属性数组转换为日期选择器的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'lunar':
          // 设置是否显示农历
          this.lunar = JSON.parse(attribute.currentValue);
          break;
        case 'selectedTextColor':
          // 设置选中文本颜色，保持原有字体配置
          this.selectedTextStyle = {
            color: attribute.currentValue,
            font: {
              size: this.selectedTextStyle.font?.size,
              weight: this.selectedTextStyle.font?.weight,
            },
          };
          break;
        case 'selectedFontSize':
          // 设置选中文本字体大小，保持原有颜色和字体粗细
          this.selectedTextStyle = {
            color: this.selectedTextStyle.color,
            font: {
              size: Number(attribute.currentValue),
              weight: this.selectedTextStyle.font?.weight,
            },
          };
          break;
        case 'selectedFontWeight':
          // 设置选中文本字体粗细，保持原有颜色和字体大小
          this.selectedTextStyle = {
            color: this.selectedTextStyle.color,
            font: {
              size: this.selectedTextStyle.font?.size,
              weight: fontWeightMapData.get(attribute.currentValue)?.value ?? this.selectedTextStyle.font?.weight,
            },
          };
          break;
        case 'fontSize':
          // 设置普通文本字体大小，保持常规字体粗细
          this.textStyle.font = {
            size: Number(attribute.currentValue),
            weight: FontWeight.Regular,
          };
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
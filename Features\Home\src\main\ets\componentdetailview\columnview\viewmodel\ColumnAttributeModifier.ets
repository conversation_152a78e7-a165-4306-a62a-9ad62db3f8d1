// 导入通用属性修改器基类，用于继承属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入列描述器类，用于获取列组件的配置信息
import { ColumnDescriptor } from './ColumnDescriptor';

/**
 * 列属性修改器类
 * 继承通用属性修改器，用于动态修改列组件的属性
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class ColumnAttributeModifier extends CommonAttributeModifier<ColumnDescriptor, ColumnAttribute> {
  /**
   * 应用普通属性的方法
   * 将列描述器中的属性值应用到列实例上
   * @param instance 列属性实例，用于设置列组件的各种属性
   */
  public applyNormalAttribute(instance: ColumnAttribute): void {
    // 分配对齐项目属性，从描述器获取水平对齐值并应用到列实例
    this.assignAttribute((descriptor => descriptor.alignItems), (val) => instance.alignItems(val));
    // 分配弹性对齐属性，从描述器获取垂直对齐值并应用到列实例的内容对齐
    this.assignAttribute((descriptor => descriptor.flexAlign), (val) => instance.justifyContent(val));
  }
}
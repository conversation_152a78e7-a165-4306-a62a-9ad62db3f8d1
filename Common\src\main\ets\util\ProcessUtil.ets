// 导入AbilityKit包中的通用类型
import type { common } from '@kit.AbilityKit';
// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG = '[ProcessUtil]';

/**
 * 进程工具类
 * 提供应用进程管理功能
 * 包括终止能力和将能力移至后台的操作
 */
export class ProcessUtil {
  /**
   * 终止能力方法
   * 终止当前UI能力实例
   * @param context UI能力上下文
   */
  public static terminateAbility(context: common.UIAbilityContext): void {
    try {
      // 调用上下文的终止自身方法
      context.terminateSelf().then(() => {
        // 记录终止自身成功信息日志
        Logger.info(TAG, 'terminateSelf succeed');
      }).catch((err: BusinessError) => {
        // 记录终止自身失败错误日志
        Logger.error(TAG, `terminateSelf failed. Cause ${err.code}, ${err.message}.`);
      });
    } catch (error) {
      // 捕获异常并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录终止自身失败错误日志
      Logger.error(TAG, `terminateSelf failed error:${err.code}, ${err.message}.`);
    }
  }

  /**
   * 移动能力到后台方法
   * 将当前UI能力移动到后台运行
   * @param context UI能力上下文
   */
  public static moveAbilityToBackground(context: common.UIAbilityContext): void {
    try {
      // 调用上下文的移动能力到后台方法
      context.moveAbilityToBackground().then(() => {
        // 记录移动能力到后台成功信息日志
        Logger.info(TAG, 'moveAbilityToBackground succeed');
      }).catch((err: BusinessError) => {
        // 记录移动能力到后台失败错误日志
        Logger.error(TAG, `moveAbilityToBackground failed, cause ${err.code}, ${err.message}`);
      });
    } catch (error) {
      // 捕获异常并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录移动能力到后台失败错误日志
      Logger.error(TAG, `moveAbilityToBackground failed error: ${err.code}, ${err.message}`);
    }
  }
}
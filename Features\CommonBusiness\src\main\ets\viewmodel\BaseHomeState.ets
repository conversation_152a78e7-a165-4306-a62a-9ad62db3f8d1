// 导入通用模块中的基础状态类和加载模型
import { BaseState, LoadingModel } from '@ohos/common';
// 导入全屏导航数据模型
import { FullScreenNavigationData } from '../model/FullScreenNavigationData';
// 导入横幅数据源
import { BannerSource } from './BannerSource';

/**
 * 基础首页状态类
 * 继承自BaseState，管理首页相关的所有状态数据
 * 包含加载状态、导航数据、滚动状态、横幅状态等
 */
@Observed
export class BaseHomeState extends BaseState {
  // 加载模型，管理页面加载状态和分页信息
  public loadingModel: LoadingModel = new LoadingModel();
  // 顶部导航数据，包含导航栏的配置信息
  public topNavigationData: FullScreenNavigationData = new FullScreenNavigationData();
  // 当前滚动状态，默认为空闲状态
  public currentScrollState: ScrollState = ScrollState.Idle;
  // 横幅状态，管理横幅相关的数据和配置
  public bannerState: BannerState = new BannerState();
  // 横幅高度，用于布局计算
  public bannerHeight: number = 0;
  // 当前页码，用于分页加载
  public currentPage: number = 1;
  // 是否有边缘效果，控制滚动边界效果
  public hasEdgeEffect: boolean = false;

  /**
   * 构造函数
   * 调用父类构造函数进行初始化
   */
  public constructor() {
    super();
  }
}

/**
 * 横幅状态类
 * 管理横幅组件相关的状态数据
 * 包含横幅高度和数据源
 */
@Observed
export class BannerState {
  // 横幅高度，用于动态调整横幅组件的显示高度
  public bannerHeight: number = 0;
  // 横幅数据源，提供横幅数据给LazyForEach使用
  public bannerResource: BannerSource = new BannerSource();
}
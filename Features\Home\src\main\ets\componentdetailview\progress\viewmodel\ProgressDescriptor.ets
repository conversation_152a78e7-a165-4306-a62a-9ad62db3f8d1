// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入进度条相关映射数据
import {
  progressColorMapData,
  progressKindMapData,
  progressStyleMapData,
  progressTypeMapData,
  progressValueMapData,
} from '../entity/ProgressAttributeMapping';

/**
 * 进度条描述器类
 * 继承通用描述器，专门用于描述进度条组件的配置
 * 包含进度值、颜色、样式、类型和类别等配置信息
 */
@Observed
export class ProgressDescriptor extends CommonDescriptor {
  // 进度值，默认使用映射数据中的默认值
  public value: number = progressValueMapData.get('Default')!.value;
  // 进度条颜色，默认使用映射数据中的默认颜色
  public color: ResourceColor = progressColorMapData.get('Default')!.value;
  // 进度条样式，默认使用映射数据中的默认样式
  public style: CommonProgressStyleOptions = progressStyleMapData.get('Default')!.value;
  // 进度条类型，默认为线性类型
  public type: ProgressType = ProgressType.Linear;
  // 进度条类别，默认使用映射数据中的默认类别
  public kind: string = progressKindMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为进度条的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'value':
          // 设置进度值，转换为数字类型
          this.value = Number(attribute.currentValue);
          break;
        case 'kind':
          // 设置进度条类别
          this.kind = attribute.currentValue;
          break;
        case 'color':
          // 设置进度条颜色
          this.color = attribute.currentValue;
          break;
        case 'style':
          // 设置进度条样式和类型
          this.style =
            progressStyleMapData.get(attribute.currentValue)?.value ?? progressStyleMapData.get('Default')!.value;
          this.type = progressTypeMapData.get(attribute.currentValue)?.value ?? ProgressType.Linear;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
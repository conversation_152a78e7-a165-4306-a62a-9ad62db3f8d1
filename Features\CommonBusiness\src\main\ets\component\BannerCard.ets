// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入横幅数据模型类型
import type { BannerData } from '../model/BannerData';
// 导入基础首页状态中的横幅状态类型
import type { BannerState } from '../viewmodel/BaseHomeState';
// 导入横幅项组件
import { BannerItem } from './BannerItem';

// 图标高度常量
const ICON_HEIGHT = 32;
// 图标内边距常量
const ICON_PADDING = 24;
// 横幅宽高比常量
const BANNER_ASPECT = 1.75;
// 横幅指示点高度常量
const BANNER_DOT_HEIGHT = 2;
// 横幅指示点间距常量
const BANNER_DOT_SPACING = 8;

/**
 * 横幅卡片组件
 * 用于显示横幅内容的卡片组件，支持响应式布局
 * 在大屏设备上显示为水平滚动列表，在小屏设备上显示为轮播图
 */
@Component
export struct BannerCard {
  // 全局信息模型，监听断点变化
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 横幅状态数据，必需属性
  @Prop @Require bannerState: BannerState;
  // 标签视图类型，必需属性
  @Prop @Require tabViewType: number;
  // 横幅项点击处理函数，可选
  handleItemClick?: (bannerData: BannerData) => void;
  // 横幅内边距，根据断点类型设置不同值
  private bannerPadding: number = new BreakpointType({
    sm: CommonConstants.SPACE_16,
    md: CommonConstants.SPACE_24,
    lg: CommonConstants.SPACE_32
  }).getValue(this.globalInfoModel.currentBreakpoint);
  // 缓存数量，横幅资源总数
  private catchCount: number = this.bannerState.bannerResource.totalCount();
  // 轮播控制器
  private swiperController: SwiperController = new SwiperController();
  // 滚动控制器
  private scrollerController: Scroller = new Scroller();
  // 横幅偏移量，用于滚动计算
  private bannerOffset: number = this.bannerState.bannerHeight * BANNER_ASPECT;
  // 当前索引状态
  @State currentIndex: number = 0;
  // 左侧图标显示状态
  @State showLeftIcon: boolean = false;
  // 右侧图标显示状态
  @State showRightIcon: boolean = true;
  // 横幅宽度状态，根据设备宽度和间距计算
  @State bannerWidth: number =
    (this.globalInfoModel.deviceWidth - this.bannerPadding * 2 - BANNER_DOT_SPACING * (this.catchCount - 1)) /
    this.catchCount;

  /**
   * 断点变化处理方法
   * 当设备断点发生变化时，重新计算横幅内边距和宽度
   */
  handleBreakPointChange(): void {
    // 根据新的断点类型重新计算横幅内边距
    this.bannerPadding = new BreakpointType({
      sm: CommonConstants.SPACE_16,
      md: CommonConstants.SPACE_24,
      lg: CommonConstants.SPACE_32
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 如果不是大屏或超大屏断点
    if (this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.LG &&
      this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL) {
      // 重新计算横幅宽度
      this.bannerWidth =
        (this.globalInfoModel.deviceWidth - this.bannerPadding * 2 - BANNER_DOT_SPACING * (this.catchCount - 1)) /
        this.catchCount;
    }
  }

  /**
   * 构建横幅卡片组件的UI结构
   * 根据设备断点类型显示不同的布局：大屏显示水平滚动列表，小屏显示轮播图
   */
  build() {
    // 如果是大屏或超大屏断点
    if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 创建堆叠容器，左上角对齐
      Stack({ alignContent: Alignment.TopStart }) {
        // 创建水平滚动容器
        Scroll(this.scrollerController) {
          // 创建水平排列的行容器
          Row({ space: CommonConstants.SPACE_16 }) {
            // 懒加载横幅数据
            LazyForEach(this.bannerState.bannerResource, (bannerData: BannerData) => {
              // 创建列容器包装横幅项
              Column() {
                // 横幅项组件
                BannerItem({ bannerData: bannerData })
                  // 设置复用ID用于性能优化
                  .reuseId('banner_scroll')
                  // 设置几何转换动画
                  .geometryTransition(CommonConstants.BANNER_GEOMETRY + bannerData.id.toString(), { follow: true })
                  // 设置透明度转换效果
                  .transition(TransitionEffect.OPACITY)
                  // 设置高度为100%
                  .height('100%')
                  // 设置宽高比
                  .aspectRatio(BANNER_ASPECT)
                  // 设置点击事件处理
                  .onClick(() => {
                    this.handleItemClick?.(bannerData);
                  })
              }
            }, (bannerData: BannerData) => bannerData.id.toString())
          }
          // 设置子元素左对齐
          .justifyContent(FlexAlign.Start)
          // 设置内边距
          .padding({
            // 根据断点类型设置左内边距
            left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ?
              (CommonConstants.TAB_BAR_WIDTH + CommonConstants.SPACE_32) : $r('sys.float.padding_level16'),
            // 设置右内边距
            right: $r('sys.float.padding_level16'),
          })
        }
        // 设置滚动到开始位置的事件处理
        .onReachStart(() => {
          // 隐藏左侧图标
          this.showLeftIcon = false;
          // 显示右侧图标
          this.showRightIcon = true;
        })
        // 设置滚动到结束位置的事件处理
        .onReachEnd(() => {
          // 显示左侧图标
          this.showLeftIcon = true;
          // 隐藏右侧图标
          this.showRightIcon = false;
        })
        // 隐藏滚动条
        .scrollBar(BarState.Off)
        // 禁用边缘效果
        .edgeEffect(EdgeEffect.None)
        // 设置水平滚动
        .scrollable(ScrollDirection.Horizontal)
        // 设置高度为100%
        .height('100%')
        // 设置宽度为100%
        .width('100%')

        // 如果是超大屏断点，显示导航按钮
        if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
          // 左侧导航按钮
          Button({ type: ButtonType.Circle }) {
            // 左箭头符号图标
            SymbolGlyph($r('sys.symbol.chevron_left'))
              // 设置图标字体大小
              .fontSize($r('app.float.banner_icon_height'))
              // 设置图标颜色
              .fontColor([$r('sys.color.font_on_primary')])
          }
          // 设置按钮背景颜色
          .backgroundColor($r('sys.color.mask_fourth'))
          // 设置按钮高度
          .height($r('app.float.banner_button_height'))
          // 设置按钮宽度
          .width($r('app.float.banner_button_height'))
          // 设置按钮外边距
          .margin({
            // 计算顶部边距，使按钮垂直居中
            top: Math.floor((this.bannerState.bannerHeight -
              (this.globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8) -
              ICON_HEIGHT) / 2),
            // 设置左边距
            left: $r('sys.float.padding_level12')
          })
          // 根据显示状态控制按钮可见性
          .visibility(this.showLeftIcon ? Visibility.Visible : Visibility.Hidden)
          // 设置点击事件，向左滚动
          .onClick(() => {
            this.scrollByOffset(-this.bannerOffset);
          })

          // 右侧导航按钮
          Button({ type: ButtonType.Circle }) {
            // 右箭头符号图标
            SymbolGlyph($r('sys.symbol.chevron_right'))
              // 设置图标字体大小
              .fontSize($r('app.float.banner_icon_height'))
              // 设置图标颜色
              .fontColor([$r('sys.color.font_on_primary')])
          }
          // 设置按钮背景颜色
          .backgroundColor($r('sys.color.mask_fourth'))
          // 设置按钮高度
          .height($r('app.float.banner_button_height'))
          // 设置按钮宽度
          .width($r('app.float.banner_button_height'))
          // 设置按钮外边距
          .margin({
            // 计算顶部边距，使按钮垂直居中
            top: Math.floor((this.bannerState.bannerHeight -
              (this.globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8) -
              ICON_HEIGHT) / 2),
            // 计算左边距，使按钮位于右侧
            left: this.globalInfoModel.deviceWidth - CommonConstants.SIDE_BAR_WIDTH - CommonConstants.SPACE_32 * 2 -
              ICON_PADDING - ICON_HEIGHT,
          })
          // 设置按钮位置
          .position({ x: 0 })
          // 根据显示状态控制按钮可见性
          .visibility(this.showRightIcon ? Visibility.Visible : Visibility.Hidden)
          // 设置点击事件，向右滚动
          .onClick(() => {
            this.scrollByOffset(this.bannerOffset);
          })
        }
      }
      // 设置堆叠容器高度
      .height(this.bannerState.bannerHeight)
      // 设置堆叠容器宽度为100%
      .width('100%')
      // 设置堆叠容器内边距
      .padding({
        // 设置顶部内边距，包含状态栏高度、导航高度和额外间距
        top: this.globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8,
      })
    } else {
      // 小屏设备使用轮播图布局
      Swiper(this.swiperController) {
        // 懒加载横幅数据
        LazyForEach(this.bannerState.bannerResource, (bannerData: BannerData) => {
          // 横幅项组件
          BannerItem({ bannerData: bannerData })
            // 设置复用ID用于性能优化
            .reuseId('banner_swiper')
            // 设置点击事件处理
            .onClick(() => {
              this.handleItemClick?.(bannerData);
            })
        }, (bannerData: BannerData) => bannerData.id.toString())
      }
      // 设置轮播图索引变化事件
      .onChange((index: number) => {
        // 更新当前索引
        this.currentIndex = index;
      })
      // 设置缓存数量
      .cachedCount(this.catchCount)
      // 设置项间距为0
      .itemSpace(0)
      // 启用循环播放
      .loop(true)
      // 当有多个项时启用自动播放
      .autoPlay(this.catchCount > 1)
      // 禁用边缘效果
      .effectMode(EdgeEffect.None)
      // 设置指示器，当有多个项时显示
      .indicator(this.catchCount > 1 ?
      new DotIndicator()
        // 设置指示器项宽度
        .itemWidth(this.bannerWidth)
        // 设置指示器项高度
        .itemHeight(BANNER_DOT_HEIGHT)
        // 设置指示器颜色
        .color($r('sys.color.icon_on_tertiary'))
        // 设置选中指示器项宽度
        .selectedItemWidth(this.bannerWidth)
        // 设置选中指示器项高度
        .selectedItemHeight(BANNER_DOT_HEIGHT)
        // 设置选中指示器颜色
        .selectedColor($r('sys.color.icon_on_primary')) :
        false)
      // 设置轮播图宽度为100%
      .width('100%')
      // 设置轮播图高度
      .height(this.bannerState.bannerHeight)
      // 设置几何转换动画
      .geometryTransition(CommonConstants.BANNER_GEOMETRY + this.tabViewType.toString(), { follow: true })
      // 设置透明度转换效果
      .transition(TransitionEffect.OPACITY)
    }
  }

  /**
   * 按偏移量滚动方法
   * 用于控制水平滚动容器的滚动位置
   * @param offset 滚动偏移量，正值向右滚动，负值向左滚动
   */
  private scrollByOffset(offset: number): void {
    // 获取当前水平偏移量
    const currentOffset = this.scrollerController.currentOffset().xOffset;
    // 滚动到新位置
    this.scrollerController.scrollTo({
      // 设置新的水平偏移量
      xOffset: currentOffset + offset,
      // 垂直偏移量保持为0
      yOffset: 0,
      // 设置滚动动画
      animation: {
        // 动画持续时间200毫秒
        duration: 200,
      }
    });
  }
}
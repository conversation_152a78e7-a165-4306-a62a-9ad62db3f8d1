// 导入开关组件属性类型定义
import type { ToggleComAttribute } from '../viewmodel/ComponentDetailState';

// 使用Component装饰器定义开关公共组件
@Component
export struct ToggleComponent {
  // 使用ObjectLink装饰器链接属性对象
  @ObjectLink attribute: ToggleComAttribute;
  // 定义回调函数
  callback: (name: string, value: string) => void = (name: string, value: string) => {
  };

  // 定义构建方法
  build() {
    // 创建行布局
    Row() {
      // 显示属性显示名称文本
      Text(this.attribute.disPlayName)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_primary'))
      // 添加空白填充
      Blank()
      // 创建开关组件
      Toggle({ type: ToggleType.Switch, isOn: JSON.parse(this.attribute.currentValue) })
        // 设置选中颜色
        .selectedColor($r('sys.color.comp_background_emphasize'))
        // 设置状态变化回调
        .onChange((isOn: boolean) => {
          this.callback(this.attribute.name, String(isOn));
        })
    }
    // 设置行高度
    .height($r('app.float.common_component_height'))
    // 设置左右内边距
    .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
    // 设置垂直对齐方式为居中
    .alignItems(VerticalAlign.Center)
    // 设置宽度为100%
    .width('100%')
  }
}
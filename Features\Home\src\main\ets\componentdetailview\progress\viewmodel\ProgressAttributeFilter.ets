// 导入可观察数组类型，用于响应式数据处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 进度条属性过滤器类
 * 实现通用属性过滤器接口，用于根据进度条类型过滤相关属性
 * 当进度条类型为LoadingProgress时，隐藏value和style属性
 */
export class ProgressAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据进度条类型控制相关属性的启用状态
   * @param attributes 属性数组，包含所有组件属性
   */
  filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，根据属性名称进行过滤处理
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'kind':
          // 查找value属性的索引位置
          const valueIndex = attributes.findIndex((item) => item.name === 'value');
          // 查找style属性的索引位置
          const styleIndex = attributes.findIndex((item) => item.name === 'style');
          // 如果找到了value和style属性
          if (valueIndex !== -1 && styleIndex !== -1) {
            // 如果进度条类型为Progress，启用value和style属性
            if (attribute.currentValue === 'Progress') {
              attributes[valueIndex].enable = true;
              attributes[styleIndex].enable = true;
            } else {
              // 如果进度条类型为LoadingProgress，禁用value和style属性
              attributes[valueIndex].enable = false;
              attributes[styleIndex].enable = false;
            }
          }
          break;
        default:
          // 其他属性不做处理
          break;
      }
    });
  }
}
// 导入详情页常量，包含容器边框等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入堆叠布局属性修改器，用于修改组件属性
import { StackAttributeModifier } from '../viewmodel/StackAttributeModifier';
// 导入堆叠布局描述器类型，用于描述组件配置
import type { StackDescriptor } from '../viewmodel/StackDescriptor';

/**
 * 堆叠布局构建器函数
 * 用于构建堆叠布局组件，包含两个重叠的列子组件
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function StackBuilder($$: DescriptorWrapper) {
  // 创建堆叠布局容器
  Stack() {
    // 创建第一个列子组件（背景层）
    Column()
      // 设置第一个列的尺寸（较大）
      .size({ width: $r('app.float.container_size_5'), height: $r('app.float.container_size_5') })
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
    // 创建第二个列子组件（前景层）
    Column()
      // 设置第二个列的尺寸（较小）
      .size({ width: $r('app.float.container_size_3'), height: $r('app.float.container_size_3') })
      // 设置背景颜色为多色03
      .backgroundColor($r('sys.color.multi_color_03'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
  }
  // 设置堆叠布局内边距
  .padding($r('sys.float.padding_level3'))
  // 设置堆叠布局高度
  .height($r('app.float.container_height'))
  // 设置堆叠布局宽度
  .width($r('app.float.container_width'))
  // 设置堆叠布局边框样式
  .border({
    width: DetailPageConstant.CONTAINER_BORDER,
    color: $r('sys.color.comp_background_emphasize'),
    radius: $r('sys.float.corner_radius_level6'),
  })
  // 设置属性修改器，用于动态修改组件属性
  .attributeModifier(new StackAttributeModifier($$.descriptor as StackDescriptor))
}
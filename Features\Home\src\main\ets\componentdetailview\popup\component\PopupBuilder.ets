// 导入弹窗组件，用于创建弹窗功能
import { Popup } from '@kit.ArkUI';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入弹窗按钮文本映射数据和弹窗样式枚举
import { popupBtnTextCodeMapData, PopupStyle } from '../entity/PopupMapping';
// 导入弹窗描述器类型，用于描述组件配置
import type { PopupDescriptor } from '../viewmodel/PopupDescriptor';

/**
 * 弹窗构建器函数
 * 用于构建弹窗组件，提供多种弹窗样式
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function PopupBuilder($$: DescriptorWrapper) {
  // 创建弹窗组件实例，传入描述器配置
  PopupComponent({ popupDescriptor: $$.descriptor as PopupDescriptor })
}

/**
 * 弹窗组件结构体
 * 提供按钮、文本、图标等多种弹窗样式
 */
@Component
struct PopupComponent {
  // 弹窗描述器属性，包含组件配置信息
  @Prop popupDescriptor: PopupDescriptor;
  // 弹窗显示状态，控制弹窗的显示和隐藏
  @State handlePopup: boolean = false;

  /**
   * 弹窗内容构建器方法
   * 根据弹窗样式类型构建不同的弹窗内容
   * @param type 弹窗样式类型
   */
  @Builder
  popupWithButtonBuilder(type: PopupStyle) {
    // 创建行布局容器
    Row() {
      // 根据弹窗样式类型显示不同的弹窗内容
      if (type === PopupStyle.STYLE_BUTTON) {
        // 显示带按钮的弹窗
        this.popupButton();
      } else if (type === PopupStyle.STYLE_TEXT) {
        // 显示纯文本弹窗
        this.popupText();
      } else if (type === PopupStyle.STYLE_ICON) {
        // 显示带图标的弹窗
        this.popupIcon();
      }
    }
    // 设置按键预处理事件，处理方向键导航
    .onKeyPreIme((keyEvent: KeyEvent) => {
      // 检查是否为左右方向键按下事件
      if ((keyEvent?.keyText === 'KEYCODE_DPAD_RIGHT' || keyEvent?.keyText === 'KEYCODE_DPAD_LEFT') &&
        keyEvent.type === KeyType.Down) {
        // 拦截按键事件
        return true;
      }
      // 不拦截其他按键事件
      return false;
    })
  }

  /**
   * 带按钮弹窗构建器方法
   * 创建包含标题、消息和确认按钮的弹窗
   */
  @Builder
  popupButton() {
    // 创建带按钮的弹窗组件
    Popup({
      // 设置弹窗标题
      title: {
        text: $r('app.string.title'),
      },
      // 设置弹窗消息内容
      message: {
        text: $r('app.string.popup_button_message'),
      },
      // 显示关闭按钮
      showClose: true,
      // 设置关闭回调，隐藏弹窗
      onClose: () => {
        this.handlePopup = false;
      },
      // 设置弹窗按钮
      buttons: [
        {
          // 确认按钮文本
          text: $r('app.string.confirmTip'),
          // 按钮点击回调，隐藏弹窗
          action: () => {
            this.handlePopup = false;
          },
        },
      ],
    })
  }

  /**
   * 纯文本弹窗构建器方法
   * 创建只包含消息文本的简单弹窗
   */
  @Builder
  popupText() {
    // 创建纯文本弹窗组件
    Popup({
      // 设置弹窗消息内容
      message: {
        text: $r('app.string.popup_text_message')
      },
      // 显示关闭按钮
      showClose: true,
      // 设置关闭回调，隐藏弹窗
      onClose: () => {
        this.handlePopup = false;
      },
    })
  }

  /**
   * 带图标弹窗构建器方法
   * 创建包含图标、标题和消息的弹窗
   */
  @Builder
  popupIcon() {
    // 创建带图标的弹窗组件
    Popup({
      // 设置弹窗图标
      icon: {
        image: $r('app.media.startIcon')
      },
      // 设置弹窗标题
      title: {
        text: $r('app.string.title'),
      },
      // 设置弹窗消息内容
      message: {
        text: $r('app.string.popup_icon_message')
      },
      // 显示关闭按钮
      showClose: true,
      // 设置关闭回调，隐藏弹窗
      onClose: () => {
        this.handlePopup = false;
      },
    })
  }

  /**
   * 构建组件UI方法
   * 创建触发弹窗的按钮和弹窗绑定
   */
  build() {
    // 创建列布局容器
    Column() {
      // 创建触发弹窗的按钮
      Button(popupBtnTextCodeMapData.get(this.popupDescriptor.type)!)
        // 设置按钮背景颜色为次要背景色
        .backgroundColor($r('sys.color.background_secondary'))
        // 设置按钮字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮点击事件，显示弹窗
        .onClick(() => {
          this.handlePopup = true;
        })
        // 绑定弹窗到按钮
        .bindPopup(this.handlePopup, {
          // 设置弹窗内容构建器
          builder: this.popupWithButtonBuilder(this.popupDescriptor.type),
          // 设置弹窗位置
          placement: this.popupDescriptor.placement,
          // 设置弹窗可获取焦点
          focusable: true,
          // 根据弹窗类型设置宽度，文本类型使用默认宽度
          width: this.popupDescriptor.type === PopupStyle.STYLE_TEXT ? undefined : $r('app.float.popup_width_large'),
          // 设置弹窗状态变化回调
          onStateChange: (e) => {
            // 当弹窗不可见时，更新状态
            if (!e.isVisible) {
              this.handlePopup = false;
            }
          },
        })
    }
    // 设置列布局宽度为100%
    .width('100%')
    // 设置列布局高度为100%
    .height('100%')
    // 设置列布局垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
  }
}
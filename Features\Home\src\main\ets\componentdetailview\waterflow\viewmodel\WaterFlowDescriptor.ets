// 导入字符串工具类，用于字符串处理
import { StringUtil } from '../../../util/StringUtil';
// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入瀑布流相关的属性映射数据
import {
  columnsGapMapData,
  columnsTemplateMapData,
  frictionMapData,
  layoutDirectionMapData,
  rowsTemplateMapData,
} from '../entity/WaterFlowAttributeMapping';

/**
 * 瀑布流描述器类
 * 继承自通用描述器，用于描述瀑布流组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class WaterFlowDescriptor extends CommonDescriptor {
  // 布局方向属性，默认使用默认值
  public layoutDirection: FlexDirection = layoutDirectionMapData.get('Default')!.value as FlexDirection;
  // 摩擦力属性，默认使用默认值
  public friction: number = frictionMapData.get('Default')!.value as number;
  public columnsTemplate: string = columnsTemplateMapData.get('Default')!.value as string;
  public rowsTemplate: string = rowsTemplateMapData.get('Default')!.value as string;
  public columnsGap: number = columnsGapMapData.get('Default')!.value as number;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'layoutDirection':
          this.layoutDirection = layoutDirectionMapData.get(attribute.currentValue)?.value as FlexDirection ??
            layoutDirectionMapData.get('Default')!.value as FlexDirection;
          break;
        case 'friction':
          this.friction =
            frictionMapData.get(attribute.currentValue)?.value as number ??
              frictionMapData.get('Default')!.value as number;
          break;
        case 'columnsTemplate':
          this.columnsTemplate =
            StringUtil.getTemplateString(Number(attribute.currentValue)) ??
              columnsTemplateMapData.get('Default')!.value as string;
          break;
        case 'rowsTemplate':
          this.rowsTemplate =
            StringUtil.getTemplateString(Number(attribute.currentValue)) ?? rowsTemplateMapData.get('Default')!.value as string;
          break;
        case 'columnsGap':
          this.columnsGap = Number(attribute.currentValue) ?? columnsGapMapData.get('Default')!.value as number;
          break;
        default:
          break;
      }
    });
  }
}
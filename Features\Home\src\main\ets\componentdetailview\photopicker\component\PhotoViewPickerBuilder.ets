// 导入业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入照片访问助手，用于实现照片选择功能
import { photoAccessHelper } from '@kit.MediaLibraryKit';
// 导入日志工具，用于记录操作信息
import { Logger } from '@ohos/common';
// 导入详情页面常量，用于获取配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

// 日志标签，用于标识照片选择器构建器的日志
const TAG = '[PhotoViewPickerBuilder]';

/**
 * 照片视图选择器构建器函数
 * 用于构建照片选择功能的组件
 * @param _$$ 描述器包装对象（此处未使用）
 */
@Builder
export function PhotoViewPickerBuilder(_$$: DescriptorWrapper) {
  // 创建照片视图选择器组件实例
  PhotoViewPickerComponent()
}

/**
 * 照片视图选择器组件结构体
 * 提供照片选择和显示功能，支持背景模糊效果
 */
@Component
struct PhotoViewPickerComponent {
  // 图片URI状态，存储单个图片的URI
  @State imageUri: string = '';
  // 图片URI数组状态，存储多个图片的URI
  @State imgUris: string[] = [];
  // 选中的图片URI状态，用于显示选中的图片
  @State selectedImageUri: ResourceStr = '';
  // 背景模糊状态，控制按钮的背景模糊效果
  @State backBlur: boolean = false;

  /**
   * 构建组件UI方法
   * 创建包含图片显示和选择按钮的界面
   */
  build() {
    // 创建堆叠布局，按钮居中显示
    Stack({ alignContent: Alignment.Center }) {
      // 创建图片组件显示选中的照片
      Image(this.selectedImageUri)
        // 设置图片适应方式为包含
        .objectFit(ImageFit.Contain)
        // 设置圆角边框
        .borderRadius($r('sys.float.corner_radius_level5'))
        // 设置图片尺寸
        .width('100%')
        .height('100%')

      // 创建照片选择按钮
      Button($r('app.string.photoViewPicker_text'))
        // 设置按钮样式为普通模式
        .buttonStyle(ButtonStyleMode.NORMAL)
        // 根据背景模糊状态设置背景模糊样式
        .backgroundBlurStyle(this.backBlur ? BlurStyle.BACKGROUND_THICK : BlurStyle.NONE,
          {
            colorMode: ThemeColorMode.SYSTEM,
            adaptiveColor: AdaptiveColor.DEFAULT,
            scale: DetailPageConstant.SCALE_LEVEL1,
          })
        // 根据背景模糊状态设置字体颜色
        .fontColor(this.backBlur ? $r('sys.color.font_on_primary') : $r('sys.color.font_emphasize'))
        // 设置按钮高度
        .height($r('app.float.button_height_normal'))
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 设置点击事件处理，启动照片选择器
        .onClick(() => {
          try {
            // 创建照片选择选项配置
            const photoSelectOptions = new photoAccessHelper.PhotoSelectOptions();
            // 设置MIME类型为图片类型
            photoSelectOptions.MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE;
            // 设置最大选择数量为1
            photoSelectOptions.maxSelectNumber = 1;
            // 创建照片视图选择器实例
            const photoPicker = new photoAccessHelper.PhotoViewPicker();
            // 调用选择方法并处理结果
            photoPicker.select(photoSelectOptions).then((photoSelectResult: photoAccessHelper.PhotoSelectResult) => {
              // 获取选择的照片URI数组
              this.imgUris = photoSelectResult.photoUris;
              // 设置单个图片URI
              this.imageUri = this.imgUris[0];
              // 设置选中的图片URI用于显示
              this.selectedImageUri = this.imgUris[0];
              // 如果选择了图片，启用背景模糊效果
              if (this.selectedImageUri.length > 0) {
                this.backBlur = true;
              }
              // 记录成功日志
              Logger.info(TAG, `PhotoViewPicker.select successfully}`);
            }).catch((err: BusinessError) => {
              // 捕获并记录选择失败错误
              Logger.error(TAG, `PhotoViewPicker.select failed with err: ${err.code}, ${err.message}`);
            });
          } catch (error) {
            // 捕获并记录其他错误
            const err: BusinessError = error as BusinessError;
            Logger.error(TAG, `PhotoViewPicker failed with err: ${err.code}, ${err.message}`);
          }
        })
    }
    // 设置堆叠容器宽度
    .width('100%')
  }
}
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入弹窗位置映射数据、弹窗样式枚举和样式映射数据
import { placementMapData, PopupStyle, popupStyleMapData } from '../entity/PopupMapping';

/**
 * 弹窗描述器类
 * 继承通用描述器，专门用于描述弹窗组件的配置
 * 包含弹窗位置和样式类型的配置信息
 */
@Observed
export class PopupDescriptor extends CommonDescriptor {
  // 弹窗位置，默认为底部位置
  public placement: Placement = placementMapData.get('Default')!.value;
  // 弹窗样式类型，默认为带按钮样式
  public type: PopupStyle = popupStyleMapData.get('Default')!;

  /**
   * 转换属性方法
   * 将原始属性数组转换为弹窗的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'type':
          // 设置弹窗样式类型
          this.type = popupStyleMapData.get(attribute.currentValue) ?? this.type;
          break;
        case 'placement':
          // 设置弹窗位置
          this.placement = placementMapData.get(attribute.currentValue)?.value ?? this.placement;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
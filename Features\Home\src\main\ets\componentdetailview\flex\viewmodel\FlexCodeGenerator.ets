// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入Flex属性映射相关的枚举和映射数据
import {
  ElementsNums,
  flexAlignItemMapData,
  flexContentMapData,
  flexDirectionMapData,
  flexWrapMapData,
} from '../entity/FlexAttributeMapping';

/**
 * Flex代码生成器类
 * 实现通用代码生成器接口，用于生成Flex布局相关的代码
 */
export class FlexCodeGenerator implements CommonCodeGenerator {
  // Flex方向代码字符串，默认使用水平从左到右
  private direction: string = flexDirectionMapData.get('Default')!.code;
  // Flex换行代码字符串，默认使用不换行
  private wrap: string = flexWrapMapData.get('Default')!.code;
  // 主轴对齐代码字符串，默认使用起始对齐
  private justifyContent: string = flexContentMapData.get('Default')!.code;
  // 交叉轴项目对齐代码字符串，默认使用自动对齐
  private alignItems: string = flexAlignItemMapData.get('Default')!.code;
  // 单个项目自身对齐代码字符串，默认使用自动对齐
  private alignSelf: string = flexAlignItemMapData.get('Default')!.code;
  // 多行内容对齐代码字符串，默认使用起始对齐
  private alignContent: string = flexContentMapData.get('Default')!.code;

  /**
   * 生成Flex布局代码方法
   * 根据传入的属性数组生成完整的Flex组件代码字符串
   * @param attributes 原始属性数组，包含所有Flex布局的配置信息
   * @returns 生成的Flex组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 初始化代码片段变量，用于存储额外的子元素代码
    let codeFragment = '';
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理元素数量属性
        case 'elements':
          // 如果元素数量为4，则生成第3和第4个文本元素的代码
          codeFragment = attribute.currentValue === ElementsNums.FOUR ? `
      Text('3')
        .width('64vp')
        .height('64vp')
        .fontColor($r('sys.color.font_on_primary'))
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .textAlign(TextAlign.Center)
        .border({ radius: $r('sys.float.corner_radius_level4') })
      Text('4')
        .width('76vp')
        .height('76vp')
        .fontColor($r('sys.color.font_on_primary'))
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .textAlign(TextAlign.Center)
        .border({ radius: $r('sys.float.corner_radius_level4') })` : ``;
          break;
        // 处理Flex方向属性
        case 'direction':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.direction =
            flexDirectionMapData.get(attribute.currentValue)?.code ?? flexDirectionMapData.get('Default')!.code;
          break;
        // 处理Flex换行属性
        case 'wrap':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.wrap = flexWrapMapData.get(attribute.currentValue)?.code ?? flexWrapMapData.get('Default')!.code;
          break;
        // 处理主轴对齐属性
        case 'justifyContent':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.justifyContent =
            flexContentMapData.get(attribute.currentValue)?.code ?? flexContentMapData.get('Default')!.code;
          break;
        // 处理交叉轴项目对齐属性
        case 'alignItems':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.alignItems =
            flexAlignItemMapData.get(attribute.currentValue)?.code ?? flexAlignItemMapData.get('Default')!.code;
          break;
        // 处理多行内容对齐属性
        case 'alignContent':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.alignContent =
            flexContentMapData.get(attribute.currentValue)?.code ?? flexContentMapData.get('Default')!.code;
          break;
        // 处理单个项目自身对齐属性
        case 'alignSelf':
          // 从映射数据中获取对应的代码字符串，如果找不到则使用默认值
          this.alignSelf =
            flexAlignItemMapData.get(attribute.currentValue)?.code ?? flexAlignItemMapData.get('Default')!.code;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    return `import { LengthMetrics } from '@kit.ArkUI';

@Component
struct FlexComponent {
  build() {
    Flex({
      space: { main: LengthMetrics.vp(6), cross: LengthMetrics.vp(6) },
      direction: ${this.direction},
      wrap: ${this.wrap},
      justifyContent: ${this.justifyContent},
      alignItems: ${this.alignItems},
      alignContent: ${this.alignContent}
    }) {
      Text('1')
        .width('40vp')
        .height('40vp')
        .fontColor($r('sys.color.font_on_primary'))
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .textAlign(TextAlign.Center)
        .border({ radius: $r('sys.float.corner_radius_level4') })
      Text('2')
        .width('52vp')
        .height('52vp')
        .fontColor($r('sys.color.font_on_primary'))
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .textAlign(TextAlign.Center)
        .border({  radius: $r('sys.float.corner_radius_level4') })
        .alignSelf(${this.alignSelf})${codeFragment}
    }
    .height('180vp')
    .width('262vp')
    .padding($r('sys.float.padding_level3'))
    .border({ width: 1, color: $r('sys.color.comp_background_emphasize'), radius: $r('sys.float.corner_radius_level6') })
  }
}`;
  }
}
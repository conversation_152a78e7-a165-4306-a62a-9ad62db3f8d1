// 导入能力工具包中的Want类型
import type { Want } from '@kit.AbilityKit';
// 导入表单工具包中的表单绑定数据、表单扩展能力、表单信息
import { formBindingData, FormExtensionAbility, formInfo } from '@kit.FormKit';

/**
 * 手机表单能力类
 * 继承自FormExtensionAbility，提供桌面小组件功能
 * 负责处理小组件的生命周期管理和事件响应
 */
export default class PhoneFormAbility extends FormExtensionAbility {
  /**
   * 添加表单时的回调方法
   * 当用户添加小组件到桌面时被调用
   * @param want 启动意图，包含表单相关信息
   * @returns 表单绑定数据对象
   */
  onAddForm(want: Want) {
    // 创建表单数据（当前为空字符串）
    const formData = '';
    // 返回表单绑定数据对象
    return formBindingData.createFormBindingData(formData);
  }

  /**
   * 临时表单转换为正常表单时的回调方法
   * 当临时表单成功转换为正常表单时被调用
   * @param formId 表单唯一标识ID
   */
  onCastToNormalForm(formId: string) {
    // 表单提供方收到临时表单成功转换为正常表单的通知时调用
  }

  /**
   * 更新表单时的回调方法
   * 当需要更新指定表单时被调用
   * @param formId 表单唯一标识ID
   */
  onUpdateForm(formId: string) {
    // 通知表单提供方更新指定表单时调用
  }

  /**
   * 表单事件处理方法
   * 当表单提供方定义的指定消息事件被触发时调用
   * @param formId 表单唯一标识ID
   * @param message 事件消息内容
   */
  onFormEvent(formId: string, message: string) {
    // 表单提供方定义的指定消息事件被触发时调用
  }

  /**
   * 移除表单时的回调方法
   * 当指定表单被销毁时被调用
   * @param formId 表单唯一标识ID
   */
  onRemoveForm(formId: string) {
    // 通知表单提供方指定表单已被销毁时调用
  }

  /**
   * 获取表单状态的方法
   * 返回表单状态对象
   * @param want 启动意图，包含表单相关信息
   * @returns 表单状态，返回READY表示表单已准备就绪
   */
  onAcquireFormState(want: Want) {
    // 返回表单状态对象，READY表示表单已准备就绪
    return formInfo.FormState.READY;
  }
};
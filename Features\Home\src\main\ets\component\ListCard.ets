// 导入通用模块中的全局信息模型类型定义
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型和通用常量
import { BreakpointType, CommonConstants } from '@ohos/common';
// 导入组件卡片数据和组件内容类型定义
import type { ComponentCardData, ComponentContent } from '../model/ComponentData';
// 导入组件项组件
import { ComponentItem } from './ComponentItem';

// 使用Reusable装饰器标记组件可复用
@Reusable
// 使用Component装饰器定义列表卡片组件
@Component
export struct ListCard {
  // 使用StorageProp装饰器定义全局信息模型存储属性
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义组件卡片数据状态
  @State componentCardData?: ComponentCardData = undefined;
  // 定义处理项目点击的回调函数
  handleItemClick?: (componentContent: ComponentContent) => void;

  // 定义组件即将复用时的回调方法
  aboutToReuse(params: Record<string, Object>): void {
    // 更新组件卡片数据
    this.componentCardData = params.componentCardData as ComponentCardData;
    // 更新处理项目点击的回调函数
    this.handleItemClick = params.handleItemClick as (componentContent: ComponentContent) => void;
  }

  // 定义组件构建方法
  build() {
    // 创建垂直排列的列容器
    Column() {
      // 创建垂直排列的列容器用于标题区域
      Column() {
        // 创建文本组件显示卡片副标题
        Text(this.componentCardData?.cardSubTitle)
          // 设置字体大小
          .fontSize($r('sys.float.Body_S'))
          // 设置字体颜色
          .fontColor($r('sys.color.font_secondary'))
          // 设置字体粗细
          .fontWeight(FontWeight.Regular)
          // 设置外边距
          .margin({ left: $r('sys.float.padding_level8'), bottom: $r('sys.float.padding_level2') })
        // 创建文本组件显示卡片标题
        Text(this.componentCardData?.cardTitle)
          // 设置字体大小
          .fontSize($r('sys.float.Title_M'))
          // 设置字体颜色
          .fontColor($r('sys.color.font_primary'))
          // 设置字体粗细
          .fontWeight(FontWeight.Bold)
          // 设置外边距
          .margin({ left: $r('sys.float.padding_level8'), bottom: $r('sys.float.padding_level4') })
          // 设置文本溢出处理
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置最大行数
          .maxLines(1)
      }
      // 设置水平对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
      // 设置高度
      .height(CommonConstants.NAVIGATION_HEIGHT)

      // 使用Repeat组件遍历卡片内容
      Repeat(this.componentCardData?.cardContents)
        // 定义每个重复项的构建方法
        .each((repeatItem: RepeatItem<ComponentContent>) => {
          // 创建组件项组件
          ComponentItem({
            componentContent: repeatItem.item,
            showDivider: repeatItem.index !== 0,
          })
            // 设置点击事件处理
            .onClick(() => {
              // 调用处理项目点击的回调函数
              this.handleItemClick?.(repeatItem.item);
            })
        })
        // 设置重复项的键值
        .key((componentContent: ComponentContent) => componentContent.id.toString())
    }
    // 设置背景颜色
    .backgroundColor($r('sys.color.comp_background_primary'))
    // 设置边框样式
    .border({
      width: $r('sys.float.border_none'),
      color: $r('sys.color.comp_background_list_card'),
      radius: $r('sys.float.corner_radius_level8'),
    })
    // 设置点击效果级别
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置水平对齐方式为左对齐
    .alignItems(HorizontalAlign.Start)
    // 设置裁剪
    .clip(true)
    // 设置内边距
    .padding({
      top: $r('sys.float.padding_level8'),
      bottom: new BreakpointType({
        sm: $r('sys.float.padding_level2'),
        md: $r('sys.float.padding_level4'),
        lg: $r('sys.float.padding_level4')
      }).getValue(this.globalInfoModel.currentBreakpoint),
    })
  }
}
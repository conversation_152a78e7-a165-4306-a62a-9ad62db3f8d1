// 导入通用常量，用于获取间距等常量值
import { CommonConstants } from '@ohos/common';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

/**
 * 列表项头部构建器函数
 * 用于构建列表项的头部标题组件，提供统一的头部样式
 * @param text 头部显示的文本内容
 * @param $$ 描述器包装对象，包含组件的配置信息
 */
@Builder
export function itemHead(text: string, $$: DescriptorWrapper) {
  // 创建行布局容器
  Row() {
    // 创建文本组件显示头部标题
    Text(text)
      // 设置文本居中对齐
      .textAlign(TextAlign.Center)
      // 设置字体大小为系统大号字体
      .fontSize($r('sys.float.Body_L'))
      // 设置背景色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置字体颜色为主色调上的字体色
      .fontColor($r('sys.color.font_on_primary'))
      // 设置宽度为100%
      .width('100%')
      // 设置高度为应用定义的大号文本高度
      .height($r('app.float.text_height_large'))
      // 设置底部外边距
      .margin({ bottom: $r('sys.float.padding_level4') })
  }
  // 设置行容器的左右负边距，用于扩展显示区域
  .margin({ left: -CommonConstants.SPACE_8, right: -CommonConstants.SPACE_8 })
}
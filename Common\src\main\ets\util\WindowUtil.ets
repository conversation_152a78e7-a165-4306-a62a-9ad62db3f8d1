// 导入AbilityKit包中的通用类型
import type { common } from '@kit.AbilityKit';
// 导入AbilityKit包中的能力常量
import { AbilityConstant } from '@kit.AbilityKit';
// 导入ArkUI包中的显示和窗口模块
import { display, window } from '@kit.ArkUI';
// 导入基础服务包中的业务错误和设备信息
import { BusinessError, deviceInfo } from '@kit.BasicServicesKit';
// 导入通用常量
import { CommonConstants } from '../constant/CommonConstants';
// 导入产品系列枚举
import { ProductSeriesEnum } from '../constant/CommonEnums';
// 导入全局信息模型
import { GlobalInfoModel } from '../model/GlobalInfoModel';
// 导入断点系统
import { BreakpointSystem } from './BreakpointSystem';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG: string = '[WindowUtil]';

/**
 * 窗口工具类
 * 提供窗口管理相关的静态方法
 * 包含状态栏设置、全屏模式、窗口方向等功能
 */
export class WindowUtil {
  /**
   * 更新状态栏颜色方法
   * 根据主题模式设置状态栏内容颜色
   * @param context 基础上下文
   * @param isDark 是否为暗色主题
   */
  public static updateStatusBarColor(context: common.BaseContext, isDark: boolean): void {
    // 获取最后一个窗口
    window.getLastWindow(context).then((windowClass: window.Window) => {
      try {
        // 设置窗口系统栏属性
        windowClass.setWindowSystemBarProperties({
          // 根据主题设置状态栏内容颜色
          statusBarContentColor: isDark ? StatusBarColorType.WHITE : StatusBarColorType.BLACK
        }).then(() => {
          // 记录设置成功信息日志
          Logger.info(TAG, 'Succeeded in setting the system bar properties.');
        }).catch((err: BusinessError) => {
          // 记录设置失败错误日志
          Logger.error(TAG, `Failed to set the system bar properties. Cause: ${err.code} ${err.message}`);
        });
      } catch (error) {
        // 捕获异常并转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录设置系统栏属性失败错误日志
        Logger.error(TAG, `Failed to set the system bar properties. Cause: ${err.code}, ${err.message}`);
      }
    }).catch((error: BusinessError) => {
      // 记录获取最后窗口失败错误日志
      Logger.error(TAG, `GetLastWindow failed. code: ${error.code}, message: ${error.message}`);
    });
  }

  /**
   * 隐藏标题栏方法
   * 设置窗口装饰为不可见并设置导航高度
   * @param windowStage 窗口舞台对象
   */
  public static hideTitleBar(windowStage: window.WindowStage) {
    // 获取主窗口
    windowStage.getMainWindow().then((data: window.Window) => {
      try {
        // 检查是否支持窗口会话管理器能力
        if (canIUse('SystemCapability.Window.SessionManager')) {
          // 设置窗口装饰为不可见
          data.setWindowDecorVisible(false);
          // 设置窗口装饰高度
          data.setWindowDecorHeight(CommonConstants.NAVIGATION_HEIGHT);
        } else {
          // 记录不支持setWindowDecorVisible的错误日志
          Logger.error(TAG, `setWindowDecorVisible invalid`);
        }
      } catch (error) {
        // 捕获异常并转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录设置窗口装饰可见性失败错误日志
        Logger.error(TAG, `Failed to set the visibility of window decor. Cause: ${err.code}, ${err.message}`);
      }
    }).catch((err: BusinessError) => {
      // 记录获取主窗口失败错误日志
      Logger.error(TAG, `Failed to obtain the main window. Cause: ${err.code}, ${err.message}`);
    })
  }

  /**
   * 请求全屏模式方法
   * 设置窗口为全屏布局并实现沉浸式效果
   * @param windowStage 窗口舞台对象
   * @param context 上下文对象
   */
  public static requestFullScreen(windowStage: window.WindowStage, context: Context): void {
    // 获取主窗口
    windowStage.getMainWindow((err: BusinessError, data: window.Window) => {
      // 如果获取窗口失败
      if (err.code) {
        // 记录获取主窗口失败错误日志
        Logger.error(TAG, `Failed to obtain the main window. Cause: ${err.code}, ${err.message}`);
        return;
      }
      // 记录获取主窗口成功调试日志
      Logger.debug(TAG, `Succeeded in obtaining the main window. Data: ${JSON.stringify(data)}`);
      // 获取窗口类对象
      const windowClass: window.Window = data;
      // 实现沉浸式效果
      try {
        // 如果是可折叠PC设备
        if (deviceInfo.productSeries === ProductSeriesEnum.HPR) {
          // 重置窗口尺寸
          WindowUtil.resetWindowSize(windowClass);
        }
        // 设置窗口布局为全屏模式
        const promise: Promise<void> = windowClass.setWindowLayoutFullScreen(true);
        promise.then(() => {
          // 记录设置全屏模式成功信息日志
          Logger.info(TAG, 'Succeeded in setting the window layout to full-screen mode.');
        }).catch((err: BusinessError) => {
          // 记录设置全屏模式失败错误日志
          Logger.error(TAG,
            `Failed to set the window layout to full-screen mode. Cause: ${err.code}, ${err.message}`);
        });
        // 获取设备尺寸
        WindowUtil.getDeviceSize(context);
      } catch {
        // 记录设置全屏模式失败错误日志
        Logger.error(TAG, 'Failed to set the window layout to full-screen mode. ');
      }
    });
  }

  /**
   * 重置窗口尺寸私有方法
   * 为可折叠PC设备设置窗口限制和位置
   * @param windowClass 窗口对象
   */
  private static resetWindowSize(windowClass: window.Window): void {
    // 检查是否支持窗口会话管理器能力
    if (canIUse('SystemCapability.Window.SessionManager')) {
      // 获取默认显示器信息
      const windowSize: display.Display = display.getDefaultDisplaySync();
      // 计算应用宽度为屏幕宽度的90%
      const appWidth: number = windowSize.width * 9 / 10;
      // 计算应用高度为屏幕高度的87.5%
      const appHeight: number = windowSize.height * 7 / 8;
      // 设置窗口限制配置
      const windowLimits: window.WindowLimits = {
        // 最大宽度
        maxWidth: appWidth,
        // 最大高度
        maxHeight: appHeight,
        // 最小宽度
        minWidth: appWidth,
        // 最小高度
        minHeight: appHeight,
      }
      // 设置窗口限制
      windowClass.setWindowLimits(windowLimits);
      // 异步移动窗口到指定位置（屏幕宽度的5%，屏幕高度的6.25%）
      windowClass.moveWindowToAsync(windowSize.width / 20, windowSize.height / 16);
    }
  }

  /**
   * 获取设备尺寸私有方法
   * 获取设备高度、宽度和装饰高度并存储到全局信息模型
   * @param context 上下文对象
   */
  private static getDeviceSize(context: Context): void {
    // 获取设备高度
    window.getLastWindow(context).then((data: window.Window) => {
      try {
        // 获取窗口属性
        const properties = data.getWindowProperties();
        // 获取或创建全局信息模型
        const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
        // 将像素转换为vp并设置设备高度
        globalInfoModel.deviceHeight = px2vp(properties.windowRect.height);
        // 将像素转换为vp并设置设备宽度
        globalInfoModel.deviceWidth = px2vp(properties.windowRect.width);
        // 检查是否支持窗口会话管理器能力
        if (canIUse('SystemCapability.Window.SessionManager')) {
          // 获取窗口装饰高度
          const decorHeight: number = data?.getWindowDecorHeight();
          // 设置装饰高度
          globalInfoModel.decorHeight = decorHeight;
        }
        // 将更新后的全局信息模型存储到AppStorage
        AppStorage.setOrCreate('GlobalInfoModel', globalInfoModel);
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error = err as BusinessError;
        // 记录获取和设置设备尺寸失败错误日志
        Logger.error(TAG, `Get and setDeviceSize failed. code: ${error.code}, message: ${error.message}`);
      }
    }).catch((error: BusinessError) => {
      // 记录获取最后窗口失败错误日志
      Logger.error(TAG, `GetLastWindow failed. code: ${error.code}, message: ${error.message}`);
    });
  }

  /**
   * 设置主窗口方向方法
   * 设置窗口的首选方向
   * @param context 上下文对象
   * @param orientation 窗口方向枚举
   * @param onSuccess 成功回调函数，可选
   */
  public static setMainWindowOrientation(context: Context, orientation: window.Orientation,
    onSuccess?: () => void): void {
    // 获取最后一个窗口
    window.getLastWindow(context).then((windowClass: window.Window) => {
      // 如果窗口类未定义
      if (windowClass === undefined) {
        // 记录主窗口类未定义错误日志
        Logger.error(TAG, `MainWindowClass is undefined.`);
        return;
      }
      try {
        // 设置窗口首选方向
        windowClass.setPreferredOrientation(orientation, (err: BusinessError) => {
          // 获取错误代码
          const errCode = err.code;
          // 如果有错误代码
          if (errCode) {
            // 记录设置窗口方向失败错误日志
            Logger.error(TAG, `Failed to set window orientation. Cause code: ${err.code}, message: ${err.message}`);
            return;
          }
          // 调用成功回调函数
          onSuccess?.();
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error = err as BusinessError;
        // 记录设置首选方向失败错误日志
        Logger.error(TAG, `SetPreferredOrientation failed. code: ${error.code}, message: ${error.message}`);
      }
    }).catch((error: BusinessError) => {
      // 记录获取最后窗口失败错误日志
      Logger.error(TAG, `GetLastWindow failed. code: ${error.code}, message: ${error.message}`);
    });
  }

  /**
   * 设置任务继续活跃状态方法
   * 设置应用的任务继续状态为活跃或非活跃
   * @param context UI能力上下文
   * @param active 是否活跃状态
   */
  public static setMissionContinueActive(context: common.UIAbilityContext, active: boolean) {
    // 根据活跃状态设置对应的继续状态
    const activeState = active ? AbilityConstant.ContinueState.ACTIVE : AbilityConstant.ContinueState.INACTIVE;
    // 设置任务继续状态
    context.setMissionContinueState(activeState).then(() => {
      // 记录设置任务继续状态成功信息日志
      Logger.info(TAG, 'setMissionContinueState success');
    }).catch((err: BusinessError) => {
      // 记录设置任务继续状态失败错误日志
      Logger.error(TAG, `setMissionContinueState failed, code is ${err.code}, message is ${err.message}`);
    });
  }

  /**
   * 启用浮动窗口旋转方法
   * 启用横屏多窗口功能
   * @param context 上下文对象
   */
  public static enableFloatWindowRotate(context: Context): void {
    // 获取最后一个窗口
    window.getLastWindow(context).then((windowClass: window.Window) => {
      // 如果窗口类未定义
      if (windowClass === undefined) {
        // 记录主窗口类未定义错误日志
        Logger.error(TAG, `MainWindowClass is undefined`);
        return;
      }
      try {
        // 检查是否支持窗口会话管理器能力
        if (canIUse('SystemCapability.Window.SessionManager')) {
          // 启用横屏多窗口
          windowClass.enableLandscapeMultiWindow();
        } else {
          // 记录不支持enableLandscapeMultiWindow的错误日志
          Logger.error(TAG, `enableLandscapeMultiWindow invalid`);
        }
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error = err as BusinessError;
        // 记录启用横屏多窗口失败错误日志
        Logger.error(TAG, `enableLandscapeMultiWindow failed. code: ${error.code}, message: ${error.message}`);
      }
    }).catch((error: BusinessError) => {
      // 记录获取最后窗口失败错误日志
      Logger.error(TAG, `GetLastWindow failed. code: ${error.code}, message: ${error.message}`);
    });
  }

  /**
   * 禁用浮动窗口旋转方法
   * 禁用横屏多窗口功能
   * @param context 上下文对象
   */
  public static disableFloatWindowRotate(context: Context): void {
    // 获取最后一个窗口
    window.getLastWindow(context).then((windowClass: window.Window) => {
      // 如果窗口类未定义
      if (windowClass === undefined) {
        // 记录主窗口类未定义错误日志
        Logger.error(TAG, `MainWindowClass is undefined`);
        return;
      }
      try {
        // 检查是否支持窗口会话管理器能力
        if (canIUse('SystemCapability.Window.SessionManager')) {
          // 禁用横屏多窗口
          windowClass.disableLandscapeMultiWindow();
        } else {
          // 记录不支持disableLandscapeMultiWindow的错误日志
          Logger.error(TAG, `disableLandscapeMultiWindow invalid`);
        }
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error = err as BusinessError;
        // 记录禁用横屏多窗口失败错误日志
        Logger.error(TAG, `disableLandscapeMultiWindow failed. code: ${error.code}, message: ${error.message}`);
      }
    }).catch((error: BusinessError) => {
      // 记录获取最后窗口失败错误日志
      Logger.error(TAG, `GetLastWindow failed. code: ${error.code}, message: ${error.message}`);
    });
  }

  /**
   * 注册断点方法
   * 注册窗口断点系统并设置窗口尺寸变化和避让区域变化监听
   * @param windowStage 窗口舞台对象
   */
  public static registerBreakPoint(windowStage: window.WindowStage) {
    // 获取主窗口
    windowStage.getMainWindow((err: BusinessError, data: window.Window) => {
      // 如果获取主窗口失败
      if (err.code) {
        // 记录获取主窗口失败错误日志
        Logger.error(TAG, `Failed to get main window: ${err.message}`);
        return;
      }
      try {
        // 更新断点系统的宽度断点
        BreakpointSystem.getInstance().updateWidthBp(data);
        // 获取或创建全局信息模型
        const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
        // 获取系统避让区域
        const systemAvoidArea: window.AvoidArea = data.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
        // 设置状态栏高度
        globalInfoModel.statusBarHeight = px2vp(systemAvoidArea.topRect.height);
        // 获取底部导航指示器避让区域
        const bottomArea: window.AvoidArea = data.getWindowAvoidArea(window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR);
        // 设置导航指示器高度
        globalInfoModel.naviIndicatorHeight = px2vp(bottomArea.bottomRect.height);
        // 将更新后的全局信息模型存储到AppStorage
        AppStorage.setOrCreate('GlobalInfoModel', globalInfoModel);
        // 监听窗口尺寸变化事件
        data.on('windowSizeChange', () => WindowUtil.onWindowSizeChange(data));
        // 监听避让区域变化事件
        data.on('avoidAreaChange', (avoidAreaOption) => {
          // 如果是系统避让区域或导航指示器避让区域
          if (avoidAreaOption.type === window.AvoidAreaType.TYPE_SYSTEM ||
            avoidAreaOption.type === window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR) {
            // 设置避让区域
            WindowUtil.setAvoidArea(avoidAreaOption.type, avoidAreaOption.area);
          }
        });
      } catch (e) {
        // 捕获异常并转换为业务错误类型
        const error = e as BusinessError;
        // 记录获取窗口避让区域失败错误日志
        Logger.error(TAG, `getWindowAvoidArea failed. code: ${error.code}, message: ${error.message}`);
      }
    });
  }

  /**
   * 设置避让区域方法
   * 获取状态栏高度和指示器高度并更新全局信息模型
   * @param type 避让区域类型
   * @param area 避让区域对象
   */
  public static setAvoidArea(type: window.AvoidAreaType, area: window.AvoidArea) {
    // 获取或创建全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
    // 如果是系统避让区域类型
    if (type === window.AvoidAreaType.TYPE_SYSTEM) {
      // 设置状态栏高度
      globalInfoModel.statusBarHeight = px2vp(area.topRect.height);
    } else {
      // 设置导航指示器高度
      globalInfoModel.naviIndicatorHeight = px2vp(area.bottomRect.height);
    }
    // 将更新后的全局信息模型存储到AppStorage
    AppStorage.setOrCreate('GlobalInfoModel', globalInfoModel);
  }

  /**
   * 窗口尺寸变化处理方法
   * 当窗口尺寸发生变化时更新设备尺寸和断点系统
   * @param window 窗口对象
   */
  public static onWindowSizeChange(window: window.Window) {
    // 获取设备尺寸
    WindowUtil.getDeviceSize(getContext());
    // 通知断点系统窗口尺寸变化
    BreakpointSystem.getInstance().onWindowSizeChange(window);
  }
}

/**
 * 状态栏颜色类型枚举
 * 定义状态栏内容的颜色类型
 */
export enum StatusBarColorType {
  // 白色状态栏内容
  WHITE = '#ffffff',
  // 黑色状态栏内容（带透明度）
  BLACK = '#E5000000',
}

/**
 * 屏幕方向枚举
 * 定义屏幕的方向类型
 */
export enum ScreenOrientation {
  // 竖屏方向
  PORTRAIT = 'portrait',
  // 横屏方向
  LANDSCAPE = 'landscape',
}
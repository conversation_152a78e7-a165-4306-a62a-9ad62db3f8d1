/**
 * 应用包信息数据类
 * 用于存储和管理应用包的基本信息
 * 包含版本名称、版本代码和包名等关键信息
 */
export class BundleInfoData {
  // 版本名称，如"1.0.0"
  public versionName: string;
  // 版本代码，用于版本比较的数字标识
  public versionCode: number;
  // 应用包名，应用的唯一标识符
  public bundleName: string;

  /**
   * 构造函数
   * 初始化应用包信息数据
   * @param versionName 版本名称，可选参数，默认为'1.0.0'
   * @param versionCode 版本代码，可选参数，默认为1000000
   * @param bundleName 应用包名，可选参数，默认为空字符串
   */
  public constructor(versionName?: string, versionCode?: number, bundleName?: string) {
    // 设置版本名称，如果未提供则使用默认值'1.0.0'
    this.versionName = versionName || '1.0.0';
    // 设置版本代码，如果未提供则使用默认值1000000
    this.versionCode = versionCode || 1000000;
    // 设置应用包名，如果未提供则使用空字符串
    this.bundleName = bundleName || '';
  }
}
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';
// 导入动态项目组件
import { FeedItem } from './FeedItem';

// 使用Component装饰器定义动态卡片组件
@Component
export struct FeedCard {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop和Require装饰器定义发现内容数组属性
  @Prop @Require discoverContents: DiscoverContent[];
  // 定义处理项目点击函数
  handleItemClick?: Function;

  // 定义构建方法
  build() {
    // 创建滑块组件
    Swiper() {
      // 创建重复组件
      Repeat(this.discoverContents)
        // 定义每个项目的渲染
        .each((repeatItem: RepeatItem<DiscoverContent>) => {
          // 创建动态项目组件
          FeedItem({ discoverContent: repeatItem.item })
            // 设置点击事件
            .onClick(() => {
              // 调用处理项目点击函数
              this.handleItemClick?.(repeatItem.item);
            })
        })
        // 设置键值
        .key((item: DiscoverContent) => item.id.toString())
    }
    // 设置缓存数量为3
    .cachedCount(3)
    // 设置边缘效果为无
    .effectMode(EdgeEffect.None)
    // 禁用循环
    .loop(false)
    // 隐藏指示器
    .indicator(false)
    // 设置前边距
    .prevMargin(new BreakpointType<Length>({
      sm: CommonConstants.SPACE_8,
      md: CommonConstants.SPACE_12,
      lg: CommonConstants.SPACE_16 + CommonConstants.TAB_BAR_WIDTH,
      xl: CommonConstants.SPACE_16,
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 设置后边距
    .nextMargin(new BreakpointType<Length>({
      sm: CommonConstants.SPACE_8,
      md: CommonConstants.SPACE_12,
      lg: CommonConstants.SPACE_16,
      xl: CommonConstants.SPACE_16,
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 根据断点类型设置显示数量
    .displayCount(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? CommonConstants.LANE_MD :
    CommonConstants.LANE_LG)
    // 设置项目间距
    .itemSpace(new BreakpointType({
      sm: CommonConstants.SPACE_8,
      md: CommonConstants.SPACE_12,
      lg: CommonConstants.SPACE_16,
      xl: CommonConstants.SPACE_16,
    }).getValue(this.globalInfoModel.currentBreakpoint))
  }
}
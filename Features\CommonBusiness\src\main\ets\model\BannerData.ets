// 导入卡片数据中的媒体类型枚举
import { MediaTypeEnum } from './CardData';

/**
 * 横幅数据类
 * 用于存储横幅相关的所有信息
 * 包含标题、描述、媒体资源、类型等属性
 */
@Observed
export class BannerData {
  // 横幅唯一标识ID
  public id: number = 0;
  // 横幅主标题
  public bannerTitle: string = '';
  // 横幅副标题
  public bannerSubTitle: string = '';
  // 横幅描述信息
  public bannerDesc: string = '';
  // 横幅类型，使用BannerTypeEnum枚举
  public bannerType: BannerTypeEnum = 0;
  // 横幅值，用于存储额外的数据
  public bannerValue: string = '';
  // 媒体类型，默认为图片类型
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  // 媒体资源URL地址
  public mediaUrl: string = '';
  // 详情页面URL地址
  public detailsUrl: string = '';
  // 标签视图类型，默认为-1
  public tabViewType: number = -1;
}

/**
 * 横幅类型枚举
 * 定义不同类型的横幅内容分类
 */
export enum BannerTypeEnum {
  // 组件类型横幅
  COMPONENT = 1,
  // 示例类型横幅
  SAMPLE = 2,
  // 代码实验室类型横幅
  CODELAB = 3,
  // 文章类型横幅
  ARTICLE = 4,
  // 未知类型横幅
  UNKNOWN = 0,
}

// 横幅放大效果因子常量
export const BANNER_SCALE_FACTOR: number = 3.00;
// 标题放大效果因子常量
export const TITLE_SCALE_FACTOR: number = 6.00;
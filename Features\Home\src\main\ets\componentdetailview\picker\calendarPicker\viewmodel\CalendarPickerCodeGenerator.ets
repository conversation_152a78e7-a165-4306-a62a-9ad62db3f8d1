// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';
// 导入通用字体颜色和字体粗细映射数据
import { commonFontColorMap, fontWeightMapData } from '../../../common/entity/CommonMapData';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../../viewmodel/CommonCodeGenerator';
// 导入日历对齐类型映射数据
import { calendarAlignTypeMapData } from '../entity/CalendarPickerAttributeMapping';

/**
 * 日历选择器代码生成器类
 * 实现通用代码生成器接口，用于生成日历选择器相关的代码
 */
export class CalendarPickerCodeGenerator implements CommonCodeGenerator {
  // 日历X轴偏移量
  private calendarOffsetX: number = 0;
  // 日历Y轴偏移量
  private calendarOffsetY: number = 0;
  // 日历对齐类型，默认使用结束对齐
  private calendarAlignType: string = calendarAlignTypeMapData.get('Default')!.code;
  // 选择器字体颜色，默认使用通用默认颜色
  private pickerFontColor: string = commonFontColorMap.get('Default')!.code;
  // 选择器字体大小，默认为16
  private pickerFontSize: number = 16;
  // 选择器字体粗细，默认使用普通粗细
  private pickerFontWeight: string = fontWeightMapData.get('Default')!.code;

  /**
   * 生成日历选择器代码方法
   * 根据属性配置生成完整的日历选择器组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的日历选择器代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'calendarAlignType':
          // 设置日历对齐类型
          this.calendarAlignType =
            calendarAlignTypeMapData.get(attribute.currentValue)?.code ?? this.calendarAlignType;
          break;
        case 'calendarOffsetX':
          // 设置日历X轴偏移量
          this.calendarOffsetX = Number(attribute.currentValue);
          break;
        case 'calendarOffsetY':
          // 设置日历Y轴偏移量
          this.calendarOffsetY = Number(attribute.currentValue);
          break;
        case 'pickerFontColor':
          // 设置选择器字体颜色
          this.pickerFontColor = attribute.currentValue;
          break;
        case 'pickerFontSize':
          // 设置选择器字体大小
          this.pickerFontSize = Number(attribute.currentValue);
          break;
        case 'pickerFontWeight':
          // 设置选择器字体粗细
          this.pickerFontWeight = fontWeightMapData.get(attribute.currentValue)?.code ?? this.pickerFontWeight;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整日历选择器代码字符串
    return `// 日历选择器组件
// 提供日期选择功能，支持自定义对齐方式和文本样式
@Component
struct CalendarPickerComponent {
  // 构建组件UI方法
  build() {
    // 创建列布局容器
    Column() {
      // 创建日历选择器组件
      CalendarPicker({ hintRadius: 10, selected: new Date('2024-03-05') })
        // 设置组件宽度为30%
        .width('30%')
        // 设置边缘对齐方式和偏移量
        .edgeAlign(${this.calendarAlignType}, { dx: ${this.calendarOffsetX}, dy: ${this.calendarOffsetY} })
        // 设置文本样式，包括颜色、字体大小和粗细
        .textStyle({ color: '${this.pickerFontColor}', font: { size: ${this.pickerFontSize}, weight: ${this.pickerFontWeight} } })
    }
    // 设置列布局垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
    // 设置列布局水平对齐方式为居中
    .alignItems(HorizontalAlign.Center)
    // 设置列布局宽度为100%
    .width('100%')
  }
}`;
  }
}
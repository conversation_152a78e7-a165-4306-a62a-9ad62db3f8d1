// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入文本区域相关的属性映射数据
import { textAlignTypeMapData, textOverflowTypeMapData } from '../entity/TextAreaAttributeMapping';

/**
 * 文本区域代码生成器类
 * 实现通用代码生成器接口，用于生成文本区域组件的代码
 */
export class TextAreaCodeGenerator implements CommonCodeGenerator {
  // 私有最大行数属性，默认为2行
  private maxLines: number = 2;
  // 私有行间距属性，默认为5像素
  private lineSpacing: number = 5;
  // 私有文本溢出类型字符串，默认使用默认值
  private textOverflowTypeStr: string = textOverflowTypeMapData.get('Default')!.code;
  // 私有文本对齐字符串，默认使用默认值
  private textAlignStr: string = textAlignTypeMapData.get('Default')!.code;

  /**
   * 生成文本区域组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的文本区域组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性进行处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理最大行数属性
        case 'maxLines':
          // 将属性值转换为数字类型
          this.maxLines = Number(attribute.currentValue);
          break;
        case 'lineSpacing':
          this.lineSpacing = Number(attribute.currentValue);
          break;
        case 'textOverflowType':
          this.textOverflowTypeStr =
            textOverflowTypeMapData.get(attribute.currentValue)?.code ?? textOverflowTypeMapData.get('Default')!.code;
          break;
        case 'textAlign':
          this.textAlignStr =
            textAlignTypeMapData.get(attribute.currentValue)?.code ?? textAlignTypeMapData.get('Default')!.code;
          break;
        default:
          break;
      }
    });
    return `import { LengthMetrics } from '@kit.ArkUI';

@Component
struct TextAreaComponent {
  build() {
   Column(){
    TextArea({
      text: '我有一只可爱的玩具熊。它长得很胖，胖得肚子都要爆炸了!它穿的衣服都很漂亮，而且衣服上五颜六色，有紫的、有粉的、还有黄的，非常漂亮!',
      placeholder: '请输入文字'
    })
      .margin({ left:36, right:36 })
      .fontWeight(FontWeight.Regular)
      .fontSize($r('sys.float.Body_L'))
      .enterKeyType(EnterKeyType.Done)
      .borderRadius($r('sys.float.corner_radius_level8'))
      .backgroundColor($r('sys.color.comp_background_tertiary'))
      .maxLines(${this.maxLines})
      .lineSpacing(LengthMetrics.px(${this.lineSpacing}))
      .textOverflow(${this.textOverflowTypeStr})
      .textAlign(${this.textAlignStr})
     }
  }
}`;
  }
}
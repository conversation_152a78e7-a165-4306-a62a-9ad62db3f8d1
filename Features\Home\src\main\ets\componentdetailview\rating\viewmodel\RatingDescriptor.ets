// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入评分组件相关映射数据
import {
  indicatorMapData,
  ratingValueMapData,
  starsMapData,
  starStyleMapData,
} from '../entity/RatingAttributeMapping';

/**
 * 评分组件描述器类
 * 继承通用描述器，专门用于描述评分组件的配置
 * 包含评分值、指示器模式、星星数量、步长和星星样式等配置信息
 */
@Observed
export class RatingDescriptor extends CommonDescriptor {
  // 当前评分值，默认使用映射数据中的默认值
  public rating: number = ratingValueMapData.get('Default')!.value as number;
  // 是否为指示器模式（只读），默认使用映射数据中的默认值
  public indicator: boolean = indicatorMapData.get('Default')!.value as boolean;
  // 星星总数，默认使用映射数据中的默认值
  public stars: number = starsMapData.get('Default')!.value as number;
  // 评分步长，默认为0.5
  public stepSize: number = 0.5;
  // 是否使用自定义星星样式，默认使用映射数据中的默认值
  public starStyle: boolean = starStyleMapData.get('Default')!.value as boolean;

  /**
   * 转换属性方法
   * 将原始属性数组转换为评分组件的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'rating':
          // 设置评分值，转换为数字类型
          this.rating = Number(attribute.currentValue) ?? ratingValueMapData.get('Default')!.value as number;
          break;
        case 'indicator':
          // 设置指示器模式，转换为布尔类型
          this.indicator = attribute.currentValue.toLowerCase() === 'true';
          break;
        case 'stars':
          // 设置星星数量，转换为数字类型
          this.stars = Number(attribute.currentValue) ?? starsMapData.get('Default')!.value as number;
          break;
        case 'starStyle':
          // 设置星星样式，转换为布尔类型
          this.starStyle = attribute.currentValue.toLowerCase() === 'true';
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
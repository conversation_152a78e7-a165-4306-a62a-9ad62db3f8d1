// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、列枚举、通用常量和视频组件
import { BreakpointType, ColumnEnum, CommonConstants, VideoComponent } from '@ohos/common';
// 导入通用业务模块中的媒体类型枚举
import { MediaTypeEnum } from '@ohos/commonbusiness';
// 导入示例详情数据
import { SampleDetailData } from '../viewmodel/SampleDetailState';
// 导入示例卡片组件
import { SampleCard } from './SampleCard';

// 使用Component装饰器定义示例组件
@Component
export struct SampleComponent {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用ObjectLink装饰器定义单个示例数据对象链接
  @ObjectLink singleSampleData: SampleDetailData;
  // 使用Prop和Require装饰器定义必需的示例索引属性
  @Prop @Require sampleIndex: number;
  // 使用Prop装饰器定义显示指示器属性
  @Prop showIndicator: boolean = false;

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 创建媒体内容列布局
      Column() {
        // 如果媒体类型是图片
        if (this.singleSampleData.mediaType === MediaTypeEnum.IMAGE) {
          // 显示图片
          Image($rawfile(this.singleSampleData.mediaUrl))
            .draggable(false)
            .alt($r('app.media.img_placeholder'))
            .objectFit(ImageFit.Contain)
            .layoutWeight(1)
        } else {
          // 否则显示视频组件
          VideoComponent({
            mediaSrc: this.singleSampleData.mediaUrl,
            autoPlay: true,
            loopPlay: true,
            clickPause: false,
            startVisibleRatio: 0.5,
          })
        }
      }
      // 设置媒体内容列权重为1
      .layoutWeight(1)

      // 创建网格行布局
      GridRow({
        // 设置不同断点的列数
        columns: { sm: ColumnEnum.SM, md: ColumnEnum.MD, lg: ColumnEnum.LG },
        // 设置网格间距
        gutter: {
          x: {
            sm: $r('sys.float.padding_level6'),
            md: $r('sys.float.padding_level6'),
            lg: $r('sys.float.padding_level8'),
          },
        },
      }) {
        // 创建网格列
        GridCol({
          // 设置不同断点的跨度
          span: { sm: CommonConstants.SPAN_4, md: CommonConstants.SPAN_6, lg: CommonConstants.SPAN_8 },
          // 设置不同断点的偏移
          offset: { sm: 0, md: 1, lg: 2 },
        }) {
          // 显示示例卡片组件
          SampleCard({ sampleCard: this.singleSampleData.sampleCard, sampleIndex: this.sampleIndex, })
        }
      }
      // 根据是否显示指示器设置网格行底部边距
      .margin({
        bottom: this.showIndicator ? $r('sys.float.padding_level16') : $r('sys.float.padding_level8'),
      })
    }
    // 设置主列背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 设置主列高度为100%
    .height('100%')
    // 设置主列宽度为100%
    .width('100%')
    // 根据断点设置主列内边距
    .padding({
      top: $r('sys.float.padding_level4'),
      left: new BreakpointType({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
      right: new BreakpointType({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
    })
  }
}
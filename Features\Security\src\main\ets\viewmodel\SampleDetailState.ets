// 导入通用模块中的基础状态和加载状态
import { BaseState, LoadingStatus } from '@ohos/common';
// 导入示例类型枚举
import { SampleTypeEnum } from '../common/SampleConstant';

// 使用Observed装饰器定义可观察的示例详情状态类
@Observed
export class SampleDetailState extends BaseState {
  // 定义示例详情数据数组公共属性
  public sampleDatas: SampleDetailData[] = [];
  // 定义示例数量公共属性
  public sampleCount: number = 0;
  // 定义加载状态公共属性
  public loadingStatus: LoadingStatus = LoadingStatus.LOADING;
  // 定义任务ID可选公共属性
  public taskId?: string = '';
  // 定义当前索引公共属性
  public currentIndex: number = 0;
  // 定义安装状态公共属性
  public installingStatus: boolean = false;
  // 定义下载状态公共属性
  public downloadingStatus: boolean = false;
  // 定义返回按钮是否被按下公共属性
  public isBackPressed: boolean = false;
}

// 使用Observed装饰器定义可观察的示例详情数据类
@Observed
export class SampleDetailData {
  // 定义ID公共属性
  public id: number = 0;
  // 定义是否收藏公共属性
  public isFavorite: boolean = false;
  // 定义媒体类型公共属性
  public mediaType: number = 0;
  // 定义媒体URL公共属性
  public mediaUrl: string = '';
  // 定义示例卡片数据公共属性
  public sampleCard: SampleCardData = new SampleCardData();
}

// 使用Observed装饰器定义可观察的示例卡片数据类
@Observed
export class SampleCardData {
  // 定义标题公共属性
  public title: string = '';
  // 定义描述公共属性
  public desc: string = '';
  // 定义示例类型公共属性
  public sampleType: SampleTypeEnum = SampleTypeEnum.COMMON_SAMPLE;
  // 定义示例ID公共属性
  public sampleId: number = 0;
  // 定义原始URL公共属性
  public originalUrl: string = '';
  // 定义模块名称公共属性
  public moduleName: string = '';
  // 定义能力名称公共属性
  public abilityName: string = '';
  // 定义绑定表单显示状态公共属性
  public bindSheetShow: boolean = false;
  // 定义下载状态公共属性
  public downloading: boolean = false;
  // 定义下载进度公共属性
  public downloadProgress: number = -1;
  // 定义安装状态公共属性
  public installed: boolean = false;
}
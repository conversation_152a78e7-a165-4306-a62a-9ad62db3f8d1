// 导出字符串工具类
export class StringUtil {
  // 定义静态方法用于生成模板字符串
  public static getTemplateString(num: number): string {
    // 声明空字符串变量用于拼接结果
    let str = '';
    // 使用for循环遍历指定次数
    for (let i = 0; i < num; i++) {
      // 每次循环向字符串添加"1fr "
      str += '1fr ';
    }
    // 去除字符串末尾的空格并返回结果
    return str.trim();
  }

  // 定义静态方法用于将字符串转换为数字数组
  public static stringToArray(inputStr: string): number[] {
    // 检查输入字符串去除空格后是否为空
    if (inputStr.trim() === '') {
      // 如果为空则返回空数组
      return [];
    }
    // 使用逗号分割字符串并转换每个元素为数字
    return inputStr.split(',').map(Number);
  }
}
// 导入通用模块中的观察数组类型
import type { ObservedArray } from '@ohos/common';
// 导入通用模块中的振动工具
import { VibratorUtils } from '@ohos/common';
// 导入属性类型定义
import type { Attribute } from '../viewmodel/Attribute';
// 导入颜色选择器组件
import { ColorPickerComponent } from './ColorPickerComponent';
// 导入选择组件
import { SelectComponent } from './SelectComponent';
// 导入滑块组件
import { SliderComponent } from './SliderComponent';
// 导入开关组件
import { ToggleComponent } from './ToggleComponent';
// 导入透明度组件
import { OpacityComponent } from './OpacityComponent';
// 导入切换按钮组件
import { ToggleButtonComponent } from './ToggleButtonComponent';
// 导入属性类型枚举
import { AttributeTypeEnum } from '../viewmodel/AttributeTypeEnum';
// 导入各种属性类型定义
import {
  ColorPickerAttribute,
  OpacityPickerAttribute,
  SelectComAttribute,
  SliderComAttribute,
  ToggleButtonAttribute,
  ToggleComAttribute,
} from '../viewmodel/ComponentDetailState';
// 导入属性变更事件和组件详情页面视图模型
import { ChangeAttributeEvent, ComponentDetailPageVM } from '../viewmodel/ComponentDetailPageVM';
// 导入组件详情管理器
import { ComponentDetailManager } from '../viewmodel/ComponentDetailManager';

/**
 * 属性变更区域组件
 * 用于显示和管理组件属性的变更控件
 * 根据属性类型动态渲染不同的控制组件
 */
@Component
export struct AttributeChangeArea {
  // 属性数组，监听变化并触发数据获取
  @ObjectLink @Watch('getAttributeData') attributes: ObservedArray<Attribute>;
  // 组件名称
  @Prop componentName: string;
  // 第二类属性数据（颜色选择器、透明度等）
  @State attributeDataTwo: Attribute[] = [];
  // 第一类属性数据（开关、滑块、选择器等）
  @State attributeDataOne: Attribute[] = [];
  // 第二类属性数据长度
  @State attributeDataTwoLength: number = 0;
  // 第一类属性数据长度
  @State attributeDataOneLength: number = 0;

  /**
   * 组件即将出现时的生命周期回调
   * 初始化属性数据
   */
  aboutToAppear(): void {
    this.getAttributeData();
  }

  /**
   * 事件回调函数
   * 处理属性值变更事件
   * @param name 属性名称
   * @param value 属性值
   * @param mode 滑块变更模式（可选）
   */
  eventCallback = (name: string, value: string, mode?: SliderChangeMode) => {
    // 获取组件详情视图模型
    const viewModel: ComponentDetailPageVM | undefined =
      ComponentDetailManager.getInstance().getDetailViewModel(this.componentName);
    // 发送属性变更事件
    viewModel?.sendEvent(new ChangeAttributeEvent(name, value));
    // 如果是滑块移动模式，触发振动反馈
    if (mode && (mode === SliderChangeMode.Moving)) {
      VibratorUtils.startVibration();
    }
  }

  /**
   * 获取属性数据方法
   * 将属性按类型分为两组进行显示
   */
  getAttributeData() {
    const attributeDataOne: Attribute[] = [];
    const attributeDataTwo: Attribute[] = [];
    // 遍历属性数组，按类型分组
    this.attributes.forEach((item: Attribute) => {
      // 第一组：开关按钮、开关、滑块、选择器
      if ([AttributeTypeEnum.TOGGLE_BUTTON, AttributeTypeEnum.TOGGLE, AttributeTypeEnum.SLIDER,
        AttributeTypeEnum.SELECT].includes(item.type)) {
        attributeDataOne.push(item);
      } else {
        // 第二组：其他类型（颜色选择器、透明度等）
        attributeDataTwo.push(item);
      }
    });
    // 更新状态数据
    this.attributeDataTwoLength = attributeDataTwo.length;
    this.attributeDataOneLength = attributeDataOne.length;
    this.attributeDataTwo = attributeDataTwo;
    this.attributeDataOne = attributeDataOne;
  }

  /**
   * 构建属性变更区域的UI结构
   * 分两个区域显示不同类型的属性控件
   */
  build() {
    // 主容器列布局
    Column() {
      // 第一类属性区域（开关、滑块、选择器等）
      if (this.attributeDataOneLength !== 0) {
        Column() {
          // 遍历第一类属性数据
          ForEach(this.attributeDataOne, (item: Attribute, index: number) => {
            // 非第一项添加分割线
            if (index !== 0) {
              Divider()
                // 设置分割线颜色
                .color($r('sys.color.comp_background_tertiary'))
                // 设置分割线宽度
                .width('100%')
                // 设置分割线内边距
                .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
            }
            // 根据属性类型渲染对应组件
            if (item instanceof SelectComAttribute) {
              // 选择组件
              SelectComponent({ attribute: item, callback: this.eventCallback })
            } else if (item instanceof ToggleButtonAttribute) {
              // 切换按钮组件
              ToggleButtonComponent({ attribute: item, callback: this.eventCallback })
            } else if (item instanceof SliderComAttribute) {
              // 滑块组件
              SliderComponent({ attribute: item, callback: this.eventCallback })
            } else if (item instanceof ToggleComAttribute) {
              // 开关组件
              ToggleComponent({ attribute: item, callback: this.eventCallback })
            }
          }, (item: Attribute, _index: number) => item.name)
        }
        // 设置第一类属性区域样式
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .padding({
          top: $r('sys.float.padding_level2'),
          bottom: $r('sys.float.padding_level2'),
        })
        // 根据第二类属性是否存在设置底部边距
        .margin({ bottom: this.attributeDataTwoLength === 0 ? 0 : $r('sys.float.padding_level8') })
        .backgroundColor($r('sys.color.comp_background_primary'))
        .borderRadius($r('sys.float.corner_radius_level8'))
      }

      // 第二类属性区域（颜色选择器、透明度等）
      if (this.attributeDataTwoLength !== 0) {
        Column() {
          // 遍历第二类属性数据
          ForEach(this.attributeDataTwo, (item: Attribute, index: number) => {
            // 非第一项添加分割线
            if (index !== 0) {
              Divider()
                // 设置分割线颜色
                .color($r('sys.color.comp_background_tertiary'))
                // 设置分割线宽度
                .width('100%')
                // 设置分割线内边距
                .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
            }
            // 根据属性类型渲染对应组件
            if (item instanceof ColorPickerAttribute) {
              // 颜色选择器组件
              ColorPickerComponent({ attribute: item, callback: this.eventCallback })
            } else if (item instanceof OpacityPickerAttribute) {
              // 透明度组件
              OpacityComponent(item, this.eventCallback)
            }
          }, (item: Attribute, _index: number) => item.name)
        }
        // 设置第二类属性区域样式
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .padding({
          top: $r('sys.float.padding_level2'),
          bottom: $r('sys.float.padding_level2'),
        })
        .backgroundColor($r('sys.color.comp_background_primary'))
        .borderRadius($r('sys.float.corner_radius_level8'))
      }
    }
  }
}
// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入滑动器相关的属性映射数据
import { indicatorEffectMapping, indicatorStyleMapData } from '../entity/SwiperAttributeMapping';

/**
 * 滑动器代码生成器类
 * 实现通用代码生成器接口，用于生成滑动器组件的代码
 */
export class SwiperCodeGenerator implements CommonCodeGenerator {
  // 私有显示箭头标志，默认为true
  private isDisplayArrow: boolean = true;
  // 私有显示箭头代码，默认为空字符串
  private displayArrow: string = '';
  // 私有指示器类型，默认为点指示器
  private indicatorType: string = 'DotIndicator';
  // 私有指示器代码，默认使用默认值
  private indicatorCode: string = indicatorStyleMapData.get('Default')!.code;
  // 私有垂直标志，默认为true
  private isVertical: boolean = true;
  // 私有效果模式，默认使用默认值
  private effectMode: string = indicatorEffectMapping.get('Default')!.code;
  // 私有循环标志，默认为true
  private loop: boolean = true;

  /**
   * 生成滑动器组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的滑动器组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 开始构建滑动器代码
    let code = `Swiper() {
      ForEach(this.list, (item: string,index: number) => {
        Text(item.toString())
          .width('90%')
          .height("80%")
          .height(160)
          .backgroundColor(0xAFEEEE)
          .textAlign(TextAlign.Center)
          .fontSize(30)
      }, (item: string) => item)
    }
    .loop(${this.loop})\n`;

    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'indicator':
          this.indicatorType = attribute.currentValue as string;
          this.indicatorCode = `    .indicator(${indicatorStyleMapData.get(this.indicatorType)?.code})`;
          break;
        case 'vertical':
          this.isVertical = JSON.parse(attribute.currentValue);
          code += `    .vertical(${this.isVertical ? 'true' : 'false'})\n`;
          break;
        case 'effectMode':
          this.effectMode = attribute.currentValue as string;
          if (this.effectMode === 'Spring') {
            code += `    .effectMode(EdgeEffect.Spring)`;
          } else if (this.effectMode === 'Fade') {
            code += `    .effectMode(EdgeEffect.Fade)`;
          } else if (this.effectMode === 'None') {
            code += `    .effectMode(EdgeEffect.None)`;
          }
          break;
        case 'isDisplayArrow':
          this.isDisplayArrow = JSON.parse(attribute.currentValue);
          if (this.isDisplayArrow) {
            this.displayArrow = `  .displayArrow({
  showBackground: true,
  isSidebarMiddle: true,
  backgroundSize: 24,
  backgroundColor: Color.White,
  arrowSize: 18,
  arrowColor: Color.Blue
})\n`;
          } else {
            this.displayArrow = '    .displayArrow(false)';
          }
          break;
        case 'loop':
          this.loop = JSON.parse(attribute.currentValue);
          break;
        default:
          break;
      }
    });
    return `function getSwiperData(): number[] {
  const list: number[] = [];
  for (let i = 1; i <= 3; i++) {
    list.push(i);
  }
  return list;
}

@Component
struct SwiperComponent {
  @State list: number[] = getSwiperData();

  build() {
    ${code}\n${this.indicatorCode}\n${this.displayArrow}
  }
}`;
  }
}
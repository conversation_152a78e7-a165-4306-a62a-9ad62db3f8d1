// 导入能力工具包中的通用类型和Want类型，用于应用间跳转
import type { common, Want } from '@kit.AbilityKit';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入电话工具包中的通话功能，用于拨打电话
import { call } from '@kit.TelephonyKit';
// 导入通用模块中的配置映射键、日志记录器和资源工具
import { ConfigMapKey, Logger, ResourceUtil } from '@ohos/common';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入应用链接描述器类型，用于获取链接配置信息
import type { AppLinkingDescriptor } from '../viewmodel/AppLinkingDescriptor';
// 导入链接类型枚举、类型资源映射数据和Want参数
import { LinkType, typeResourcesMapData, wantParam } from '../entity/AppLinkingAttributeMapping';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[AppLinkingComponent]';

/**
 * 应用链接构建器函数
 * 用于构建应用链接组件的UI界面
 * @param $$ 描述器包装对象，包含应用链接的配置信息
 */
@Builder
export function AppLinkingBuilder($$: DescriptorWrapper) {
  // 创建应用链接组件实例，传入描述器配置
  AppLinkingComponent({ appLinkingDescriptor: $$.descriptor as AppLinkingDescriptor })
}

/**
 * 应用链接组件结构体
 * 实现应用间跳转功能，支持跳转到图库、地图、设置和拨号等应用
 */
@Component
struct AppLinkingComponent {
  // 应用链接描述器属性，包含链接类型等配置信息
  @Prop appLinkingDescriptor: AppLinkingDescriptor;
  // 图库URL地址，用于存储图库应用的链接地址
  private galleryUrl: string = '';

  /**
   * 构建UI界面的方法
   * 创建包含跳转按钮的垂直布局
   */
  build() {
    // 创建垂直列布局
    Column() {
      // 创建跳转按钮，按钮文本根据链接类型动态显示
      Button($r('app.string.pull_up_page', typeResourcesMapData.get(this.appLinkingDescriptor.type)))
        // 设置按钮背景色为次要背景色
        .backgroundColor($r('sys.color.background_secondary'))
        // 设置按钮高度
        .height($r('app.float.button_height_normal'))
        // 设置按钮字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置按钮字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置按钮字体大小
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮点击事件处理函数
        .onClick(() => {
          // 根据链接类型执行不同的跳转操作
          if (this.appLinkingDescriptor.type === LinkType.TYPE_GALLERY) {
            // 跳转到图库应用
            this.jumpToGallery();
          } else if (this.appLinkingDescriptor.type === LinkType.TYPE_MAP) {
            // 跳转到地图应用
            this.jumpToMap();
          } else if (this.appLinkingDescriptor.type === LinkType.TYPE_SETTINGS) {
            // 跳转到设置应用
            this.jumpToSettings();
          } else if (this.appLinkingDescriptor.type === LinkType.TYPE_DAIL) {
            // 跳转到拨号应用
            this.jumpToDial();
          }
        })
    }
    // 设置列布局宽度为100%
    .width('100%')
    // 设置列布局高度为100%
    .height('100%')
    // 设置子组件在主轴上居中对齐
    .justifyContent(FlexAlign.Center)
  }

  /**
   * 跳转到图库应用的方法
   * 使用openLink方式打开图库应用，支持应用链接功能
   */
  jumpToGallery(): void {
    // 获取当前组件的UI能力上下文
    const context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
    // 如果图库URL为空，则从资源文件中获取
    if (!this.galleryUrl) {
      // 通过资源工具获取图库URL配置
      this.galleryUrl = ResourceUtil.getRawFileStringByKey(context, ConfigMapKey.GALLERY_URL);
    }
    // 使用try-catch处理可能的异常
    try {
      // 调用openLink方法打开图库链接，允许非应用链接方式
      context.openLink(this.galleryUrl, { appLinkingOnly: false })
        .then(() => {
          // 链接打开成功时记录日志
          Logger.info(TAG, 'OpenLink success.');
        })
        .catch((error: BusinessError) => {
          // 链接打开失败时记录错误日志
          Logger.error(TAG, `Openlink failed. Code: ${error.code}, message is ${error.message}`);
        });
    } catch (error) {
      // 捕获同步异常并记录错误日志
      const err: BusinessError = error as BusinessError;
      Logger.error(TAG, `Openlink failed., error code: ${err.code}, message: ${err.message}.`);
    }
  }

  /**
   * 跳转到地图应用的方法
   * 使用startAbilityByType方式启动导航类型的应用
   */
  jumpToMap(): void {
    // 获取当前组件的UI能力上下文
    const context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
    // 定义能力启动回调对象，处理启动结果
    const abilityStartCallback: common.AbilityStartCallback = {
      // 启动失败时的回调函数
      onError: (code: number, name: string, message: string) => {
        // 记录启动失败的错误日志
        Logger.error(TAG, `Fail start ability, name is ${name}, code is ${code}, message is ${message}`);
      },
      // 启动成功时的回调函数
      onResult: (result) => {
        // 记录启动成功的调试日志
        Logger.debug(TAG, `Success in start ability, result is ${JSON.stringify(result)}`);
      }
    }
    // 使用try-catch处理可能的异常
    try {
      // 按类型启动导航应用，传入Want参数和回调函数
      context.startAbilityByType('navigation', wantParam, abilityStartCallback);
    } catch (err) {
      // 捕获异常并记录错误日志
      const error: BusinessError = err as BusinessError;
      Logger.error(TAG, `StartAbilityByType error, the code is ${error.code}, the message is ${error.message}`);
    }
  }

  /**
   * 跳转到设置应用的方法
   * 使用startAbility方式启动系统设置应用
   */
  jumpToSettings(): void {
    // 获取当前组件的UI能力上下文
    const context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
    // 定义Want对象，指定要启动的设置应用信息
    const want: Want = {
      // 设置应用的包名
      bundleName: 'com.huawei.hmos.settings',
      // 设置应用的主能力名称
      abilityName: 'com.huawei.hmos.settings.MainAbility',
      // URI参数，此处为空
      uri: '',
    };
    // 使用try-catch处理可能的异常
    try {
      // 启动设置应用
      context.startAbility(want);
    } catch (err) {
      // 捕获异常并记录错误日志
      const error: BusinessError = err as BusinessError;
      Logger.error(TAG, `StartAbility error, the code is ${error.code}, the message is ${error.message}`);
    }
  }

  /**
   * 跳转到拨号应用的方法
   * 检查设备是否支持通话功能，如果支持则发起通话，否则显示提示对话框
   */
  jumpToDial(): void {
    // 检查设备是否支持语音通话功能
    let isSupport = call.hasVoiceCapability();
    // 如果设备支持通话功能
    if (isSupport) {
      // 检查是否具有联系人系统能力
      if (canIUse('SystemCapability.Applications.Contacts')) {
        // 发起通话，传入空号码和回调函数
        call.makeCall('', (err: BusinessError) => {
          // 如果通话发起失败
          if (err) {
            // 记录通话失败的错误日志
            Logger.error(TAG, `MakeCall fail, error code ${err.code}, message: ${err.message}`);
          } else {
            // 记录通话成功的日志
            Logger.info(TAG, `MakeCall success`);
          }
        });
      }
    } else {
      // 如果设备不支持通话功能，显示提示对话框
      AlertDialog.show({
        // 对话框标题为空
        title: '',
        // 显示不支持拨号的提示消息
        message: $r('app.string.not_support_dial'),
        // 设置确认按钮
        primaryButton: {
          // 按钮文本
          value: $r('app.string.sure'),
          // 按钮点击事件
          action: () => {
            // 记录设备不支持拨号的日志
            Logger.info(TAG, 'The device does not support dial-up.');
          }
        }
      });
    }
  }
}
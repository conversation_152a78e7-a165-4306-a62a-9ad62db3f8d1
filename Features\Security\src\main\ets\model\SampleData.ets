// 导入通用模块中的加载模型
import { LoadingModel } from '@ohos/common';
// 导入通用业务模块中的横幅数据类型
import type { BannerData, } from '@ohos/commonbusiness';
// 导入通用业务模块中的卡片样式类型枚举、卡片类型枚举和媒体类型枚举
import { CardStyleTypeEnum, CardTypeEnum, MediaTypeEnum } from '@ohos/commonbusiness';

// 导出示例内容类
export class SampleContent {
  // 定义示例内容ID
  public id: number = 0;
  // 定义示例内容类型
  public type: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 定义示例内容媒体类型
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  // 定义示例内容媒体URL
  public mediaUrl: string = '';
  // 定义示例内容标题
  public title: string = '';
  // 定义示例内容副标题
  public subTitle: string = '';
  // 定义示例内容标签数组
  public tags: string[] = [];
}

// 导出示例卡片数据类
export class SampleCardData {
  // 定义示例卡片数据ID
  public id: number = 0;
  // 定义示例卡片标题
  public cardTitle: string = '';
  // 定义示例卡片副标题
  public cardSubTitle: string = '';
  // 定义示例卡片类型
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 定义示例卡片样式类型
  public cardStyleType: CardStyleTypeEnum = CardStyleTypeEnum.LIST;
  // 定义示例卡片图片
  public cardImage: string = '';
  // 定义示例卡片版本
  public version: string = '';
  // 定义示例卡片内容数组
  public sampleContents: SampleContent[] = [];
  // 定义示例卡片详情卡片ID
  public detailCardId: number = 0;
}

// 使用Observed装饰器定义可观察的示例分类类
@Observed
export class SampleCategory {
  // 定义加载模型
  public loadingModel: LoadingModel = new LoadingModel();
  // 定义当前页码
  public currentPage: number = 1;
  // 定义示例分类ID
  public id: number = 0;
  // 定义示例分类名称
  public categoryName: string = '';
  // 定义示例分类类型
  public categoryType: number = 0;
  // 定义标签页图标
  public tabIcon: string = '';
  // 定义标签页选中图标
  public tabIconSelected: string = '';
  // 定义示例卡片数组
  public sampleCards: SampleCardData[] = [];
}

// 使用Observed装饰器定义可观察的示例数据类
@Observed
export class SampleData {
  // 定义横幅信息数组
  public bannerInfos?: BannerData[];
  // 定义示例分类数组
  public sampleCategories: SampleCategory[] = [];
}
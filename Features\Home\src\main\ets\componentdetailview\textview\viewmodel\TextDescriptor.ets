// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入字体粗细映射数据，用于字体粗细属性处理
import { fontWeightMapData } from '../../common/entity/CommonMapData';
// 导入文本相关的属性映射数据
import {
  fontColorMapData,
  fontSizeMapData,
  letterSpacingMapData,
  opacityMapData,
  textShadowRadiusMapData,
} from '../entity/TextAttributeMapping';

/**
 * 文本描述器类
 * 继承自通用描述器，用于描述文本组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TextDescriptor extends CommonDescriptor {
  // 字体粗细属性，默认使用默认值
  public fontWeight: FontWeight = fontWeightMapData.get('Default')!.value as FontWeight;
  // 字体大小属性，支持数字或字符串类型，默认使用默认值
  public fontSize: number | string = fontSizeMapData.get('Default')!.value as number;
  public fontColor: ResourceColor = fontColorMapData.get('Default')!.value as string;
  public opacity: number = opacityMapData.get('Default')!.value as number;
  public letterSpacing: number = letterSpacingMapData.get('Default')!.value as number;
  public textShadowRadius: number = textShadowRadiusMapData.get('Default')!.value as number;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'fontWeight':
          this.fontWeight = (fontWeightMapData.get(attribute.currentValue)?.value ??
          fontWeightMapData.get('Default')!.value) as FontWeight;
          break;
        case 'fontColor':
          this.fontColor = attribute.currentValue;
          break;
        case 'fontSize':
          this.fontSize = Number(attribute.currentValue);
          break;
        case 'opacity':
          this.opacity = Number(attribute.currentValue);
          break;
        case 'letterSpacing':
          this.letterSpacing = Number(attribute.currentValue);
          break;
        case 'textShadowRadius':
          this.textShadowRadius = Number(attribute.currentValue);
          break;
        default:
          break;
      }
    });
  }
}
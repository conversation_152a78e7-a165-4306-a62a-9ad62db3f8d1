// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承基础描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入警告对话框对齐方式映射数据，包含各种对齐方式的配置选项
import { alertDialogAlignmentMapData } from '../entity/AlertDialogAttributeMapping';

/**
 * 警告对话框描述器类
 * 继承通用描述器，用于描述警告对话框组件的属性和行为
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class AlertDialogDescriptor extends CommonDescriptor {
  // 警告对话框对齐方式属性，控制对话框在屏幕上的显示位置
  public alertDialogAlignment: DialogAlignment = alertDialogAlignmentMapData.get('Default')!.value as DialogAlignment;

  /**
   * 转换属性方法
   * 将原始属性数组转换为警告对话框描述器的具体属性值
   * @param attributes 原始属性数组，包含组件的各种配置属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理对话框对齐方式属性
        case 'dialogAlignment':
          // 从映射数据中获取对应的对齐方式枚举值，如果不存在则保持原值
          this.alertDialogAlignment =
            alertDialogAlignmentMapData.get(attribute.currentValue)?.value as DialogAlignment ??
            this.alertDialogAlignment;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    })
  }
}
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入组件库服务
import { ComponentLibraryService } from '../service/ComponentLibraryService';
// 导入组件详情数据类型定义
import type { ComponentDetailData } from './ComponentDetailData';

// 定义标签常量
const TAG = '[ComponentDetailModel]';

// 定义组件详情模型类
export class ComponentDetailModel {
  // 定义静态实例变量
  private static instance: ComponentDetailModel;
  // 定义私有服务实例
  private service: ComponentLibraryService = new ComponentLibraryService();

  // 定义私有构造函数
  private constructor() {
  }

  // 定义获取实例的静态方法
  public static getInstance(): ComponentDetailModel {
    // 如果实例不存在则创建新实例
    if (!ComponentDetailModel.instance) {
      ComponentDetailModel.instance = new ComponentDetailModel();
    }
    // 返回实例
    return ComponentDetailModel.instance;
  }

  // 定义初始化方法，返回组件详情数据的Promise
  public init(id: number): Promise<ComponentDetailData> {
    // 首先尝试从偏好设置获取组件详情数据
    return this.service.getComponentDetailByPreference(id).then((data: ComponentDetailData) => {
      // 返回获取到的数据
      return data;
    }).catch((err: string) => {
      // 记录错误日志
      Logger.error(TAG, `get data from network failed! try to get data form preference. ${err}`);
      // 从网络获取组件详情数据
      return this.service.getComponentDetail(id)
        .then((data: ComponentDetailData) => {
          // 将数据保存到偏好设置
          this.service.setComponentDetailToPreference(id, data);
          // 返回数据
          return data;
        });
    });
  }
}
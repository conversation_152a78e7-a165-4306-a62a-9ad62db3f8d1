// 导入ArkUI工具包中的分段按钮相关类型
import { ItemRestriction, SegmentButton, SegmentButtonOptions, SegmentButtonTextItem } from '@kit.ArkUI';
// 导入切换按钮属性类型定义
import type { ToggleButtonAttribute } from '../viewmodel/ComponentDetailState';

// 使用Component装饰器定义切换按钮组件
@Component
export struct ToggleButtonComponent {
  // 使用ObjectLink装饰器链接属性对象
  @ObjectLink attribute: ToggleButtonAttribute;
  // 使用State和Watch装饰器定义选中索引状态，监听变化
  @State @Watch('onChangeSelectIndex') selectIndex: number[] =
    [this.attribute.selectOption.findIndex((item) => item?.text === this.attribute.currentValue)];
  // 使用State装饰器定义单选胶囊选项状态
  @State singleSelectCapsuleOptions: SegmentButtonOptions = SegmentButtonOptions.capsule({
    // 设置按钮选项
    buttons: this.attribute.selectOption as ItemRestriction<SegmentButtonTextItem>,
    // 设置为单选模式
    multiply: false,
    // 设置选中字体颜色
    selectedFontColor: $r('sys.color.font_primary'),
    // 设置字体颜色
    fontColor: $r('sys.color.font_secondary'),
    // 设置选中背景颜色
    selectedBackgroundColor: $r('sys.color.ohos_id_color_foreground_contrary_disable'),
  });
  // 定义回调函数
  callback: (name: string, value: string) => void = (name: string, value: string) => {
  };

  // 定义选中索引变化回调方法
  onChangeSelectIndex() {
    // 如果选中索引存在
    if (this.selectIndex) {
      // 获取第一个选中索引
      const index = this.selectIndex[0];
      // 调用回调函数传递属性名和选中文本
      this.callback(this.attribute.name, this.attribute.selectOption[index]?.text as string);
    }
  }

  // 定义构建方法
  build() {
    // 创建行布局
    Row() {
      // 显示属性显示名称文本
      Text(this.attribute.disPlayName)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_primary'))
      // 添加空白填充
      Blank()
      // 创建分段按钮
      SegmentButton({
        // 设置选项
        options: this.singleSelectCapsuleOptions,
        // 绑定选中索引
        selectedIndexes: $selectIndex
      }).width('60%')
    }
    // 设置行高度
    .height($r('app.float.common_component_height'))
    // 设置左右内边距
    .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
    // 设置垂直对齐方式为居中
    .alignItems(VerticalAlign.Center)
    // 设置宽度为100%
    .width('100%')
  }
}
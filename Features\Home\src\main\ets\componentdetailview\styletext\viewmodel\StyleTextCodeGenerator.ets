// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入高亮颜色映射数据
import { highlightColorMap } from '../../common/entity/CommonMapData';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入文本溢出类型映射数据
import { textOverflowTypeMapData } from '../../textarea/entity/TextAreaAttributeMapping';

/**
 * 样式文本组件代码生成器类
 * 实现通用代码生成器接口，用于生成样式文本组件代码
 */
export class StyleTextCodeGenerator implements CommonCodeGenerator {
  // 文本缩进值，默认为0
  private textIndent: number = 0;
  // 最大行数，默认为2行
  private maxLines: number = 2;
  // 溢出处理方式字符串，默认使用映射数据中的默认值
  private overflowStr: string = textOverflowTypeMapData.get('Default')!.code;
  // 高亮颜色字符串，默认使用映射数据中的默认值
  private highlightColor: string = highlightColorMap.get('Default')!.code;

  /**
   * 生成样式文本组件代码方法
   * 根据属性配置生成完整的样式文本组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的样式文本组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'textIndent':
          // 设置文本缩进值
          this.textIndent = Number(attribute.currentValue) as number;
          break;
        case 'maxLines':
          // 设置最大行数
          this.maxLines = Number(attribute.currentValue) as number;
          break;
        case 'overflow':
          // 设置溢出处理方式
          this.overflowStr =
            textOverflowTypeMapData.get(attribute.currentValue)?.code ?? textOverflowTypeMapData.get('Default')!.code;
          break;
        case 'highlightColor':
          // 设置高亮颜色
          this.highlightColor = attribute.currentValue;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });

    return `import { LengthMetrics } from '@kit.ArkUI';

@Component
struct StyleTextComponent {
  private text: string =
    'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA';
  textController: TextController = new TextController();
  paragraphStyleAttr: ParagraphStyle = new ParagraphStyle({
    textIndent: LengthMetrics.vp(${this.textIndent}),
    maxLines: ${this.maxLines},
    overflow: ${this.overflowStr}
  });
  textIndentStyle: SpanStyle = {
    start: 0,
    length: 3,
    styledKey: StyledStringKey.PARAGRAPH_STYLE,
    styledValue: this.paragraphStyleAttr
  };
  mutableStyledString: MutableStyledString =
    new MutableStyledString(this.text,
      [
        {
          start: 0,
          length: 3,
          styledKey: StyledStringKey.PARAGRAPH_STYLE,
          styledValue: this.paragraphStyleAttr
        },
        {
          start: 10,
          length: 5,
          styledKey: StyledStringKey.FONT,
          styledValue: new TextStyle({
            fontColor: '${this.highlightColor}'
          })
        }
      ]);

  build() {
    Column() {
      Text(undefined, { controller: this.textController }) {
      }
      .margin({ left: 36, right: 36 })
      .fontSize($r('sys.float.Body_L'))
      .fontWeight(FontWeight.Regular)
    }
    .width(320)
    .justifyContent(FlexAlign.Center)
    .onAttach(() => {
      this.textController.setStyledString(this.mutableStyledString);
    })
  }
 }`;
  }
}
// 导入字符串工具类，用于字符串处理
import { StringUtil } from '../../../util/StringUtil';
// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入图片相关的属性映射数据
import { filterStyleMapData, imageFitStyleMapData } from '../entity/ImageAttributeMapping';

/**
 * 图片代码生成器类
 * 实现通用代码生成器接口，用于生成图片组件的代码
 */
export class ImageCodeGenerator implements CommonCodeGenerator {
  // 私有对象适配属性，默认使用默认值
  private objectFit: string = imageFitStyleMapData.get('Default')!.code;
  // 私有裁剪属性，默认为false
  private clip: boolean = false;
  // 私有颜色滤镜矩阵字符串，默认使用默认值
  private colorFilterMatrixStr: string = filterStyleMapData.get('Default')!.code;
  // 私有颜色滤镜矩阵数组，将字符串转换为数组
  private colorFilterMatrix: number[] = StringUtil.stringToArray(this.colorFilterMatrixStr);

  /**
   * 生成图片组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的图片组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性进行处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理对象适配属性
        case 'objectFit':
          this.objectFit =
            imageFitStyleMapData.get(attribute.currentValue)?.code ?? imageFitStyleMapData.get('Default')!.code;
          break;
        case 'colorFilterMatrixStr':
          this.colorFilterMatrixStr =
            filterStyleMapData.get(attribute.currentValue)?.code ?? filterStyleMapData.get('Default')!.code;
          this.colorFilterMatrix = StringUtil.stringToArray(this.colorFilterMatrixStr);
          break;
        case 'clip':
          this.clip = JSON.parse(attribute.currentValue);
          break;
        default:
          break;
      }
    });
    return `@Component
struct ImageComponent {
  build() {
    Column() {
      // You need to place a PNG format image in the media folder.
      Image($r('app.media.image_src340'))
        .colorFilter(new ColorFilter([${this.colorFilterMatrix}]))
        .objectFit(${this.objectFit})
    }
    .borderRadius($r('sys.float.corner_radius_level8'))
    .width('200vp')
    .height('140vp')
    .clip(${this.clip})
  }
}`;
  }
}
// 导入性能分析包中的hilog模块
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 日志工具类
 * 封装HarmonyOS的hilog功能，提供统一的日志输出接口
 * 支持debug、info、warn、error四种日志级别
 */
class Logger {
  // 日志域，用于标识日志来源
  private domain: number;
  // 日志前缀，用于标识日志标签
  private prefix: string;
  // 日志格式字符串
  private format: string = '%{public}s, %{public}s';

  /**
   * 构造函数
   * 初始化日志工具实例
   * @param prefix 日志前缀标签
   */
  public constructor(prefix: string) {
    // 设置日志前缀
    this.prefix = prefix;
    // 设置日志域为0xFF00
    this.domain = 0xFF00;
  }

  /**
   * 输出调试级别日志方法
   * 用于输出调试信息，通常在开发阶段使用
   * @param args 要输出的日志参数，支持多个参数
   */
  public debug(...args: Object[]): void {
    // 调用hilog的debug方法输出调试日志
    hilog.debug(this.domain, this.prefix, this.format, args);
  }

  /**
   * 输出信息级别日志方法
   * 用于输出一般信息，记录程序运行状态
   * @param args 要输出的日志参数，支持多个参数
   */
  public info(...args: Object[]): void {
    // 调用hilog的info方法输出信息日志
    hilog.info(this.domain, this.prefix, this.format, args);
  }

  /**
   * 输出警告级别日志方法
   * 用于输出警告信息，提示潜在问题
   * @param args 要输出的日志参数，支持多个参数
   */
  public warn(...args: Object[]): void {
    // 调用hilog的warn方法输出警告日志
    hilog.warn(this.domain, this.prefix, this.format, args);
  }

  /**
   * 输出错误级别日志方法
   * 用于输出错误信息，记录程序异常情况
   * @param args 要输出的日志参数，支持多个参数
   */
  public error(...args: Object[]): void {
    // 调用hilog的error方法输出错误日志
    hilog.error(this.domain, this.prefix, this.format, args);
  }
}

// 导出默认的Logger实例，使用'[HMOSWorld]'作为日志前缀
export default new Logger('[HMOSWorld]');
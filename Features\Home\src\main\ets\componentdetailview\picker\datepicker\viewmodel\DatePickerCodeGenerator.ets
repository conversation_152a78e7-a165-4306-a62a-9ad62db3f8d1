// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';
// 导入通用字体颜色映射和字体粗细映射数据
import { commonFontColorMap, fontWeightMapData } from '../../../common/entity/CommonMapData';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../../viewmodel/CommonCodeGenerator';

/**
 * 日期选择器代码生成器类
 * 实现通用代码生成器接口，用于生成日期选择器组件代码
 */
export class DatePickerCodeGenerator implements CommonCodeGenerator {
  // 是否显示农历，默认为false
  private lunar: boolean = false;
  // 选中文本颜色，默认使用通用字体颜色
  private selectedTextColor: ResourceColor = commonFontColorMap.get('Default')!.code;
  // 选中文本字体大小，默认为20
  private selectedFontSize: number = 20;
  // 选中文本字体粗细，默认使用默认字体粗细
  private selectedFontWeight: string = fontWeightMapData.get('Default')!.code;
  // 普通文本字体大小，默认为16
  private fontSize: number = 16;

  /**
   * 生成日期选择器代码方法
   * 根据属性配置生成完整的日期选择器组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的日期选择器代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'lunar':
          // 设置是否显示农历
          this.lunar = JSON.parse(attribute.currentValue);
          break;
        case 'selectedTextColor':
          // 设置选中文本颜色
          this.selectedTextColor = attribute.currentValue;
          break;
        case 'selectedFontSize':
          // 设置选中文本字体大小
          this.selectedFontSize = Number(attribute.currentValue);
          break;
        case 'selectedFontWeight':
          // 设置选中文本字体粗细
          this.selectedFontWeight =
            fontWeightMapData.get(attribute.currentValue)?.code ?? fontWeightMapData.get('Default')!.code;
          break;
        case 'fontSize':
          // 设置普通文本字体大小
          this.fontSize = Number(attribute.currentValue);
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整日期选择器代码字符串
    return `// 日期选择器组件
// 提供日期选择功能，支持农历显示和自定义文本样式
@Component
struct DatePickerComponent {
  // 构建组件UI方法
  build() {
    // 创建列布局容器
    Column() {
      // 创建日期选择器组件
      DatePicker({
      // 设置开始日期为1970年1月1日
      start: new Date('1970-1-1'),
      // 设置结束日期为2100年1月1日
      end: new Date('2100-1-1'),
      // 设置默认选中日期为2021年8月8日
      selected: new Date('2021-08-08')
    })
      // 设置组件边距
      .margin({
        left: $r('sys.float.padding_level12'),
        right: $r('sys.float.padding_level12'),
        top: $r('sys.float.padding_level8'),
        bottom: $r('sys.float.padding_level8')
      })
      // 设置是否显示农历
      .lunar(${this.lunar})
      // 设置选中文本样式
      .selectedTextStyle({
        color: '${this.selectedTextColor}',
        font: {
          size: ${this.selectedFontSize},
          weight: ${this.selectedFontWeight}
        }
      })
      // 设置普通文本样式
      .textStyle({
        color: $r('sys.color.font_primary'),
        font: {
          size: ${this.fontSize},
          weight: FontWeight.Regular
        }
      })
    }
    // 设置列布局宽度为100%
    .width('100%')
    // 设置列布局高度为100%
    .height('100%')
    // 设置列布局垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
  }
}`;
  }
}
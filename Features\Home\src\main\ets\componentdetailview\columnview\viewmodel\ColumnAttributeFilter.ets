// 导入通用模块中的观察数组类型，用于处理响应式数组
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器基类，用于实现属性过滤接口
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 列属性过滤器类
 * 实现通用属性过滤器接口，用于根据列组件的属性值动态控制其他属性的可用性
 */
export class ColumnAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据列组件属性的当前值，动态启用或禁用相关属性
   * @param attributes 观察数组类型的属性列表，包含所有可配置的属性
   */
  public filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，查找需要处理的属性类型
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理内边距属性
        case 'padding':
          // 查找内边距数值属性在数组中的索引位置
          const paddingIndex = attributes.findIndex((item) => item.name === 'paddingNum');
          // 查找弹性对齐属性在数组中的索引位置
          const flexAlignIndex = attributes.findIndex((item) => item.name === 'flexAlign');
          // 如果相关属性都存在
          if (paddingIndex !== -1 && flexAlignIndex !== -1) {
            // 如果内边距设置为无
            if (attribute.currentValue === 'None') {
              // 禁用内边距数值属性（无内边距时不需要设置数值）
              attributes[paddingIndex].enable = false;
              // 启用弹性对齐属性（无内边距时可以使用弹性对齐）
              attributes[flexAlignIndex].enable = true;
            } else {
              // 有内边距时启用内边距数值属性
              attributes[paddingIndex].enable = true;
              // 有内边距时禁用弹性对齐属性（避免冲突）
              attributes[flexAlignIndex].enable = false;
            }
          }
          break;
        // 处理弹性对齐属性
        case 'flexAlign':
          // 查找间距属性在数组中的索引位置
          const spaceIndex = attributes.findIndex((item) => item.name === 'space');
          // 如果间距属性存在
          if (spaceIndex !== -1) {
            // 如果弹性对齐设置为SpaceBetween（元素间等距分布）
            if (attribute.currentValue === 'SpaceBetween') {
              // 禁用间距属性（SpaceBetween会自动处理间距）
              attributes[spaceIndex].enable = false;
            } else {
              // 其他对齐方式启用间距属性
              attributes[spaceIndex].enable = true;
            }
          }
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入ArkGraphics2D工具包中的显示同步模块
import { displaySync } from '@kit.ArkGraphics2D';
// 导入通用模块中的常量、加载状态、顶部导航视图、Web工具和窗口工具
import {
  CommonConstants,
  LoadingStatus,
  TopNavigationView,
  WebUtil,
  WindowUtil,
} from '@ohos/common';
// 导入通用业务模块中的基础详情组件和组件详情参数
import { BaseDetailComponent, ComponentDetailParams } from '@ohos/commonbusiness';
// 导入组件详情事件、组件详情事件类型、组件详情页面视图模型和初始化组件事件
import {
  ComponentDetailEvent,
  ComponentDetailEventType,
  ComponentDetailPageVM,
  InitComponentEvent,
} from '../viewmodel/ComponentDetailPageVM';
// 导入详情内容视图
import { DetailContentView } from '../component/DetailContentView';
// 导入组件详情状态类型定义
import type { ComponentDetailState } from '../viewmodel/ComponentDetailState';
// 导入组件详情管理器
import { ComponentDetailManager } from '../viewmodel/ComponentDetailManager';
// 导入代码预览JavaScript工具
import { CodePreviewJSUtil } from '../util/CodePreviewJSUtil';
// 导入推荐数据
import { RecommendData } from '../model/ComponentDetailData';

// 使用Component装饰器定义组件详情视图，设置非活跃时冻结
@Component({ freezeWhenInactive: true })
export struct ComponentDetailView {
  // 使用StorageProp装饰器获取系统颜色模式
  @StorageProp('systemColorMode') systemColorMode: ConfigurationConstant.ColorMode = AppStorage.get('systemColorMode')!;
  // 使用StorageLink装饰器链接Web加载状态，并监听变化
  @StorageLink('webIsLoading') @Watch('sendCodeToWeb') webIsLoading: boolean = false;
  // 使用Prop装饰器定义组件名称属性
  @Prop componentName: string = '';
  // 使用Prop装饰器定义组件ID属性
  @Prop componentId: number = 0;
  // 定义私有显示同步对象
  private displaySync = displaySync.create();
  // 定义私有视图模型
  private viewModel?: ComponentDetailPageVM;
  // 使用State装饰器定义组件详情状态
  @State componentDetailState?: ComponentDetailState = this.viewModel?.getState();
  // 使用State装饰器定义加载状态
  @State loadingStatus: LoadingStatus = LoadingStatus.IDLE;

  // 定义组件即将出现时的回调方法
  aboutToAppear(): void {
    // 判断是否为系统深色模式
    const isSystemDark: boolean = (this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this), isSystemDark);
  }

  // 定义组件即将消失时的回调方法
  aboutToDisappear(): void {
    // 等待移除持续时间后执行清理操作
    CommonConstants.PROMISE_WAIT(CommonConstants.REMOVE_DURATION).then(() => {
      // 遍历推荐数据并移除对应的Web节点
      this.componentDetailState?.recommends.forEach((item: RecommendData) => {
        WebUtil.removeNode(item.url);
      });
    });
  }

  // 使用Builder装饰器定义详情内容构建器
  @Builder
  DetailContentBuilder() {
    // 创建详情内容视图
    DetailContentView({
      // 传递组件详情状态
      componentDetailState: this.componentDetailState,
      // 传递组件名称
      componentName: this.componentName,
    })
      // 设置布局权重为1
      .layoutWeight(1)
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建顶部导航视图
    TopNavigationView({
      // 传递顶部导航数据
      topNavigationData: this.componentDetailState?.topNavigationData,
    })
  }

  // 定义构建方法
  build() {
    // 创建导航目标组件
    NavDestination() {
      // 创建基础详情组件
      BaseDetailComponent({
        // 设置详情内容视图构建器
        detailContentView: () => {
          this.DetailContentBuilder()
        },
        // 设置顶部标题视图构建器
        topTitleView: () => {
          this.TopTitleViewBuilder()
        },
        // 传递加载状态
        loadingStatus: this.loadingStatus,
      })
        // 扩展安全区域包括键盘和系统区域
        .expandSafeArea([SafeAreaType.KEYBOARD, SafeAreaType.SYSTEM])
    }
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置高度为100%
    .height('100%')
    // 设置背景颜色为系统次要背景色
    .backgroundColor($r('sys.color.background_secondary'))
    // 设置消失回调
    .onDisAppear(() => {
      this.displaySync.stop();
    })
    // 设置准备就绪回调
    .onReady((ctx: NavDestinationContext) => {
      // 获取导航参数
      const params: ComponentDetailParams = ctx.pathInfo.param as ComponentDetailParams;
      // 设置组件名称
      this.componentName = params.componentName as string;
      // 设置组件ID
      this.componentId = Number(params.componentId);
      // 等待500毫秒后分配视图数据
      CommonConstants.PROMISE_WAIT(500).then(() => {
        this.assignViewData();
      });
    })
  }

  // 定义分配视图数据方法
  assignViewData() {
    // 从组件详情管理器获取详情视图模型
    this.viewModel = ComponentDetailManager.getInstance().getDetailViewModel(this.componentName);
    // 如果视图模型不存在则创建新的
    if (!this.viewModel) {
      this.viewModel = new ComponentDetailPageVM(this.componentName);
    }
    // 定义期望帧率范围
    const range: ExpectedFrameRateRange = { expected: 120, min: 60, max: 120 };
    // 设置显示同步的期望帧率范围
    this.displaySync.setExpectedFrameRateRange(range);
    // 初始化帧计数器
    let frameCount = 0;
    // 定义事件列表
    const eventList: ComponentDetailEventType[] = [
      ComponentDetailEventType.INIT_DESCRIPTOR,
      ComponentDetailEventType.INIT_RECOMMEND,
      ComponentDetailEventType.WEB_CODE_EVENT,
    ];
    // 获取事件数量
    const eventCount = eventList.length;
    // 发送初始化组件事件
    this.viewModel?.sendEvent(new InitComponentEvent(this.componentId))?.then(() => {
      // 获取组件详情状态
      this.componentDetailState = this.viewModel?.getState();
      // 设置加载状态为加载中
      this.loadingStatus = LoadingStatus.LOADING;
      // 监听帧事件
      this.displaySync.on('frame', () => {
        // 发送组件详情事件
        const promise = this.viewModel?.sendEvent(new ComponentDetailEvent(eventList[frameCount]));
        // 更新组件详情状态
        this.componentDetailState = this.viewModel?.getState();
        // 增加帧计数
        frameCount++;
        // 检查是否完成所有事件
        if (frameCount >= eventCount) {
          // 等待Promise完成后停止显示同步并刷新代码预览Web
          promise?.then(() => {
            this.displaySync.stop();
            this.refreshCodePreviewWeb();
          });
        }
      });
      // 启动显示同步
      this.displaySync.start();
    });
  }

  // 定义刷新代码预览Web方法
  refreshCodePreviewWeb() {
    // 设置Web加载状态为true
    this.webIsLoading = true;
    // 刷新Web控制器
    WebUtil.getWebController(WebUtil.getComponentCodeUrl())?.refresh();
  }

  // 定义发送代码到Web方法
  sendCodeToWeb() {
    // 检查加载状态为加载中且Web未加载
    if (this.loadingStatus === LoadingStatus.LOADING && !this.webIsLoading) {
      // 定义代码转HTML方法名
      const codeToHtmlMethod: string = 'codeToHtml(%param)';
      // 构建参数字符串
      const params: string = `${JSON.stringify(this.componentDetailState?.code)}, ${this.systemColorMode}`;
      // 运行JavaScript方法
      CodePreviewJSUtil.codeViewRunJS(codeToHtmlMethod, params, () => {
        this.loadingStatus = LoadingStatus.SUCCESS;
      });
    }
  }
}

// 使用Builder装饰器定义组件详情构建器函数
@Builder
export function ComponentDetailBuilder() {
  // 创建组件详情视图
  ComponentDetailView()
}
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入评分组件相关映射数据
import { indicatorMapData, ratingValueMapData, starsMapData } from '../entity/RatingAttributeMapping';

/**
 * 评分组件代码生成器类
 * 实现通用代码生成器接口，用于生成评分组件代码
 */
export class RatingCodeGenerator implements CommonCodeGenerator {
  // 评分值，默认使用映射数据中的默认值
  private rating: string = ratingValueMapData.get('Default')!.code;
  // 指示器模式，默认使用映射数据中的默认值
  private indicator: string = indicatorMapData.get('Default')!.code;
  // 星星数量，默认使用映射数据中的默认值
  private stars: string = starsMapData.get('Default')!.code;
  // 步长值，默认为0.5
  private stepSize: string = '0.5';

  /**
   * 生成评分组件代码方法
   * 根据属性配置生成完整的评分组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的评分组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 星星样式代码段，默认为空
    let codeSegment = ``;
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'rating':
          // 设置评分值
          this.rating = attribute.currentValue;
          break;
        case 'indicator':
          // 设置指示器模式，转换为小写
          this.indicator = attribute.currentValue.toLowerCase();
          break;
        case 'stars':
          // 设置星星数量
          this.stars = attribute.currentValue;
          break;
        case 'starStyle':
          // 设置星星样式，根据布尔值生成不同的样式代码
          const bool = attribute.currentValue.toLowerCase() === 'true';
          codeSegment = bool ? `{
        // 需要在media文件夹中替换星星样式资源图片
        backgroundUri: '/resources/base/media/rating_background.png',
        foregroundUri: '/resources/base/media/rating_foreground.png',
      }` : `{
        backgroundUri: undefined,
        foregroundUri: undefined,
      }`;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整评分组件代码字符串
    return `// 评分组件
// 提供星级评分功能，支持指示器模式和自定义样式
@Component
struct RatingComponent {
  // 构建组件UI方法
  build() {
    // 创建评分组件
    Rating({ rating: ${this.rating}, indicator: ${this.indicator}})
      // 设置星星数量
      .stars(${this.stars})
      // 设置步长值
      .stepSize(${this.stepSize})
      // 设置星星样式
      .starStyle(${codeSegment})
  }
}`;
  }
}
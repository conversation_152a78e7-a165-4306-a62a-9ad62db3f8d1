// 导入能力工具包中的通用类型和Want类型
import type { common, Want } from '@kit.AbilityKit';
// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的应用包信息数据类型
import type { BundleInfoData } from '@ohos/common';
// 导入通用模块中的基础视图模型、基础视图模型事件、配置映射键、日志记录器、资源工具和更新服务
import { BaseVM, BaseVMEvent, ConfigMapKey, Logger, ResourceUtil, UpdateService } from '@ohos/common';
// 导入关于页面状态
import { AboutState } from './AboutState';

// 定义日志标签
const TAG: string = '[AboutVM]';

// 定义关于页面视图模型类
export class AboutVM extends BaseVM<AboutState> {
  // 定义静态实例
  private static instance: AboutVM;
  // 创建更新服务实例
  private updateService = UpdateService.getInstance();

  // 定义获取实例的静态方法
  public static getInstance(): AboutVM {
    // 如果实例不存在则创建新实例
    if (!AboutVM.instance) {
      AboutVM.instance = new AboutVM();
    }
    // 返回实例
    return AboutVM.instance;
  }

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数并传入关于页面状态
    super(new AboutState());
  }

  // 定义发送事件方法
  sendEvent(event: BaseVMEvent) {
    // 如果是检查版本事件
    if (event instanceof CheckVersionEvent) {
      // 执行检查版本方法
      this.checkVersion();
    } else if (event instanceof UpdateVersionEvent) {
      // 如果是更新版本事件则执行更新版本方法
      this.updateVersion();
    } else if (event instanceof ViewRegistrationInfoEvent) {
      // 如果是查看注册信息事件则获取上下文并跳转到浏览器
      const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
      this.jumpToBrowser(context);
    }
  }

  // 定义检查版本的私有方法
  private checkVersion(): void {
    // 调用更新服务检查更新
    this.updateService.checkUpdate().then((existNewVersion: boolean) => {
      // 设置是否存在更新版本
      this.state.laterVersionExist = existNewVersion;
      // 设置当前版本号
      this.state.currentVersion = AppStorage.get<BundleInfoData>('BundleInfoData')?.versionName as string;
    });
  }

  // 定义更新版本的私有方法
  private updateVersion(): void {
    // 设置正在加载更新状态为true
    this.state.isLoadingUpdate = true;
    // 调用更新服务更新版本
    this.updateService.updateVersion().then(() => {
      // 更新完成后设置正在加载更新状态为false
      this.state.isLoadingUpdate = false;
    });
  }

  // 定义跳转到浏览器的私有方法
  private jumpToBrowser(context: common.UIAbilityContext): void {
    // 创建Want对象
    const want: Want = {
      // 设置动作为查看数据
      action: 'ohos.want.action.viewData',
      // 设置实体为可浏览
      entities: ['entity.system.browsable'],
      // 设置URI为MIIT网址
      uri: ResourceUtil.getRawFileStringByKey(getContext(), ConfigMapKey.MIIT_URL),
    };
    // 启动能力
    context.startAbility(want)
      .then(() => {
        // 成功时记录日志
        Logger.info(TAG, 'Start browsableAbility successfully.');
      })
      .catch((err: BusinessError) => {
        // 失败时记录错误日志
        Logger.error(TAG, `Failed to startAbility. Code: ${err.code}, message: ${err.message}`);
      });
  }
}

// 定义检查版本事件类
export class CheckVersionEvent implements BaseVMEvent {
}

// 定义更新版本事件类
export class UpdateVersionEvent implements BaseVMEvent {
}

// 定义查看注册信息事件类
export class ViewRegistrationInfoEvent implements BaseVMEvent {
}
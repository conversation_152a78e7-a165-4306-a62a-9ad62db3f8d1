// 导入通用业务模块中的标签栏类型枚举
import { TabBarType } from '@ohos/commonbusiness';

/**
 * 标签栏数据接口
 * 定义标签栏项的数据结构
 */
export interface TabBarData {
  // 标签ID，使用TabBarType枚举
  id: TabBarType;
  // 标签标题，支持资源字符串
  title: ResourceStr;
  // 标签图标，使用系统资源
  icon: Resource;
}

/**
 * 标签列表常量
 * 定义应用中所有标签页的配置数据
 * 包含首页、安全、社区、我的四个标签
 */
export const TABS_LIST: TabBarData[] = [
  {
    // 首页标签配置
    id: TabBarType.HOME,
    // 使用系统房屋填充图标
    icon: $r('sys.symbol.house_fill'),
    // 首页标题资源
    title: $r('app.string.tab_home'),
  },
  {
    // 安全标签配置（对应示例模块）
    id: TabBarType.SAMPLE,
    // 使用系统钥匙盾牌填充图标
    icon: $r('sys.symbol.key_shield_fill'),
    // 安全标题资源
    title: $r('app.string.tab_security'),
  },
  {
    // 社区标签配置（对应实践模块）
    id: TabBarType.PRACTICE,
    // 使用系统发现填充图标
    icon: $r('sys.symbol.discover_fill'),
    // 社区标题资源
    title: $r('app.string.tab_community'),
  },
  {
    // 我的标签配置
    id: TabBarType.MINE,
    // 使用系统个人头像圆圈填充图标
    icon: $r('sys.symbol.person_crop_circle_fill_1'),
    // 我的标题资源
    title: $r('app.string.tab_mine'),
  },
]
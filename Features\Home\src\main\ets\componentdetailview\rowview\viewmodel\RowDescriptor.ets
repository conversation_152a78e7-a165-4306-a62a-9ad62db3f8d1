// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入行布局组件相关映射数据
import {
  paddingNumMapData,
  rowAlignMapData,
  rowJustifyContentMapData,
  rowPaddingMapData,
  rowSpaceMapData,
} from '../entity/RowAttributeMapping';

/**
 * 行布局组件描述器类
 * 继承通用描述器，专门用于描述行布局组件的配置
 * 包含交叉轴对齐、主轴对齐、间距、内边距等配置信息
 */
@Observed
export class RowDescriptor extends CommonDescriptor {
  // 交叉轴对齐方式，默认使用映射数据中的默认值
  public alignItems: VerticalAlign = rowAlignMapData.get('Default')!.value;
  // 主轴对齐方式，默认使用映射数据中的默认值
  public flexAlign: FlexAlign = rowJustifyContentMapData.get('Default')!.value;
  // 子组件间距，默认使用映射数据中的默认值
  public space: number = rowSpaceMapData.get('Default')!.value;
  // 内边距类型，默认使用映射数据中的默认值
  public padding: string = rowPaddingMapData.get('Default')!.value;
  // 内边距数值，默认使用映射数据中的默认值
  public paddingNum: number = paddingNumMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为行布局组件的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'alignItems':
          // 设置交叉轴对齐方式
          this.alignItems = rowAlignMapData.get(attribute.currentValue)?.value ?? this.alignItems;
          break;
        case 'flexAlign':
          // 设置主轴对齐方式
          this.flexAlign = rowJustifyContentMapData.get(attribute.currentValue)?.value ?? this.flexAlign;
          break;
        case 'space':
          // 设置子组件间距，转换为数字类型
          this.space = Number(attribute.currentValue);
          break;
        case 'padding':
          // 设置内边距类型
          this.padding = attribute.currentValue;
          break;
        case 'paddingNum':
          // 设置内边距数值，转换为数字类型
          this.paddingNum = Number(attribute.currentValue);
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
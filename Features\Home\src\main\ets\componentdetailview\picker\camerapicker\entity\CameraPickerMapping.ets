// 导入相机选择器模块，用于定义媒体类型
import { cameraPicker } from '@kit.CameraKit';

/**
 * 相机媒体类型映射类
 * 用于映射相机媒体类型的代码和值
 */
class CameraMediaType {
  // 代码字符串，用于代码生成
  public code: string;
  // 媒体类型数组，用于实际应用
  public value: cameraPicker.PickerMediaType[];

  /**
   * 构造函数
   * @param code 代码字符串
   * @param value 媒体类型数组
   */
  constructor(code: string, value: cameraPicker.PickerMediaType[]) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 相机选择器媒体类型映射数据
 * 包含所有支持的相机媒体类型及其对应的映射关系
 */
export const pickerMediaType: Map<string, CameraMediaType> = new Map([
  // 默认模式：支持拍照和录制
  ['Default', new CameraMediaType('[cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO]',
    [cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO])],
  // 拍照模式：仅支持拍照
  ['拍照模式', new CameraMediaType('[cameraPicker.PickerMediaType.PHOTO]', [cameraPicker.PickerMediaType.PHOTO])],
  // 录制模式：仅支持录制
  ['录制模式', new CameraMediaType('[cameraPicker.PickerMediaType.VIDEO]', [cameraPicker.PickerMediaType.VIDEO])],
  // 混合模式：支持拍照和录制
  ['混合模式', new CameraMediaType('[cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO]',
    [cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO])],
]);
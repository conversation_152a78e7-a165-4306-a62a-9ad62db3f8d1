// 导入ArkUI工具包中的弹窗组件，用于显示提示信息
import { Popup } from '@kit.ArkUI';
// 导入通用模块中的偏好设置管理器，用于存储和读取用户偏好
import { PreferenceManager } from '@ohos/common';
// 导入详情页面常量，用于获取Grid布局的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入通用存储键，用于偏好设置的键值管理
import { CommonStorageKey } from '../../common/entity/CommonStorageKey';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入Grid属性修改器，用于动态修改Grid组件属性
import { GridAttributeModifier } from '../viewmodel/GridAttributeModifier';
// 导入Grid描述器类型，用于获取Grid布局的属性配置
import type { GridDescriptor } from '../viewmodel/GridDescriptor';
// 导入像素图构建器，用于拖拽时创建拖拽图像
import { pixelMapBuilder } from './PixelMapBuilder';
// 导入Grid数据获取函数和项目数据类，用于管理Grid中的数据
import { getData, ItemData } from '../entity/GridAttributeMapping';

/**
 * Grid布局构建器函数
 * 用于构建可配置的Grid布局组件，展示不同的Grid属性效果
 * @param $$ 描述器包装对象，包含Grid布局的配置信息
 */
@Builder
export function GridBuilder($$: DescriptorWrapper) {
  // 创建Grid组件实例，传入Grid描述器作为预览配置
  GridComponent({ preView: $$.descriptor as GridDescriptor });
}

// 拖拽时的缩放比例常量，用于放大拖拽项目
const DRAG_RATIO: number = 1.1;

/**
 * Grid组件
 * 实现可拖拽的网格布局，支持属性动态配置和用户交互
 */
@Component
struct GridComponent {
  // 列表数据状态，存储Grid中显示的所有项目
  @State listData: string[] = [];
  // 弹窗显示状态，控制编辑提示弹窗的显示
  @State showPopup: boolean = true;
  // 项目数据，存储拖拽项目的尺寸和文本信息
  @State itemData: ItemData = new ItemData();
  // Grid描述器属性，从父组件传入的Grid配置信息
  @Prop preView: GridDescriptor;

  /**
   * 组件即将出现时的生命周期方法
   * 初始化弹窗显示状态和列表数据
   */
  aboutToAppear(): void {
    // 从偏好设置中获取Grid提示弹窗的显示状态
    PreferenceManager.getInstance().getValue<boolean>(CommonStorageKey.KEY_GRID_TIP).then((value) => {
      // 如果没有设置过，默认显示弹窗
      this.showPopup = value ?? true;
    });
    // 获取Grid的初始数据
    this.listData = getData();
  }

  /**
   * 自定义弹窗构建器方法
   * 创建编辑提示弹窗，指导用户如何使用Grid的拖拽功能
   */
  @Builder
  MyPopup() {
    // 创建行容器包装弹窗
    Row() {
      // 创建弹窗组件
      Popup({
        // 设置弹窗标题
        title: {
          text: $r('app.string.edit'),
        },
        // 设置弹窗消息内容
        message: {
          text: $r('app.string.editTip')
        },
        // 显示关闭按钮
        showClose: true,
        // 关闭按钮点击回调
        onClose: () => {
          // 隐藏弹窗
          this.showPopup = false;
        },
        // 设置弹窗按钮
        buttons: [
          {
            // 确认按钮文本
            text: $r('app.string.confirmTip'),
            // 确认按钮点击回调
            action: () => {
              // 隐藏弹窗
              this.showPopup = false;
            },
          },
          {
            // 取消按钮文本
            text: $r('app.string.cancelTip'),
            // 取消按钮点击回调
            action: () => {
              // 保存用户选择，下次不再显示提示
              PreferenceManager.getInstance().setValue(CommonStorageKey.KEY_GRID_TIP, false);
              // 隐藏弹窗
              this.showPopup = false;
            },
          },
        ],
      })
    }
    // 处理键盘预输入事件，用于方向键导航
    .onKeyPreIme((keyEvent: KeyEvent) => {
      // 如果是左右方向键按下事件
      if ((keyEvent?.keyText === 'KEYCODE_DPAD_RIGHT' || keyEvent?.keyText === 'KEYCODE_DPAD_LEFT') &&
        keyEvent.type === KeyType.Down) {
        // 返回true表示消费该事件
        return true;
      }
      // 返回false表示不消费该事件
      return false;
    })
  }

  /**
   * 构建组件UI方法
   * 创建包含Grid布局的完整界面，支持拖拽和属性配置
   */
  build() {
    // 创建列容器作为根布局
    Column() {
      // 创建Grid组件，使用描述器中的滚动控制器
      Grid((this.preView as GridDescriptor).scroller) {
        // 遍历列表数据，为每个项目创建GridItem
        ForEach(this.listData, (item: string, index: number) => {
          // 创建Grid项目
          GridItem() {
            // 创建文本组件显示项目内容
            Text(item)
              // 设置字体大小
              .fontSize($r('sys.float.Body_L'))
              // 设置字体颜色
              .fontColor($r('sys.color.icon_emphasize'))
              // 设置文本居中对齐
              .textAlign(TextAlign.Center)
              // 设置右边距
              .margin({ right: $r('sys.float.padding_level2') })
          }
          // 设置背景颜色
          .backgroundColor($r('sys.color.comp_background_primary'))
          // 监听区域变化事件，用于更新拖拽项目的尺寸
          .onAreaChange((_oldValue: Area, newValue: Area) => {
            // 根据拖拽比例更新项目宽度
            this.itemData.width = (newValue.width as number) * DRAG_RATIO;
            // 根据拖拽比例更新项目高度
            this.itemData.height = (newValue.height as number) * DRAG_RATIO;
          })
          // 设置圆角
          .borderRadius($r('sys.float.corner_radius_level4'))
          // 设置边框样式
          .border({ width: $r('app.float.border_width_large'), color: $r('sys.color.comp_background_emphasize') })
          // 绑定弹窗，只在第一个项目上显示
          .bindPopup(this.showPopup && index === 0, {
            // 弹窗构建器
            builder: this.MyPopup(),
            // 弹窗宽度
            width: $r('app.float.water_flow_width'),
            // 背景模糊样式
            backgroundBlurStyle: BlurStyle.COMPONENT_ULTRA_THICK,
            // 圆角半径
            radius: ($r('sys.float.corner_radius_level4')),
            // 不显示遮罩
            mask: false,
            // 可获取焦点
            focusable: true,
            // 弹窗颜色
            popupColor: $r('sys.color.ohos_id_blur_style_component_regular_color'),
            // Y轴偏移量
            offset: { y: DetailPageConstant.GRID_POPUP_OFFSET_Y },
            // 弹窗位置在底部
            placement: Placement.Bottom
          })
        }, (item: string) => item)
      }
      // 监听项目拖拽开始事件
      .onItemDragStart((_event: ItemDragInfo, itemIndex: number) => {
        // 设置拖拽项目的文本内容
        this.itemData.text = this.listData[itemIndex];
        // 返回拖拽时显示的像素图
        return pixelMapBuilder(this.itemData);
      })
      // 监听项目拖拽放置事件
      .onItemDrop((_event: ItemDragInfo, itemIndex: number, insertIndex: number, isSuccess: boolean): void => {
        // 如果拖拽失败或目标索引超出范围，直接返回
        if (!isSuccess || insertIndex >= this.listData.length) {
          return;
        }
        // 交换拖拽项目和目标项目的位置
        const temp: string = this.listData[itemIndex];
        this.listData[itemIndex] = this.listData[insertIndex];
        this.listData[insertIndex] = temp;
      })
      // 应用Grid属性修改器，动态设置Grid的属性
      .attributeModifier(new GridAttributeModifier(this.preView as GridDescriptor))
    }
    // 设置列容器高度为100%
    .height('100%')
    // 设置列容器宽度为100%
    .width('100%')
    // 设置内边距
    .padding($r('sys.float.padding_level3'))
    // 设置对齐方式为居中
    .align(Alignment.Center)
  }
}
/**
 * 卡片内容类
 * 用于存储卡片内部的具体内容项信息
 * 包含媒体资源、标题、描述等详细信息
 */
export class CardContent {
  // 内容项唯一标识ID
  public id: number = 0;
  // 内容类型，使用CardTypeEnum枚举
  public type: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 媒体类型，默认为图片类型
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  // 媒体资源URL地址
  public mediaUrl: string = '';
  // 内容标题
  public title: string = '';
  // 内容副标题
  public subTitle: string = '';
  // 内容描述信息
  public desc: string = '';
  // 详情页面URL地址，可选
  public detailsUrl?: string = '';
  // 原始URL地址，可选
  public originalUrl?: string = '';
}

/**
 * 卡片数据类
 * 用于存储卡片的基本信息和内容列表
 * 包含卡片标题、样式类型、版本等属性
 */
export class CardData {
  // 卡片唯一标识ID
  public id: number = 0;
  // 卡片主标题
  public cardTitle: string = '';
  // 卡片副标题
  public cardSubTitle: string = '';
  // 卡片类型，使用CardTypeEnum枚举
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 卡片样式类型，默认为列表样式
  public cardStyleType: CardStyleTypeEnum = CardStyleTypeEnum.LIST;
  // 卡片图片URL地址
  public cardImage: string = '';
  // 卡片版本信息
  public version: string = '';
  // 卡片内容列表
  public cardContents: CardContent[] = [];
}

/**
 * 卡片样式类型枚举
 * 定义不同的卡片布局样式
 */
export enum CardStyleTypeEnum {
  // 图片在上方的列表样式
  PICTURE_ABOVE_LIST = 1,
  // 纯列表样式
  LIST = 2,
  // 纯图片样式
  PICTURE = 3,
  // 图片在上方的文本样式
  PICTURE_ABOVE_TEXT = 4,
  // 图片转轮播样式
  PICTURE_TO_SWIPER = 5,
}

/**
 * 媒体类型枚举
 * 定义支持的媒体资源类型
 */
export enum MediaTypeEnum {
  // 图片类型
  IMAGE = 1,
  // 视频类型
  VIDEO = 2,
  // 符号图标类型
  SYMBOL = 3,
}

/**
 * 卡片类型枚举
 * 定义不同类型的卡片内容分类
 */
export enum CardTypeEnum {
  // 组件类型卡片
  COMPONENT = 1,
  // 示例类型卡片
  SAMPLE = 2,
  // 代码实验室类型卡片
  CODELAB = 3,
  // 文章类型卡片
  ARTICLE = 4,
  // 未知类型卡片
  UNKNOWN = 0,
}
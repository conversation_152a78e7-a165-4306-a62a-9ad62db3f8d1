// 导入ArkGraphics2D包中的效果工具包
import { effectKit } from '@kit.ArkGraphics2D';
// 导入ArkTS包中的JSON和工具模块
import { JSON, util } from '@kit.ArkTS';
// 导入基础服务包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入图像工具包
import { image } from '@kit.ImageKit';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG: string = '[ResourceUtil]';

/**
 * 资源工具类
 * 提供资源管理功能，包括字符串资源获取、原始文件读取和图片颜色提取
 * 支持配置文件解析和图像处理操作
 */
export class ResourceUtil {
  /**
   * 获取资源字符串方法
   * 根据指定的资源ID获取对应的字符串
   * @param context 上下文对象
   * @param resource 资源对象
   * @returns 资源字符串
   */
  public static getResourceString(context: Context, resource: Resource): string {
    // 检查资源对象是否为空
    if (ResourceUtil.isEmptyObj(resource)) {
      // 记录资源为空错误日志
      Logger.error(TAG, '[getResourceString] resource is empty.');
      // 返回空字符串
      return '';
    }
    // 初始化资源字符串
    let resourceString: string = '';
    try {
      // 同步获取资源字符串
      resourceString = context.resourceManager.getStringSync(resource.id);
    } catch (error) {
      // 捕获异常并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录获取字符串同步失败错误日志
      Logger.error(TAG, `[getResourceString]getStringSync failed, error: ${err.code}, ${err.message}.`);
    }
    // 返回资源字符串
    return resourceString;
  }

  /**
   * 检查对象是否为空私有方法
   * 检查对象是否为空或没有属性
   * @param obj 要检查的对象
   * @returns 如果对象为空或没有属性返回true，否则返回false
   */
  private static isEmptyObj(obj: Object): boolean {
    // 如果对象为null或不是对象类型
    if (obj === null || typeof obj !== 'object') {
      // 返回true表示为空
      return true;
    }
    // 检查对象是否有属性，没有属性则为空
    return Object.keys(obj).length === 0;
  }

  /**
   * 根据键从原始文件获取字符串方法
   * 从原始文件资源"hmos_web_config.json"中根据键获取内容
   * @param context 能力或应用的基础上下文
   * @param key JSON键值
   * @returns 返回键对应的值
   */
  public static getRawFileStringByKey(context: Context, key: ConfigMapKey): string {
    // 首先尝试从AppStorage中获取配置字符串
    const configStr: string | undefined = AppStorage.get(key);
    // 如果已存在配置字符串
    if (configStr) {
      // 直接返回缓存的配置字符串
      return configStr;
    }
    try {
      // 同步获取原始文件内容
      const result: Uint8Array = context.resourceManager.getRawFileContentSync('hmos_web_config.json');
      // 创建UTF-8文本解码器，忽略BOM
      const textDecoder = util.TextDecoder.create('utf-8', { ignoreBOM: true });
      // 将字节数组解码为字符串
      const content: string = textDecoder.decodeToString(result, { stream: false });
      // 解析JSON内容为配置映射数据
      const jsonContent: ConfigMapData = JSON.parse(content) as ConfigMapData;
      // 检查JSON内容是否包含指定键
      if (JSON.has(jsonContent, key)) {
        // 根据键获取对应的数据
        const linkUrl: string = ResourceUtil.getDataByKey(jsonContent, key);
        // 将数据存储到AppStorage中进行缓存
        AppStorage.setOrCreate(key, linkUrl);
        // 返回链接URL
        return linkUrl;
      }
      // 记录获取原始文件内容失败错误日志（未配置指定键值）
      Logger.error(TAG, `GetRawFileContent failed, cause: no ${key} value is configured.`);
      // 返回空字符串
      return '';
    } catch (error) {
      // 记录获取原始文件内容失败错误日志
      Logger.error(TAG, `GetRawFileContent failed, error code: ${error.code}, message: ${error.message}.`);
      // 返回空字符串
      return '';
    }
  }

  /**
   * 根据键获取数据私有方法
   * 从配置映射数据中根据键获取对应的值
   * @param content 配置映射数据
   * @param key 配置映射键
   * @returns 返回键对应的字符串值
   */
  private static getDataByKey(content: ConfigMapData, key: ConfigMapKey): string {
    // 根据不同的键返回对应的值
    if (key === ConfigMapKey.GALLERY_URL) {
      // 返回画廊URL
      return content.galleryUrl;
    } else if (key === ConfigMapKey.MIIT_URL) {
      // 返回MIIT URL
      return content.miitUrl;
    } else if (key === ConfigMapKey.WHITELIST) {
      // 返回白名单
      return content.whitelist;
    }
    // 默认返回空字符串
    return '';
  }

  /**
   * 根据路径获取颜色数据方法
   * 从媒体URL路径提取图片的主要颜色数据
   * @param mediaUrl 媒体URL路径
   * @returns Promise<颜色数组>
   */
  public static getColorDataByPath(mediaUrl: string): Promise<number[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: number[]) => void, reject: (reason?: Object) => void) => {
      // 获取原始文件内容
      getContext().resourceManager.getRawFileContent(mediaUrl)
        .then((unit8Array: Uint8Array) => {
          // 初始化图像源和像素映射
          let imageSource: image.ImageSource | undefined = undefined;
          let pixelMap: image.PixelMap | undefined = undefined;
          try {
            // 从字节数组创建图像源
            imageSource = image.createImageSource(unit8Array.buffer.slice(0, unit8Array.buffer.byteLength));
            // 同步创建像素映射，指定RGBA_8888格式
            pixelMap = imageSource.createPixelMapSync({
              desiredPixelFormat: image.PixelMapFormat.RGBA_8888,
            });
            // 创建颜色选择器
            effectKit.createColorPicker(pixelMap, (err, colorPicker) => {
              // 如果创建颜色选择器失败
              if (err) {
                // 记录创建颜色选择器失败错误日志
                Logger.error(TAG, 'Failed to create color picker');
                // 拒绝Promise并返回错误
                reject(err);
              } else {
                // 获取最大比例的颜色
                const color = colorPicker.getLargestProportionColor();
                // 解析Promise并返回RGB颜色数组
                resolve([color.red, color.green, color.blue]);
              }
            });
          } catch (error) {
            // 记录获取原始文件内容失败错误日志
            Logger.error(TAG, `GetRawFileContent failed, error code: ${error.code}, message: ${error.message}.`);
            // 拒绝Promise并返回错误
            reject(error);
          } finally {
            // 释放图像源资源
            imageSource?.release();
            // 释放像素映射资源
            pixelMap?.release();
          }
        }).catch((error: BusinessError) => {
        // 记录通过路径获取像素映射失败错误日志
        Logger.error(TAG, `[getPixelMapByPath] failed, error code: ${error.code}, message: ${error.message}.`);
        // 拒绝Promise并返回错误
        reject(error);
      });
    });
  }
}

/**
 * 配置映射数据类
 * 存储配置文件中的URL和白名单信息
 */
export class ConfigMapData {
  // 画廊URL
  public galleryUrl: string = '';
  // MIIT URL
  public miitUrl: string = '';
  // 白名单
  public whitelist: string = '';
}

/**
 * 配置映射键枚举
 * 定义配置文件中的键名常量
 */
export enum ConfigMapKey {
  // 画廊URL键
  GALLERY_URL = 'galleryUrl',
  // MIIT URL键
  MIIT_URL = 'miitUrl',
  // 白名单键
  WHITELIST = 'whitelist',
}
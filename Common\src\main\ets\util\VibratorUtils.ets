// 导入传感器服务包中的振动器模块
import { vibrator } from '@kit.SensorServiceKit';
// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG: string = '[VibratorUtils]';

/**
 * 振动器工具类
 * 提供设备振动功能
 * 支持预设振动效果和自定义振动属性
 */
export class VibratorUtils {
  /**
   * 开始振动方法
   * 触发设备振动，使用预设的时钟计时器振动效果
   */
  public static startVibration() {
    // 定义振动效果配置
    const effect: vibrator.VibrateEffect = {
      // 振动类型为预设类型
      type: 'preset',
      // 效果ID为触觉时钟计时器
      effectId: 'haptic.clock.timer',
      // 振动次数为1次
      count: 1,
      // 振动强度为50%
      intensity: 50,
    };
    // 定义振动属性配置
    const attribute: vibrator.VibrateAttribute = {
      // 振动器ID为0（默认振动器）
      id: 0,
      // 使用场景为触摸反馈
      usage: 'touch',
    };
    try {
      // 触发振动器振动
      vibrator.startVibration(effect, attribute, (error: BusinessError) => {
        // 如果发生错误
        if (error) {
          // 记录启动振动失败错误日志
          Logger.error(TAG, `Failed to start vibration. Code: ${error.code}, message: ${error.message}`);
          // 直接返回
          return;
        }
        // 记录启动振动成功信息日志
        Logger.info(TAG, 'Succeed in starting vibration');
      });
    } catch (err) {
      // 捕获异常并转换为业务错误类型
      const e: BusinessError = err as BusinessError;
      // 记录意外错误日志
      Logger.error(TAG, `An unexpected error occurred. Code: ${e.code}, message: ${e.message}`);
    }
  }
}
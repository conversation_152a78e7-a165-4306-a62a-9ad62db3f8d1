// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入堆叠布局对齐映射数据
import { stackAlignMapData } from '../entity/StackAttributeMapping';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 堆叠布局组件代码生成器类
 * 实现通用代码生成器接口，用于生成堆叠布局组件代码
 */
export class StackCodeGenerator implements CommonCodeGenerator {
  // 对齐内容方式，默认使用映射数据中的默认值
  private alignContent: string = stackAlignMapData.get('Default')!.code;

  /**
   * 生成堆叠布局组件代码方法
   * 根据属性配置生成完整的堆叠布局组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的堆叠布局组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 查找对齐方向属性的索引位置
    const index: number = attributes.findIndex((item) => item.name === 'alignDirection');
    // 对齐方向值，默认为空字符串
    let alignDirection: string = '';
    // 如果找到了对齐方向属性
    if (index >= 0) {
      alignDirection = attributes[index].currentValue;
    }
    // 遍历所有属性，根据对齐方向设置对应的对齐内容
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'alignContentTop':
          // 如果对齐方向为顶部，设置顶部对齐内容
          if (alignDirection === 'Top') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.code ?? this.alignContent;
          }
          break;
        case 'alignContentCenter':
          // 如果对齐方向为居中，设置居中对齐内容
          if (alignDirection === 'Center') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.code ?? this.alignContent;
          }
          break;
        case 'alignContentBottom':
          // 如果对齐方向为底部，设置底部对齐内容
          if (alignDirection === 'Bottom') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.code ?? this.alignContent;
          }
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整堆叠布局组件代码字符串
    return `// 堆叠布局组件
// 提供层叠排列的布局容器，包含两个重叠的列子组件
@Component
struct StackComponent {
  // 构建组件UI方法
  build() {
    // 创建堆叠布局容器，设置对齐内容方式
    Stack({ alignContent: ${this.alignContent} }){
      // 第一个列子组件（背景层）
      Column()
        // 设置第一个列的尺寸（较大）
        .size({ width: 92, height: 92 })
        // 设置背景颜色
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level4'))
      // 第二个列子组件（前景层）
      Column()
        // 设置第二个列的尺寸（较小）
        .size({ width: 64, height: 64 })
        // 设置背景颜色
        .backgroundColor($r('sys.color.multi_color_03'))
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level4'))
    }
    // 设置堆叠布局内边距
    .padding($r('sys.float.padding_level3'))
    // 设置堆叠布局高度
    .height('180vp')
    // 设置堆叠布局宽度
    .width('262vp')
    // 设置堆叠布局边框样式
    .border({
      width: '1vp',
      color: $r('sys.color.comp_background_emphasize'),
      radius: $r('sys.float.corner_radius_level6')
    })
  }
}`;
  }
}
// 导入能力工具包中的通用类型
import type { common } from '@kit.AbilityKit';

/**
 * 全局命名空间声明
 * 在全局对象上声明画笔引擎上下文属性
 */
declare namespace globalThis {
  // 画笔引擎上下文，用于存储UI能力上下文
  let _brushEngineContext: common.UIAbilityContext;
};

/**
 * 全局UI能力上下文管理类
 * 提供静态方法来管理全局的UI能力上下文
 * 主要用于penkit组件等需要访问上下文的场景
 */
export default class GlobalUIAbilityContext {
  /**
   * 获取全局UI能力上下文
   * @returns UI能力上下文对象
   */
  public static getContext(): common.UIAbilityContext {
    // 返回全局存储的画笔引擎上下文
    return globalThis._brushEngineContext;
  }

  /**
   * 设置全局UI能力上下文
   * @param context UI能力上下文对象
   */
  public static setContext(context: common.UIAbilityContext): void {
    // 将上下文存储到全局对象中
    globalThis._brushEngineContext = context;
  }
}
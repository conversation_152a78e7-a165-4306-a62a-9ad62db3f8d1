// 导入通用颜色映射类，用于处理颜色属性的映射
import { CommonColorMapping } from '../../common/entity/CommonMapData';

/**
 * 按钮样式映射类
 * 用于存储按钮样式的代码字符串和实际枚举值的映射关系
 */
class ButtonStyleMapping {
 // 代码字符串，用于代码生成
 public code: string;
 // 按钮样式模式枚举值
 public value: ButtonStyleMode;

  /**
   * 构造函数
   * @param code 代码字符串，表示按钮样式的代码形式
   * @param value 按钮样式模式枚举值
   */
  constructor(code: string, value: ButtonStyleMode) {
    // 初始化代码字符串
    this.code = code;
    // 初始化按钮样式枚举值
    this.value = value;
  }
}

/**
 * 控件尺寸映射类
 * 用于存储控件尺寸的代码字符串和实际枚举值的映射关系
 */
class ControlSizeMapping {
  // 代码字符串，用于代码生成
  public code: string;
  // 控件尺寸枚举值
  public value: ControlSize;

  /**
   * 构造函数
   * @param code 代码字符串，表示控件尺寸的代码形式
   * @param value 控件尺寸枚举值
   */
  constructor(code: string, value: ControlSize) {
    // 初始化代码字符串
    this.code = code;
    // 初始化控件尺寸枚举值
    this.value = value;
  }
}

/**
 * 按钮类型映射类
 * 用于存储按钮类型的代码字符串和实际枚举值的映射关系
 */
class ButtonTypeMapping {
  // 代码字符串，用于代码生成
  public code: string;
  // 按钮类型枚举值
  public value: ButtonType;

  /**
   * 构造函数
   * @param code 代码字符串，表示按钮类型的代码形式
   * @param value 按钮类型枚举值
   */
  constructor(code: string, value: ButtonType) {
    // 初始化代码字符串
    this.code = code;
    // 初始化按钮类型枚举值
    this.value = value;
  }
}

/**
 * 按钮操作映射类
 * 用于存储按钮操作的代码字符串和实际字符串值的映射关系
 */
class ButtonActionMapping {
  // 代码字符串，用于代码生成
  public code: string;
  // 按钮操作字符串值
  public value: string;

  /**
   * 构造函数
   * @param code 代码字符串，表示按钮操作的代码形式
   * @param value 按钮操作字符串值
   */
  constructor(code: string, value: string) {
    // 初始化代码字符串
    this.code = code;
    // 初始化按钮操作字符串值
    this.value = value;
  }
}

// 导出按钮样式映射数据，包含所有可用的按钮样式选项
export const styleMapData: Map<string, ButtonStyleMapping> = new Map([
  // 普通样式，标准的按钮外观
  ['Normal', new ButtonStyleMapping('ButtonStyleMode.NORMAL', ButtonStyleMode.NORMAL)],
  // 强调样式，突出显示的按钮外观
  ['Emphasized', new ButtonStyleMapping('ButtonStyleMode.EMPHASIZED', ButtonStyleMode.EMPHASIZED)],
  // 文本样式，纯文本形式的按钮外观
  ['Textual', new ButtonStyleMapping('ButtonStyleMode.TEXTUAL', ButtonStyleMode.TEXTUAL)],
  // 默认样式，使用强调样式作为默认
  ['Default', new ButtonStyleMapping('ButtonStyleMode.EMPHASIZED', ButtonStyleMode.EMPHASIZED)],
]);

// 导出控件尺寸映射数据，包含所有可用的尺寸选项
export const sizeMapData: Map<string, ControlSizeMapping> = new Map([
  // 普通尺寸，标准大小的控件
  ['Normal', new ControlSizeMapping('ControlSize.NORMAL', ControlSize.NORMAL)],
  // 小尺寸，较小的控件大小
  ['Small', new ControlSizeMapping('ControlSize.SMALL', ControlSize.SMALL)],
  // 默认尺寸，使用小尺寸作为默认
  ['Default', new ControlSizeMapping('ControlSize.SMALL', ControlSize.SMALL)],
]);

// 导出按钮类型映射数据，包含所有可用的按钮类型选项
export const buttonTypeMapData: Map<string, ButtonTypeMapping> = new Map([
  // 胶囊型按钮，圆角矩形外观
  ['Capsule', new ButtonTypeMapping('ButtonType.Capsule', ButtonType.Capsule)],
  // 普通型按钮，标准矩形外观
  ['Normal', new ButtonTypeMapping('ButtonType.Normal', ButtonType.Normal)],
  // 默认类型，使用胶囊型作为默认
  ['Default', new ButtonTypeMapping('ButtonType.Capsule', ButtonType.Capsule)],
]);

// 导出按钮背景颜色映射数据，包含所有可用的背景颜色选项
export const buttonBgColorMap: Map<string, CommonColorMapping> = new Map([
  // 默认背景颜色，白色背景
  ['Default', new CommonColorMapping('rgb(255, 255, 255)', 'rgb(255, 255, 255)')],
]);

// 导出按钮操作映射数据，包含所有可用的按钮操作类型
export const buttonActionMap: Map<string, ButtonActionMapping> = new Map([
  // 点击操作，普通的点击事件
  ['Click', new ButtonActionMapping('Click', 'Click')],
  // 长按手势操作，长按触发的事件
  ['LongGesture', new ButtonActionMapping('LongGesture', 'LongGesture')],
  // 默认操作，使用点击操作作为默认
  ['Default', new ButtonActionMapping('Click', 'Click')],
]);
// 导入通用布尔映射和通用颜色映射，用于切换组件属性配置
import { CommonBoolMapping, CommonColorMapping } from '../../common/entity/CommonMapData';

// 定义切换值类型，支持切换类型或资源颜色
type ToggleValue = ToggleType | ResourceColor ;

/**
 * 切换类型映射类
 * 用于存储切换组件类型的代码字符串和实际值的映射关系
 */
class ToggleTypeMapping {
  // 只读的代码字符串属性，用于代码生成
  public readonly code: string;
  // 只读的切换值属性，存储实际的切换值
  public readonly value: ToggleValue;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 切换值，实际的切换类型或颜色值
   */
  constructor(code: string, value: ToggleValue) {
    // 初始化代码字符串
    this.code = code;
    // 初始化切换值
    this.value = value;
  }
}

export const toggleTypeMapData: Map<string, ToggleTypeMapping> = new Map([
  ['Default', new ToggleTypeMapping('ToggleType.Switch', ToggleType.Switch)],
  ['Switch', new ToggleTypeMapping('ToggleType.Switch', ToggleType.Switch)],
  ['Button', new ToggleTypeMapping('ToggleType.Button', ToggleType.Button)],
  ['Checkbox', new ToggleTypeMapping('ToggleType.Checkbox', ToggleType.Checkbox)],
]);

export const trackBorderRadiusMapData: Map<string, ToggleTypeMapping> = new Map([
  ['Default', new ToggleTypeMapping('16', $r('sys.float.ohos_id_corner_radius_default_l'))],
]);

export const isOnMapData: Map<string, CommonBoolMapping> = new Map([
  ['Default', new CommonBoolMapping('true', true)],
]);

export const backgroundColorMapData: Map<string, CommonColorMapping> = new Map([
  ['Default', new CommonColorMapping('rgba(0,85,255)', 'rgba(0,85,255)')],
]);
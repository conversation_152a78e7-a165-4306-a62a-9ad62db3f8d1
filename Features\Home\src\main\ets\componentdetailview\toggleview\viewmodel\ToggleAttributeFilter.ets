// 导入观察数组类型，用于响应式数组处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于属性处理
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 切换组件属性过滤器类
 * 实现通用属性过滤器接口，用于过滤和处理切换组件的属性
 */
export class ToggleAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据切换组件的特定逻辑过滤和处理属性
   * @param attributes 属性数组，包含所有需要处理的属性
   */
  filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性进行过滤处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理开关状态属性
        case 'isOn':
          // 查找背景颜色属性的索引
          const index = attributes.findIndex((item) => item.name === 'backgroundColor');
          // 如果找到背景颜色属性
          if (index !== -1) {
            // 根据开关状态设置背景颜色属性的启用状态
            attributes[index].enable = (attribute.currentValue === 'true');
          }
          break;
        // 默认情况不做处理
        default:
          break;
      }
    });
  }
}
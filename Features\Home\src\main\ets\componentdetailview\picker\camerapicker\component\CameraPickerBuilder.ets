// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../../viewmodel/DescriptorWrapper';
// 导入相机选择器描述器类型，用于描述组件配置
import type { CameraPickerDescriptor } from '../viewmodel/CameraPickerDescriptor';
// 导入相机选择器组件，用于创建相机选择功能
import { CameraPickerComponent } from './CameraPickerComponent';

/**
 * 相机选择器构建器函数
 * 用于构建相机选择器组件，提供相机拍照和录像功能
 * @param _$$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function CameraPickerBuilder(_$$: DescriptorWrapper) {
  // 创建相机选择器组件实例，传入描述器配置
  CameraPickerComponent({ cameraPickerDescriptor: _$$.descriptor as CameraPickerDescriptor })
}
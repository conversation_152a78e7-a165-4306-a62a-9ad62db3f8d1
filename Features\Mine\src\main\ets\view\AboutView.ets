// 导入通用常量
import { CommonConstants } from '@ohos/common';
// 导入关于页面项目卡片组件
import { AboutItemCard } from '../component/AboutItemCard';
// 导入关于页面视图模型和查看注册信息事件
import { AboutVM, ViewRegistrationInfoEvent } from '../viewmodel/AboutVM';

// 使用Component装饰器定义关于页面视图组件
@Component
export struct AboutView {
  // 创建视图模型实例
  viewModel: AboutVM = AboutVM.getInstance();

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 创建上部分列布局
      Column() {
        // 显示应用图标
        Image($r('app.media.app_icon'))
          .draggable(false)
          .width($r('app.float.about_image_size'))
          .height($r('app.float.about_image_size'))
          .borderRadius($r('sys.float.corner_radius_level8'))
        // 显示应用名称
        Text($r('app.string.app_name'))
          .fontSize($r('sys.float.Title_S'))
          .fontWeight(FontWeight.Bold)
          .margin({ top: $r('sys.float.padding_level4') })
        // 显示关于页面项目卡片
        AboutItemCard()
          .margin({ top: $r('sys.float.padding_level16') })
      }
      // 设置上部分列的外边距
      .margin({ top: $r('sys.float.padding_level8') })
      // 设置上部分列的内边距
      .padding({ left: $r('sys.float.padding_level8'), right: $r('sys.float.padding_level8') })

      // 创建下部分列布局
      Column() {
        // 显示版权信息1
        Text($r('app.string.copyright1'))
          .fontWeight(FontWeight.Medium)
          .fontColor($r('sys.color.font_emphasize'))
          .fontSize($r('sys.float.Body_S'))
          .onClick(() => {
            // 发送查看注册信息事件
            this.viewModel.sendEvent(new ViewRegistrationInfoEvent());
          })
        // 显示版权信息2
        Text($r('app.string.copyright2'))
          .fontColor($r('sys.color.font_secondary'))
          .fontWeight(FontWeight.Regular)
          .fontSize($r('sys.float.Body_S'))
      }
      // 设置下部分列的外边距
      .margin({ bottom: $r('sys.float.padding_level24') })
    }
    // 设置主列的垂直对齐方式为两端对齐
    .justifyContent(FlexAlign.SpaceBetween)
    // 设置主列宽度为100%
    .width(CommonConstants.FULL_PERCENT)
    // 设置主列高度为100%
    .height(CommonConstants.FULL_PERCENT)
  }
}

// 使用Builder装饰器定义关于页面构建器函数
@Builder
export function AboutBuilder() {
  // 创建关于页面视图
  AboutView()
}
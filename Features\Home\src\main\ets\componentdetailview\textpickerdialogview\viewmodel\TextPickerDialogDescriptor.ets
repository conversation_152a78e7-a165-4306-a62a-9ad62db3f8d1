// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入文本选择器对话框相关的属性映射数据
import {
  canLoopMapData,
  itemHeightMapData,
} from '../entity/TextDialogAttributeMapping';

/**
 * 文本选择器对话框描述器类
 * 继承自通用描述器，用于描述文本选择器对话框组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TextPickerDialogDescriptor extends CommonDescriptor {
  // 选中项属性，支持单选或多选，默认为0
  public selected: number | number[] = 0;
  // 循环属性，默认使用默认值
  public canLoop: boolean = canLoopMapData.get('Default')!.value as boolean;
  // 项目高度属性，默认使用默认值
  public itemHeight: number = itemHeightMapData.get('Default')!.value as number;

  /**
   * 转换属性方法
   * 将原始属性转换为描述器属性
   * @param attributes 原始属性数组，包含需要转换的属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性进行转换
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'canLoop':
          this.canLoop = JSON.parse(attribute.currentValue) ?? this.canLoop;
          break;
        case 'itemHeight':
          this.itemHeight = Number(attribute.currentValue) ?? this.itemHeight;
          break;
        default:
          break;
      }
    });
  }
}
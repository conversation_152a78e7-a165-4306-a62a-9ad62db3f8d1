// 导入通用模块中的观察数组类型，用于处理响应式数组
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器基类，用于实现属性过滤接口
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 按钮属性过滤器类
 * 实现通用属性过滤器接口，用于根据按钮样式动态控制其他属性的可用性
 */
export class ButtonAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据按钮样式的当前值，动态启用或禁用相关属性
   * @param attributes 观察数组类型的属性列表，包含所有可配置的属性
   */
  public filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，查找需要处理的属性类型
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理按钮样式属性
        case 'buttonStyle':
          // 查找按钮类型属性在数组中的索引位置
          const buttonTypeIndex = attributes.findIndex((item) => item.name === 'buttonType');
          // 查找操作类型属性在数组中的索引位置
          const operationIndex = attributes.findIndex((item) => item.name === 'operation');
          // 查找背景颜色属性在数组中的索引位置
          const backgroundColorIndex = attributes.findIndex((item) => item.name === 'backgroundColor');
          // 如果任何一个相关属性不存在，则直接返回
          if (buttonTypeIndex === -1 || operationIndex === -1 || backgroundColorIndex === -1) {
            return;
          }
          // 如果按钮样式为文本样式
          if (attribute.currentValue === 'Textual') {
            // 禁用按钮类型属性（文本样式不需要设置类型）
            attributes[buttonTypeIndex].enable = false;
            // 启用操作类型属性（文本样式支持操作）
            attributes[operationIndex].enable = true;
            // 禁用背景颜色属性（文本样式没有背景）
            attributes[backgroundColorIndex].enable = false;
          } else if (attribute.currentValue === 'Emphasized') {
            // 如果按钮样式为强调样式，启用所有相关属性
            attributes[buttonTypeIndex].enable = true;
            attributes[operationIndex].enable = true;
            attributes[backgroundColorIndex].enable = true;
          } else {
            // 其他样式（普通样式）的处理
            attributes[buttonTypeIndex].enable = true;
            attributes[operationIndex].enable = true;
            // 普通样式不支持自定义背景颜色
            attributes[backgroundColorIndex].enable = false;
          }
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
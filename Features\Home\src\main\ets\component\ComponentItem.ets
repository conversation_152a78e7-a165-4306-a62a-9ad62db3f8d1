// 导入通用模块中的断点类型枚举、通用常量和全局信息模型
import { BreakpointTypeEnum, CommonConstants, GlobalInfoModel } from '@ohos/common';
// 导入组件内容类型定义
import type { ComponentContent } from '../model/ComponentData';

// 组件装饰器，标识这是一个可复用的UI组件
@Component
// 导出组件项结构体
export struct ComponentItem {
  // 组件内容数据属性，使用Prop装饰器接收外部传入的数据
  @Prop componentContent: ComponentContent;
  // 是否显示分割线的布尔属性
  @Prop showDivider: boolean;
  // 按钮颜色属性，默认值为系统交互激活色
  @Prop buttonColor: ResourceColor = $r('sys.color.interactive_active');
  // 全局信息模型实例，从应用存储中获取
  private globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

  // 构建方法，定义组件的UI结构
  build() {
    // 创建列布局容器作为主容器
    Column() {
      // 创建分割线组件
      Divider()
        // 根据showDivider属性控制分割线的可见性
        .visibility(this.showDivider ? Visibility.Visible : Visibility.Hidden)
        // 设置分割线的颜色为系统第三级背景色
        .color($r('sys.color.comp_background_tertiary'))
        // 设置分割线的左右边距
        .margin({ left: $r('sys.float.padding_level36'), right: $r('sys.float.padding_level6') })
      // 创建行布局容器用于内容排列
      Row() {
        // 创建图片组件显示组件图标
        Image($rawfile(this.componentContent.mediaUrl))
          // 禁用图片拖拽功能
          .draggable(false)
          // 设置图片加载失败时的占位图片
          .alt($r('app.media.ic_placeholder'))
          // 设置图片圆角半径
          .borderRadius($r('sys.float.corner_radius_level5'))
          // 设置图片宽度
          .width($r('app.float.tip_image_height'))
          // 设置图片宽高比为1:1
          .aspectRatio(1)
          // 设置图片右侧边距
          .margin({ right: $r('sys.float.padding_level8') })

        // 创建列布局容器用于文本信息排列
        Column({ space: CommonConstants.SPACE_6 }) {
          // 创建文本组件显示组件副标题
          Text(this.componentContent.subTitle)
            // 设置副标题字体大小
            .fontSize($r('sys.float.Subtitle_M'))
            // 设置副标题字体粗细为中等
            .fontWeight(FontWeight.Medium)
            // 设置副标题字体颜色为主要字体色
            .fontColor($r('sys.color.font_primary'))
            // 设置副标题最大显示行数为1行
            .maxLines(1)
            // 设置副标题文本溢出时显示省略号
            .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 创建文本组件显示组件标题
          Text(this.componentContent.title)
            // 设置标题字体大小
            .fontSize($r('sys.float.Body_S'))
            // 设置标题字体颜色为次要字体色
            .fontColor($r('sys.color.font_secondary'))
            // 设置标题最大显示行数为1行
            .maxLines(1)
            // 设置标题文本溢出时显示省略号
            .textOverflow({ overflow: TextOverflow.Ellipsis })
        }
        // 设置文本列容器内子元素水平左对齐
        .alignItems(HorizontalAlign.Start)
        // 设置文本列容器的布局权重为1，占据剩余空间
        .layoutWeight(1)

        // 创建操作按钮组件
        Button($r('app.string.open'), { buttonStyle: ButtonStyleMode.NORMAL, controlSize: ControlSize.SMALL })
          // 根据当前断点类型设置按钮圆角半径
          .borderRadius(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
          $r('sys.float.corner_radius_level4') : 0)
          // 根据当前断点类型设置按钮类型
          .type(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? ButtonType.Normal :
          ButtonType.Capsule)
          // 设置按钮左侧边距
          .margin({ left: $r('sys.float.padding_level8') })
          // 设置按钮宽度
          .width($r('app.float.tip_button_width'))
          // 设置按钮字体颜色
          .fontColor(this.buttonColor)

      }
      // 设置行容器宽度为100%
      .width('100%')
      // 设置行容器的布局权重为1
      .layoutWeight(1)
      // 设置行容器内边距
      .padding($r('sys.float.padding_level6'))
    }
    // 启用渲染组优化性能
    .renderGroup(true)
    // 设置列容器内子元素水平左对齐
    .alignItems(HorizontalAlign.Start)
    // 设置组件项整体高度
    .height($r('app.float.component_item_height'))
    // 设置组件项整体宽度为100%
    .width('100%')
  }
}
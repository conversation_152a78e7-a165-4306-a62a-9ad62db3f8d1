// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入Flex属性映射相关的枚举和映射数据
import {
  ElementsNums,
  elementsNumsMapData,
  flexAlignItemMapData,
  flexContentMapData,
  flexDirectionMapData,
  flexWrapMapData,
} from '../entity/FlexAttributeMapping';

/**
 * Flex描述器类
 * 继承通用描述器，用于存储和管理Flex布局的所有属性配置
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class FlexDescriptor extends CommonDescriptor {
  // 元素数量，默认使用映射数据中的默认值（2个元素）
  public elements: ElementsNums = elementsNumsMapData.get('Default')!.value as ElementsNums;
  // Flex方向，默认使用映射数据中的默认值（水平从左到右）
  public direction: FlexDirection = flexDirectionMapData.get('Default')!.value as FlexDirection;
  // Flex换行方式，默认使用映射数据中的默认值（不换行）
  public wrap: FlexWrap = flexWrapMapData.get('Default')!.value as FlexWrap;
  // 主轴对齐方式，默认使用映射数据中的默认值（起始对齐）
  public justifyContent: FlexAlign = flexContentMapData.get('Default')!.value as FlexAlign;
  // 交叉轴项目对齐方式，默认使用映射数据中的默认值（自动对齐）
  public alignItems: ItemAlign = flexAlignItemMapData.get('Default')!.value as ItemAlign;
  // 单个项目自身对齐方式，默认使用映射数据中的默认值（自动对齐）
  public alignSelf: ItemAlign = flexAlignItemMapData.get('Default')!.value as ItemAlign;
  // 多行内容对齐方式，默认使用映射数据中的默认值（起始对齐）
  public alignContent: FlexAlign = flexContentMapData.get('Default')!.value as FlexAlign;

  /**
   * 转换属性方法
   * 将原始属性数组转换为Flex描述器的具体属性值
   * @param attributes 原始属性数组，包含所有Flex布局的配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理元素数量属性
        case 'elements':
          // 根据当前值判断是显示4个元素还是2个元素
          this.elements =
            attribute.currentValue === ElementsNums.FOUR.toString() ? ElementsNums.FOUR : ElementsNums.TWO;
          break;
        // 处理Flex方向属性
        case 'direction':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的Row方向
          this.direction =
            flexDirectionMapData.get(attribute.currentValue)?.value as FlexDirection ?? FlexDirection.Row;
          break;
        // 处理Flex换行属性
        case 'wrap':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的NoWrap
          this.wrap = flexWrapMapData.get(attribute.currentValue)?.value as FlexWrap ?? FlexWrap.NoWrap;
          break;
        // 处理主轴对齐属性
        case 'justifyContent':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的Start对齐
          this.justifyContent =
            flexContentMapData.get(attribute.currentValue)?.value as FlexAlign ?? FlexAlign.Start;
          break;
        // 处理交叉轴项目对齐属性
        case 'alignItems':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的Auto对齐
          this.alignItems =
            flexAlignItemMapData.get(attribute.currentValue)?.value as ItemAlign ?? ItemAlign.Auto;
          break;
        // 处理单个项目自身对齐属性
        case 'alignSelf':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的Auto对齐
          this.alignSelf = flexAlignItemMapData.get(attribute.currentValue)?.value as ItemAlign ?? ItemAlign.Auto;
          break;
        // 处理多行内容对齐属性
        case 'alignContent':
          // 从映射数据中获取对应的枚举值，如果找不到则使用默认的Start对齐
          this.alignContent =
            flexContentMapData.get(attribute.currentValue)?.value as FlexAlign ?? FlexAlign.Start;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
// 导入详情页面常量，用于获取List组件的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入List属性映射数据和类型
import { edgeEffectMapData, LanesStyle, listDirectionMapData } from '../entity/ListAttributeMapping';

/**
 * List代码生成器类
 * 实现通用代码生成器接口，用于生成List布局相关的代码
 */
export class ListCodeGenerator implements CommonCodeGenerator {
  // List滚动方向代码字符串，默认为垂直方向
  private listDirection: string = listDirectionMapData.get('Default')!.code;
  // List多列布局配置，默认为单列无间距
  private lanes: LanesStyle = {
    value: 1,
    gutter: 0,
  };
  // List边缘效果代码字符串，默认为弹簧效果
  private edgeEffect: string = edgeEffectMapData.get('Default')!.code;
  // 粘性头部状态，默认为true
  private sticky: boolean = true;
  // 粘性头部样式名称，默认为Header样式
  private stickyName: string = 'StickyStyle.Header';

  /**
   * 生成List布局代码方法
   * 根据传入的属性数组生成完整的List组件代码字符串
   * @param attributes 原始属性数组，包含所有List布局的配置信息
   * @returns 生成的List组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 初始化高度和宽高比变量
    let height: string = '';
    let aspectRatio: number = DetailPageConstant.ASPECT_RATIO_SQUARE;
    // 遍历所有属性，根据属性名称更新对应的配置
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理List滚动方向属性
        case 'listDirection':
          // 从映射数据中获取对应的代码字符串
          this.listDirection = listDirectionMapData.get(attribute.currentValue)?.code ?? this.listDirection;
          break;
        // 处理List列数属性
        case 'lanesNum':
          // 更新列数配置
          this.lanes = {
            value: Number(attribute.currentValue),
            gutter: this.lanes.gutter
          };
          // 根据列数阈值设置不同的高度和宽高比
          if (this.lanes.value >= DetailPageConstant.LIST_LANES_THRESHOLD) {
            height = '64vp';
            aspectRatio = DetailPageConstant.ASPECT_RATIO_SQUARE;
          } else {
            height = '92vp';
            aspectRatio = DetailPageConstant.ASPECT_RATIO_INVALID;
          }
          break;
        // 处理List列间距属性
        case 'gutter':
          // 更新列间距配置
          this.lanes = {
            value: this.lanes.value,
            gutter: Number(attribute.currentValue ?? 0)
          };
          break;
        // 处理List边缘效果属性
        case 'edgeEffect':
          // 从映射数据中获取对应的代码字符串
          this.edgeEffect = edgeEffectMapData.get(attribute.currentValue)?.code ?? this.edgeEffect;
          break;
        // 处理粘性头部属性
        case 'sticky':
          // 解析布尔值并设置对应的样式名称
          this.sticky = JSON.parse(attribute.currentValue);
          this.sticky && (this.stickyName = 'StickyStyle.Header');
          !this.sticky && (this.stickyName = 'StickyStyle.None');
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 定义List组件的数据源代码字符串
    const codeOne: string = `
    [
      {
        title: 'ONE',
        projects: ['item 1', 'item 2', 'item 3', 'item 4']
      },
      {
        title: 'TWO',
        projects: ['item 5', 'item 6', 'item 7', 'item 8']
      },
      {
        title: 'THREE',
        projects: ['item 9', 'item 10', 'item 11', 'item 12']
      },
      {
        title: 'FOUR',
        projects: ['item 13', 'item 14', 'item 15', 'item 16']
      }
    ]`;
    // 返回生成的完整List组件代码字符串
    return `// 时间表接口，定义List组件中分组数据的结构
interface TimeTable {
  title: string;
  projects: string[];
}

// List组件，实现分组列表显示
@Component
struct ListComponent {
  // 列表数据状态，存储分组数据
  @State listData: TimeTable[] = ${codeOne};

  // 列表项头部构建器
  @Builder
  itemHead(text: string) {
    Text(text)
      .textAlign(TextAlign.Center)
      .fontSize($r('sys.float.Body_L'))
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      .fontColor($r('sys.color.font_on_primary'))
      .width('100%')
      .height(32)
      .margin({ bottom: $r('sys.float.padding_level4') })
  }

  // 构建组件UI方法
  build() {
    Column() {
      // 创建List组件，设置项目间距
      List({ space: 8 }) {
        // 遍历列表数据创建列表项组
        ForEach(this.listData, (item: TimeTable) => {
          // 创建列表项组，包含头部和项目列表
          ListItemGroup({ header: this.itemHead(item.title) }) {
            // 遍历项目数据创建列表项
            ForEach(item.projects, (project: string) => {
              // 创建列表项
              ListItem() {
                // 创建列布局容器
                Column() {
                  // 创建文本组件显示项目内容
                  Text(project)
                    .fontSize($r('sys.float.Body_L'))
                    .fontColor($r('sys.color.font_emphasize'))
                    .textAlign(TextAlign.Center)
                }
                .width('100%')
                .height('100%')
                .borderRadius($r('sys.float.corner_radius_level4'))
                .border({ width: 1.5, color: $r('sys.color.comp_background_emphasize') })
                .justifyContent(FlexAlign.Center)
              }
              // 设置列表项的尺寸和宽高比
              .width('100%')
              .height('${height}')
              .aspectRatio(${aspectRatio})
            }, (item: string) => item)
          }
        }, (item: TimeTable, _index: number) => item.title.toString())
      }
      // 设置List组件的属性
      .width('100%')
      .height('100%')
      // 设置粘性头部样式
      .sticky(${this.stickyName})
      // 隐藏滚动条
      .scrollBar(BarState.Off)
      // 设置多列布局和列间距
      .lanes(${this.lanes.value}, ${this.lanes.gutter})
      // 设置滚动方向
      .listDirection(${this.listDirection})
      // 设置边缘滚动效果
      .edgeEffect(${this.edgeEffect})
    }
    // 设置列容器的对齐方式和样式
    .alignItems(HorizontalAlign.Center)
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .height('100%')
    .clip(true)
    .borderRadius($r('sys.float.corner_radius_level8'))
  }
}`;
  }
}
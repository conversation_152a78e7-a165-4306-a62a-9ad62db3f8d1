// 导入通用颜色映射和通用数字映射，用于文本属性配置
import { CommonColorMapping, CommonNumberMapping } from '../../common/entity/CommonMapData';

/**
 * 文本映射类
 * 用于存储文本属性的代码字符串和实际值的映射关系
 */
class TextMapping {
  // 只读的代码字符串属性，用于代码生成
  public readonly code: string;
  // 只读的长度值属性，存储实际的长度值
  public readonly value: Length;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 长度值，实际的长度对象
   */
  constructor(code: string, value: Length) {
    // 初始化代码字符串
    this.code = code;
    // 初始化长度值
    this.value = value;
  }
}

export const fontSizeMapData: Map<string, TextMapping> = new Map([
  ['Default', new TextMapping('16', $r('sys.float.ohos_id_text_size_body1'))],
]);

export const fontColorMapData: Map<string, CommonColorMapping> = new Map([
  ['Default', new CommonColorMapping('rgba(0,85,255)', 'rgba(0,85,255)')],
]);

export const opacityMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('1', 1)],
]);

export const letterSpacingMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('0', 0)],
]);

export const textShadowRadiusMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('0', 0)],
]);
// 导入长度度量工具，用于处理长度单位转换
import { LengthMetrics } from '@kit.ArkUI';
// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入文本区域描述器类型，用于描述文本区域组件配置
import type { TextAreaDescriptor } from './TextAreaDescriptor';

/**
 * 文本区域属性修改器类
 * 继承自通用属性修改器，用于修改文本区域组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class TextAreaAttributeModifier extends CommonAttributeModifier<TextAreaDescriptor, TextAreaAttribute> {
  /**
   * 应用普通属性到文本区域组件实例
   * 将描述器中的属性值应用到实际的文本区域组件上
   * @param instance 文本区域属性实例，用于设置文本区域组件属性
   */
  applyNormalAttribute(instance: TextAreaAttribute): void {
    // 分配行间距属性，将描述器中的lineSpacing值转换为vp单位并应用到组件实例
    this.assignAttribute((descriptor => descriptor.lineSpacing), (val) => instance.lineSpacing(LengthMetrics.vp(val)));
    // 分配文本溢出类型属性，将描述器中的textOverflowTypeData值应用到组件实例
    this.assignAttribute((descriptor => descriptor.textOverflowTypeData), (val) => instance.textOverflow(val));
    // 分配文本对齐属性，将描述器中的textAlign值应用到组件实例
    this.assignAttribute((descriptor => descriptor.textAlign), (val) => instance.textAlign(val));
  }
}
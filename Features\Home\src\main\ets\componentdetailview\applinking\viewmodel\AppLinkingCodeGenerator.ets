// 导入通用模块中的配置映射键和资源工具，用于获取配置信息
import { ConfigMapKey, ResourceUtil } from '@ohos/common';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入应用链接相关的类型和映射数据
import {
  LinkType,
  typeImportCodeMapData,
  typeInvokeCodeMapData,
  typeMapData,
} from '../entity/AppLinkingAttributeMapping';

/**
 * 应用链接代码生成器类
 * 实现通用代码生成器接口，用于生成应用链接组件的代码
 */
export class AppLinkingCodeGenerator implements CommonCodeGenerator {
  // 链接类型属性，默认使用映射数据中的默认值
  private type: LinkType = typeMapData.get('Default')!;

  /**
   * 生成应用链接组件代码的方法
   * 根据传入的属性数组生成完整的应用链接组件代码字符串
   * @param attributes 原始属性数组，包含组件的各种配置属性
   * @returns 生成的应用链接组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的配置
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理链接类型属性
        case 'type':
          // 从映射数据中获取对应的链接类型，如果不存在则保持原值
          this.type = typeMapData.get(attribute.currentValue) ?? this.type;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 从映射数据中获取对应链接类型的调用代码字符串
    let codeStr: string = typeInvokeCodeMapData.get(this.type)!;
    // 如果是应用市场类型，需要替换URL占位符
    if (this.type === LinkType.TYPE_GALLERY) {
      // 从资源文件中获取应用市场的链接URL
      const linkUrl: string = ResourceUtil.getRawFileStringByKey(getContext(), ConfigMapKey.GALLERY_URL);
      // 将代码字符串中的占位符替换为实际的URL
      codeStr = codeStr.replace('%s', linkUrl);
    }
    // 返回生成的应用链接组件代码字符串，包含导入语句和组件定义
    return `${typeImportCodeMapData.get(this.type)!}

@Component
struct AppLinkingComponent {
  build() {
    Column() {
      Button('拉起${this.type}页')
        .backgroundColor($r('sys.color.background_secondary'))
        .height(40)
        .fontColor($r('sys.color.font_emphasize'))
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .onClick(() => {
          ${codeStr}
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}`;
  }
}
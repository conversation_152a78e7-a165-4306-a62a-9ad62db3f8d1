// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入全屏导航数据模型
import { FullScreenNavigationData } from '../model/FullScreenNavigationData';

/**
 * 全屏导航组件
 * 用于全屏页面的顶部导航栏组件
 * 支持标题显示、标签页视图、模糊效果等功能
 * 具备响应式布局和动画效果
 */
@Component
export struct FullScreenNavigation {
  // 全局信息模型，用于获取设备信息和断点状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 顶部导航数据，包含标题、样式等配置
  @Prop topNavigationData: FullScreenNavigationData = new FullScreenNavigationData();
  // 模糊渲染组状态
  @StorageProp('BlurRenderGroup') blurRenderGroup: boolean = false;
  // 标签视图构建器参数，可选
  @BuilderParam tabView?: () => void;

  /**
   * 构建全屏导航组件的UI结构
   * 包含标题行、可选的标签视图和分割线
   */
  build() {
    // 创建垂直布局的列容器
    Column() {
      // 标题行容器
      Row() {
        // 导航标题文本
        Text(this.topNavigationData.title)
          // 设置标题缩放变换
          .scale({
            x: this.topNavigationData.titleScale,
            y: this.topNavigationData.titleScale,
            z: this.topNavigationData.titleScale,
            centerX: 0,
            centerY: 0,
          })
          // 根据断点类型设置字体大小
          .fontSize(new BreakpointType({
            sm: $r('sys.float.Title_M'),
            md: $r('sys.float.Title_L'),
            lg: $r('sys.float.Title_L'),
            xl: $r('sys.float.Title_S'),
          }).getValue(this.globalInfoModel.currentBreakpoint))
          // 设置字体颜色
          .fontColor(this.topNavigationData.titleColor)
          // 设置字体粗细为粗体
          .fontWeight(FontWeight.Bold)
          // 设置文本左对齐
          .textAlign(TextAlign.Start)
          // 设置布局权重为1，占满剩余空间
          .layoutWeight(1)
      }
      // 设置行容器顶部外边距
      .margin({ top: this.topNavigationData.titleOffsetY })
      // 设置垂直居中对齐
      .alignItems(VerticalAlign.Center)
      // 设置行容器高度
      .height(CommonConstants.NAVIGATION_HEIGHT)
      // 根据断点类型设置内边距
      .padding({
        // 设置左内边距
        left: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level16'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        // 设置右内边距
        right: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level16'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
      })

      // 如果存在标签视图且需要显示标签
      if (this.tabView && this.topNavigationData.showTab) {
        // 显示标签视图
        this.tabView()
      }
      // 分割线组件
      Divider()
        // 设置分割线颜色
        .color($r('sys.color.comp_divider'))
        // 根据是否启用模糊效果控制分割线可见性
        .visibility(this.topNavigationData.isBlur ? Visibility.Visible : Visibility.Hidden)
    }
    // 根据是否启用模糊效果设置背景模糊样式
    .backgroundBlurStyle(this.topNavigationData.isBlur ? BlurStyle.COMPONENT_THICK : undefined)
    // 设置背景颜色为透明
    .backgroundColor(Color.Transparent)
    // 设置渲染组
    .renderGroup(this.blurRenderGroup)
    // 设置列容器内边距
    .padding({
      // 设置顶部内边距为状态栏高度
      top: this.globalInfoModel.statusBarHeight,
      // 根据断点类型设置左内边距，大屏时考虑标签栏宽度
      left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? CommonConstants.TAB_BAR_WIDTH : 0,
    })
    // 设置列容器宽度为100%
    .width('100%')
  }
}
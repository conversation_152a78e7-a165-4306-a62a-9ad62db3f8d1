// 导入组件详情页面视图模型类型定义
import type { ComponentDetailPageVM } from './ComponentDetailPageVM';

// 定义组件详情管理器类
export class ComponentDetailManager {
  // 定义静态实例变量
  private static instance: ComponentDetailManager;
  // 定义私有详情视图模型映射表
  private detailViewModelMap: Map<string, ComponentDetailPageVM> = new Map();

  // 定义私有构造函数
  private constructor() {
    // 构造函数体为空
  }

  // 定义获取实例的静态方法
  public static getInstance(): ComponentDetailManager {
    // 如果实例不存在则创建新实例
    if (!ComponentDetailManager.instance) {
      ComponentDetailManager.instance = new ComponentDetailManager();
    }
    // 返回实例
    return ComponentDetailManager.instance;
  }

  // 定义获取详情视图模型的公共方法
  public getDetailViewModel(key: string): ComponentDetailPageVM | undefined {
    // 从映射表中获取视图模型
    return this.detailViewModelMap.get(key);
  }

  // 定义更新详情视图模型映射表的公共方法
  public updateDetailViewModelMap(key: string, value: ComponentDetailPageVM) {
    // 在映射表中设置键值对
    this.detailViewModelMap.set(key, value);
  }
}
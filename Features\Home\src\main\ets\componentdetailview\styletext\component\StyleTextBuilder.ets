// 导入长度度量工具，用于设置文本缩进
import { LengthMetrics } from '@kit.ArkUI';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入样式文本描述器类型，用于描述组件配置
import type { StyleTextDescriptor } from '../viewmodel/StyleTextDescriptor';

/**
 * 样式文本构建器函数
 * 用于构建样式文本组件，支持富文本样式设置
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function StyleTextBuilder($$: DescriptorWrapper) {
  // 创建样式文本组件实例
  StyleTextComponent({ styleTextDescriptor: $$.descriptor as StyleTextDescriptor })
}

/**
 * 样式文本组件
 * 提供富文本显示功能，支持段落样式和字体样式设置
 */
@Component
struct StyleTextComponent {
  // 样式文本描述器属性，监听变化并触发样式更新
  @Prop @Watch('onStyleUpdated') styleTextDescriptor: StyleTextDescriptor;
  // 文本内容，使用长字符串进行演示
  private text: string =
    'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA';
  // 文本控制器，用于控制文本显示
  textController: TextController = new TextController();
  // 段落样式属性，设置文本缩进和最大行数
  paragraphStyleAttr: ParagraphStyle = new ParagraphStyle({
    textIndent: LengthMetrics.vp(this.styleTextDescriptor.textIndent),
    maxLines: this.styleTextDescriptor.maxLines
  });
  // 可变样式字符串，包含文本内容和样式配置
  mutableStyledString: MutableStyledString =
    new MutableStyledString(this.text,
      [
        {
          // 设置段落样式，应用于前3个字符
          start: 0,
          length: 3,
          styledKey: StyledStringKey.PARAGRAPH_STYLE,
          styledValue: this.paragraphStyleAttr
        },
        {
          // 设置字体样式，应用于第10-14个字符
          start: 10,
          length: 5,
          styledKey: StyledStringKey.FONT,
          styledValue: new TextStyle({
            fontColor: this.styleTextDescriptor.highlightColor
          })
        }
      ]);

  /**
   * 构建组件UI方法
   */
  build() {
    // 创建列布局容器
    Column() {
      // 创建文本组件，使用文本控制器
      Text(undefined, { controller: this.textController })
        // 设置左右边距
        .margin({ left: $r('app.float.common_left_right_margin'), right: $r('app.float.common_left_right_margin') })
        // 设置字体大小
        .fontSize($r('sys.float.Body_L'))
        // 设置字体粗细
        .fontWeight(FontWeight.Regular)
    }
    // 设置列布局宽度
    .width($r('app.float.multiline_text_width'))
    // 设置垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
    // 组件附加时设置样式字符串
    .onAttach(() => {
      this.textController.setStyledString(this.mutableStyledString);
    })
  }

  /**
   * 样式更新回调方法
   * 当样式文本描述器属性发生变化时触发
   * @param _changedPropertyName 变化的属性名称
   */
  onStyleUpdated(_changedPropertyName: string) {
    // 更新段落样式
    this.mutableStyledString.setStyle({
      start: 0,
      length: 3,
      styledKey: StyledStringKey.PARAGRAPH_STYLE,
      styledValue: new ParagraphStyle({
        textIndent: LengthMetrics.vp(this.styleTextDescriptor.textIndent),
        maxLines: this.styleTextDescriptor.maxLines,
        overflow: this.styleTextDescriptor.overflow,
      }),
    });
    // 更新字体样式
    this.mutableStyledString.setStyle({
      start: 10,
      length: 5,
      styledKey: StyledStringKey.FONT,
      styledValue: new TextStyle({
        fontColor: this.styleTextDescriptor.highlightColor,
      }),
    });
    // 应用更新后的样式字符串
    this.textController.setStyledString(this.mutableStyledString);
  }
}
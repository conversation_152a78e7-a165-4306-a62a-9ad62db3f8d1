// 导入ArkTS包中的uri模块
import { uri } from '@kit.ArkTS';
// 导入ArkUI包中的构建节点、框架节点、节点控制器和窗口模块
import { BuilderNode, FrameNode, NodeController, window } from '@kit.ArkUI';
// 导入ArkWeb包中的webview模块
import { webview } from '@kit.ArkWeb';
// 导入基础服务包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入模块名称枚举和滚动方向枚举
import { ModuleNameEnum, ScrollDirectionEnum } from '../constant/CommonEnums';
// 导入日志工具
import Logger from './Logger';
// 导入网络工具
import { NetworkUtil } from './NetworkUtil';
// 导入资源工具和配置映射键
import { ConfigMapKey, ResourceUtil } from './ResourceUtil';

// 日志标签常量
const TAG: string = '[WebUtil]';

// Web节点映射表，存储URL与Web节点控制器的对应关系
const webNodeMap: Map<string, WebNodeController> = new Map();
// Web控制器映射表，存储URL与Web视图控制器的对应关系
const webControllerMap: Map<string, WebviewController | undefined> = new Map();
// 事件发射器映射表，存储模块与回调函数的对应关系
const eventEmitterMap: Map<string, Function> = new Map();
// 弹窗事件映射表，存储URL与弹窗回调函数的对应关系
const sheetEventMap: Map<string, Function> = new Map();
// 页面事件映射表，存储URL与页面跳转回调函数的对应关系
const pageEventMap: Map<string, Function> = new Map();
// 节点需求映射表，检查Web节点是否需要再次使用
const nodeRequiredMap: Map<string, boolean> = new Map();
// Web加载状态映射表，存储URL与加载状态的对应关系
const webLoadedMap: Map<string, boolean> = new Map();
// 当前滚动偏移量
let currentOffset: number = 0;
// UI上下文
let context: UIContext;

// 组件代码HTML路径常量
const componentCodeHtml: string = `/codePreview/index.html`;

/**
 * Web构建器参数接口
 * 定义创建Web组件所需的参数结构
 */
interface WebBuilderParam {
  // Web页面URL地址
  webUrl: string;
  // Web视图控制器
  webController: WebviewController;
  // 嵌套滚动选项
  nestedScroll: NestedScrollOptions;
  // 原生操作数据对象
  nativeActionData: NativeActionData;
  // 模块名称，可选
  module?: ModuleNameEnum;
  // 是否仅白色模式，可选
  onlyWhiteMode?: boolean;
  // 是否允许垂直滚动条，可选
  verticalScrollBarAccess?: boolean;
}

/**
 * Web构建器函数
 * 创建配置完整的Web组件
 * 包含各种Web属性设置和事件处理
 * @param param Web构建器参数对象
 */
@Builder
function webBuilder(param: WebBuilderParam) {
  // 创建Web组件并设置源URL和控制器
  Web({ src: param.webUrl, controller: param.webController })
    // 禁用缩放功能
    .zoomAccess(false)
    // 允许文件访问
    .fileAccess(true)
    // 允许图片访问
    .imageAccess(true)
    // 设置混合模式为无
    .mixedMode(MixedMode.None)
    // 设置垂直滚动条访问权限
    .verticalScrollBarAccess(param.verticalScrollBarAccess)
    // 禁用水平滚动条
    .horizontalScrollBarAccess(false)
    // 设置缓存模式为默认
    .cacheMode(CacheMode.Default)
    // 允许DOM存储访问
    .domStorageAccess(true)
    // 允许JavaScript访问
    .javaScriptAccess(true)
    // 根据模块类型设置JavaScript代理
    .javaScriptProxy(param.module === ModuleNameEnum.ARTICLE_DETAIL ? {
      // 代理对象
      object: param.nativeActionData,
      // 代理名称
      name: 'nativeActionData',
      // 可调用的方法列表
      methodList: ['webSheet', 'jumpPage'],
      // Web控制器
      controller: param.webController,
      // 权限配置
      permission: javascriptProxyPermission,
    } : undefined)
    // 禁用地理位置访问
    .geolocationAccess(false)
    // 设置背景色为透明
    .backgroundColor(Color.Transparent)
    // 设置嵌套滚动选项
    .nestedScroll(param.nestedScroll)
    // 根据参数设置暗色模式
    .darkMode(param.onlyWhiteMode ? WebDarkMode.Off : WebDarkMode.Auto)
    // 允许强制暗色访问
    .forceDarkAccess(true)
    // 禁用拖放功能
    .allowDrop(null)
    // 页面开始加载事件处理
    .onPageBegin(() => {
      // 激活Web控制器
      param.webController.onActive();
      // 记录页面开始加载日志
      Logger.debug(TAG, `onPageBegin with url: ${param.webUrl}`);
    })
    // 页面加载完成事件处理
    .onPageEnd(() => {
      // 设置Web加载状态为false
      AppStorage.setOrCreate('webIsLoading', false);
      // 设置信任列表
      WebUtil.setTrustList(param.webUrl);
      // 记录页面加载完成日志
      Logger.debug(TAG, `onPageEnd with url: ${param.webUrl}`);
    })
    // 加载拦截事件处理
    .onLoadIntercept((event: OnLoadInterceptEvent) => {
      // 获取请求URL
      const tempUrl = event.data.getRequestUrl();
      // 检查URL是否合法
      return WebUtil.checkUrl(tempUrl);
    })
    // SSL错误事件处理
    .onSslErrorEventReceive((event) => {
      // 记录SSL检查失败错误日志
      Logger.error(TAG, `SSL checked failed, error: ${event.error.toString()}`);
      // 取消SSL错误处理
      event.handler.handleCancel();
    })
    // 滚动事件处理
    .onScroll((event) => {
      // 如果有模块且有Y轴偏移
      if (param.module && event.yOffset) {
        // 获取对应模块的事件发射器
        const eventEmitter = eventEmitterMap.get(param.module);
        // 计算滚动偏移量
        const scrollOffset: number = event.yOffset - currentOffset;
        // 更新当前偏移量
        currentOffset = event.yOffset;
        // 判断滚动方向并触发相应事件
        if (scrollOffset > 0) {
          // 向下滚动
          eventEmitter && eventEmitter(ScrollDirectionEnum.DOWN, currentOffset);
        } else if (scrollOffset < 0) {
          // 向上滚动
          eventEmitter && eventEmitter(ScrollDirectionEnum.UP, currentOffset);
        }
      }
    })
    // 控制器附加事件处理
    .onControllerAttached(() => {
      try {
        // 激活Web控制器
        param.webController.onActive();
        // 获取并设置自定义用户代理
        const userAgent = `${param.webController.getUserAgent()} Mobile`;
        param.webController.setCustomUserAgent(userAgent);
        // 设置允许跨域访问的本地文件路径
        param.webController.setPathAllowingUniversalAccess([getContext().resourceDir]);
      } catch (error) {
        // 捕获异常并转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录Web用户代理设置错误日志
        Logger.error(TAG, `Web User-Agent setting error: ${err.code}, ${err.message}.`);
      }
    })
    // 设置宽度为100%
    .width('100%')
    // 设置高度为100%
    .height('100%')
}

/**
 * 原生操作数据类
 * 提供Web页面与原生应用交互的接口
 * 包含弹窗显示和页面跳转功能
 */
export class NativeActionData {
  // Web弹窗方法，接收源地址和类型参数
  public webSheet: (src: string, type: number) => void;
  // 页面跳转方法，接收类型、ID、当前索引和组件名称参数
  public jumpPage: (type: string, id: number, currentIndex?: number, componentName?: string) => void;

  /**
   * 构造函数
   * 初始化原生操作数据实例
   * @param url 关联的URL地址
   */
  constructor(url: string) {
    /**
     * Web弹窗方法实现
     * 根据URL获取对应的弹窗事件处理器并执行
     * @param src 弹窗源地址
     * @param type 弹窗类型
     */
    this.webSheet = (src: string, type: number) => {
      // 从弹窗事件映射表中获取对应的事件处理器
      const sheetEvent = sheetEventMap.get(url);
      // 如果事件处理器存在则执行
      if (sheetEvent !== undefined) {
        sheetEvent(src, type);
      }
    };

    /**
     * 页面跳转方法实现
     * 根据URL获取对应的页面事件处理器并执行
     * @param type 跳转类型
     * @param id 目标ID
     * @param currentIndex 当前索引，可选
     * @param componentName 组件名称，可选
     */
    this.jumpPage = (type: string, id: number, currentIndex?: number, componentName?: string) => {
      // 从页面事件映射表中获取对应的事件处理器
      const sheetEvent = pageEventMap.get(url);
      // 如果事件处理器存在则执行
      if (sheetEvent !== undefined) {
        sheetEvent(type, id, currentIndex, componentName);
      }
    }
  }
}

/**
 * Web工具类
 * 提供Web节点管理、控制器管理和各种Web相关功能
 * 包含Web组件的创建、更新、删除和事件处理等功能
 */
export class WebUtil {
  // 文章白名单方法数组，定义允许的方法列表
  public static readonly ARTICLE_WHITE_METHODS: string[] = ['checkPreview()', 'closePreview()'];

  /**
   * 添加Web节点方法
   * 根据URL添加对应的Web节点
   * @param url Web页面URL地址
   */
  static addNode(url: string) {
    // 从Web节点映射表中获取节点并添加
    webNodeMap.get(url)?.add();
  }

  /**
   * 初始化Web工具方法
   * 初始化Web引擎和相关映射表
   * @param windowStage 窗口舞台对象
   */
  public static initialize(windowStage: window.WindowStage) {
    try {
      // 获取主窗口的UI上下文
      context = windowStage.getMainWindowSync().getUIContext();
      // 清空Web节点映射表
      webNodeMap.clear();
      // 清空Web控制器映射表
      webControllerMap.clear();
      // 初始化Web引擎
      webview.WebviewController.initializeWebEngine();
      // 创建组件代码预览Web节点
      WebUtil.createWebNode(WebUtil.getComponentCodeUrl(), context, undefined, ModuleNameEnum.CODE_PREVIEW, true,
        false);
    } catch (err) {
      // 记录初始化失败错误日志
      Logger.error(TAG, `Initialize failed. Cause: ${err.code} ${err.message}`);
    }
  }

  /**
   * 移除Web节点方法
   * 根据URL移除对应的Web节点和相关资源
   * @param url Web页面URL地址
   */
  public static removeNode(url: string) {
    // 检查节点是否需要再次使用
    if (nodeRequiredMap.has(url)) {
      // 记录节点仍需使用的信息日志
      Logger.info(TAG, `Web Node is Required again, should not dispose: ${url}`);
      // 从需求映射表中删除该URL
      nodeRequiredMap.delete(url);
      return;
    }
    // 获取Web节点
    const webNode = WebUtil.getWebNode(url);
    // 销毁Web节点
    webNode?.disposeNode();
    // 从各映射表中删除相关记录
    webLoadedMap.delete(url);
    webNodeMap.delete(url);
    webControllerMap.delete(url);
    // 记录移除节点调试日志
    Logger.debug(TAG, `removeNode with url: ${url}`);
  }

  /**
   * 检查Web是否已加载方法
   * 检查指定URL的Web页面是否已加载完成
   * @param url Web页面URL地址
   * @returns boolean 是否已加载
   */
  public static checkWebLoaded(url: string): boolean {
    // 获取加载状态
    const loaded: boolean = !!webLoadedMap.get(url);
    // 如果未加载且有网络连接
    if (!loaded && NetworkUtil.hasDefaultNet()) {
      // 获取Web控制器
      const webController = WebUtil.getWebController(url);
      // 设置Web加载状态为true
      AppStorage.setOrCreate('webIsLoading', true);
      // 标记为已加载
      webLoadedMap.set(url, true);
      // 刷新Web页面
      webController?.refresh();
      return true;
    }
    // 返回加载状态
    return loaded;
  }

  /**
   * 创建Web节点方法
   * 创建新的Web节点并进行初始化配置
   * @param webUrl Web页面URL地址
   * @param uiContext UI上下文，可选
   * @param nestedScrollMode 嵌套滚动模式，可选
   * @param module 模块名称，可选
   * @param onlyWhiteMode 是否仅白色模式，可选
   * @param verticalScrollBarAccess 是否允许垂直滚动条，可选
   */
  public static createWebNode(webUrl: string, uiContext?: UIContext, nestedScrollMode?: NestedScrollMode,
    module?: ModuleNameEnum, onlyWhiteMode?: boolean, verticalScrollBarAccess?: boolean) {
    // 如果Web节点映射表中已存在该URL
    if (webNodeMap.has(webUrl)) {
      // 标记节点为需要状态
      nodeRequiredMap.set(webUrl, true);
      // 记录已存在Web节点的调试日志
      Logger.debug(TAG, `Has web node with url: ${webUrl}, should not create web node.`);
      return;
    }
    // 如果全局上下文不存在且传入了UI上下文
    if (!context && uiContext) {
      // 设置全局上下文
      context = uiContext;
    }
    // 记录初始化Web节点调试日志
    Logger.debug(TAG, `initWebNode with url: ${webUrl}`);
    // 创建Web节点控制器
    const webNode = new WebNodeController();
    // 创建Web视图控制器
    const webController = new webview.WebviewController();
    // 构建Web构建器参数对象
    const webBuilderParam: WebBuilderParam = {
      // Web页面URL
      webUrl: webUrl,
      // Web控制器
      webController: webController,
      // 默认嵌套滚动配置
      nestedScroll:
      {
        // 向前滚动模式为自身优先
        scrollForward: NestedScrollMode.SELF_FIRST,
        // 向后滚动模式为自身优先
        scrollBackward: NestedScrollMode.SELF_FIRST,
      },
      // 模块名称
      module: module,
      // 是否仅白色模式
      onlyWhiteMode,
      // 原生操作数据对象
      nativeActionData: new NativeActionData(webUrl),
      // 垂直滚动条访问权限，默认为false
      verticalScrollBarAccess: verticalScrollBarAccess || false,
    };
    // 如果指定了嵌套滚动模式
    if (nestedScrollMode !== undefined) {
      // 更新嵌套滚动配置
      webBuilderParam.nestedScroll = {
        scrollForward: nestedScrollMode,
        scrollBackward: nestedScrollMode,
      };
    }
    // 初始化Web节点
    webNode.initWebNode(context, webBuilderParam);
    // 设置Web加载状态
    webLoadedMap.set(webUrl, NetworkUtil.hasDefaultNet());
    // 将Web控制器添加到映射表
    webControllerMap.set(webUrl, webController);
    // 将Web节点添加到映射表
    webNodeMap.set(webUrl, webNode);
  }

  /**
   * 更新Web节点方法
   * 更新现有Web节点的配置参数
   * @param webUrl Web页面URL地址
   * @param nestedScrollMode 嵌套滚动模式，可选
   * @param module 模块名称，可选
   * @param onlyWhiteMode 是否仅白色模式，可选
   * @param verticalScrollBarAccess 是否允许垂直滚动条，可选
   */
  public static updateWebNode(webUrl: string, nestedScrollMode?: NestedScrollMode, module?: ModuleNameEnum,
    onlyWhiteMode?: boolean, verticalScrollBarAccess?: boolean) {
    // 从映射表中获取Web控制器
    const webController = webControllerMap.get(webUrl)!;
    // 构建Web构建器参数对象
    const webBuilderParam: WebBuilderParam = {
      // Web页面URL
      webUrl: webUrl,
      // Web控制器
      webController: webController,
      // 默认嵌套滚动配置
      nestedScroll:
      {
        // 向前滚动模式为自身优先
        scrollForward: NestedScrollMode.SELF_FIRST,
        // 向后滚动模式为自身优先
        scrollBackward: NestedScrollMode.SELF_FIRST,
      },
      // 模块名称
      module: module,
      // 是否仅白色模式
      onlyWhiteMode,
      // 原生操作数据对象
      nativeActionData: new NativeActionData(webUrl),
      // 垂直滚动条访问权限，默认为false
      verticalScrollBarAccess: verticalScrollBarAccess || false,
    };
    // 如果指定了嵌套滚动模式
    if (nestedScrollMode !== undefined) {
      // 更新嵌套滚动配置
      webBuilderParam.nestedScroll = {
        scrollForward: nestedScrollMode,
        scrollBackward: nestedScrollMode,
      };
    }
    // 从映射表中获取节点控制器
    const nodeController: WebNodeController | undefined = webNodeMap.get(webUrl);
    // 如果节点控制器存在
    if (nodeController) {
      // 更新Web节点
      nodeController.updateWebNode(webBuilderParam);
    }
  }

  /**
   * 获取Web节点方法
   * 根据URL获取对应的Web节点控制器
   * @param webUrl Web页面URL地址
   * @returns WebNodeController | undefined Web节点控制器或undefined
   */
  public static getWebNode(webUrl: string): WebNodeController | undefined {
    // 从Web节点映射表中获取节点
    return webNodeMap.get(webUrl);
  }

  /**
   * 获取Web控制器方法
   * 根据URL获取对应的Web视图控制器
   * @param webUrl Web页面URL地址
   * @returns webview.WebviewController | undefined Web视图控制器或undefined
   */
  public static getWebController(webUrl: string): webview.WebviewController | undefined {
    // 从Web控制器映射表中获取控制器
    return webControllerMap.get(webUrl);
  }

  /**
   * 设置Web控制器方法
   * 将Web视图控制器与URL关联并存储到映射表
   * @param webUrl Web页面URL地址
   * @param webViewController Web视图控制器
   */
  public static setWebController(webUrl: string, webViewController: webview.WebviewController): void {
    // 将Web控制器添加到映射表
    webControllerMap.set(webUrl, webViewController);
  }

  /**
   * 设置信任列表方法
   * 为指定URL的Web控制器设置URL信任列表
   * @param webUrl Web页面URL地址
   */
  public static setTrustList(webUrl: string): void {
    // 从映射表中获取Web控制器
    const webController: webview.WebviewController = webControllerMap.get(webUrl)!;
    try {
      // 获取白名单配置文件内容
      const whitelist: string = ResourceUtil.getRawFileStringByKey(getContext(), ConfigMapKey.WHITELIST);
      // 设置URL信任列表
      webController?.setUrlTrustList(whitelist);
    } catch (error) {
      // 捕获异常并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录Web用户代理设置错误日志
      Logger.error(TAG, `Web User-Agent setting error: ${err.code}, ${err.message}.`);
    }
  }

  /**
   * 检查URL方法
   * 验证和规范化URL地址
   * @param url 要检查的URL地址
   * @returns boolean 检查结果，当前总是返回false
   */
  public static checkUrl(url: string): boolean {
    // 创建URI对象
    const tempUri: uri.URI = new uri.URI(url);
    // 规范化URI
    const res: uri.URI = tempUri.normalize();
    // 记录Web请求URL调试日志
    Logger.debug(TAG, `Web request url : ${res.toString()}`);
    // 返回false（当前实现）
    return false;
  }

  /**
   * 注册事件发射器方法
   * 为指定模块注册事件回调函数
   * @param module 模块名称枚举
   * @param callback 回调函数
   */
  public static registerEmitter(module: ModuleNameEnum, callback: Function) {
    // 将模块和回调函数添加到事件发射器映射表
    eventEmitterMap.set(module, callback);
  }

  /**
   * 设置Web弹窗操作方法
   * 为指定URL设置弹窗操作回调函数
   * @param url Web页面URL地址
   * @param callback 弹窗回调函数
   */
  public static setWebSheetAction(url: string, callback: Function) {
    // 将URL和回调函数添加到弹窗事件映射表
    sheetEventMap.set(url, callback);
  }

  /**
   * 设置页面跳转操作方法
   * 为指定URL设置页面跳转回调函数
   * @param url Web页面URL地址
   * @param callback 页面跳转回调函数
   */
  public static setJumpPageAction(url: string, callback: Function) {
    // 将URL和回调函数添加到页面事件映射表
    pageEventMap.set(url, callback);
  }

  /**
   * 获取组件代码URL方法
   * 获取组件代码预览页面的完整URL路径
   * @returns string 组件代码URL
   */
  public static getComponentCodeUrl() {
    // 返回资源目录加上组件代码HTML路径
    return getContext().resourceDir + componentCodeHtml;
  }
}

/**
 * Web节点控制器类
 * 继承自NodeController，管理Web组件的节点生命周期
 * 提供Web节点的创建、更新、移除和销毁功能
 */
export class WebNodeController extends NodeController {
  // 根节点构建器，用于构建Web组件
  private rootNode: BuilderNode<WebBuilderParam[]> | null = null;
  // 是否移除标志
  private isRemove: boolean = false;

  /**
   * 创建节点方法
   * 根据当前状态创建或返回框架节点
   * @returns FrameNode | null 框架节点或null
   */
  makeNode(): FrameNode | null {
    // 如果标记为移除状态
    if (this.isRemove === true) {
      return null;
    }
    // 如果根节点存在
    if (this.rootNode) {
      // 返回根节点的框架节点
      return this.rootNode.getFrameNode();
    }
    // 默认返回null
    return null;
  }

  /**
   * 销毁节点方法
   * 销毁根节点并释放资源
   */
  disposeNode() {
    // 销毁根节点
    this.rootNode?.dispose();
  }

  /**
   * 移除节点方法
   * 临时移除节点并重建
   */
  remove() {
    // 设置移除标志为true
    this.isRemove = true;
    // 重建节点
    this.rebuild();
    // 重置移除标志为false
    this.isRemove = false;
  }

  /**
   * 添加节点方法
   * 添加节点并重建
   */
  add() {
    // 设置移除标志为false
    this.isRemove = false;
    // 重建节点
    this.rebuild();
  }

  /**
   * 初始化Web节点方法
   * 使用UI上下文和构建参数初始化Web节点
   * @param uiContext UI上下文
   * @param webBuilderParam Web构建器参数
   */
  initWebNode(uiContext: UIContext, webBuilderParam: WebBuilderParam) {
    // 如果根节点不存在
    if (!this.rootNode) {
      // 创建新的构建节点
      this.rootNode = new BuilderNode(uiContext);
      // 先销毁节点（清理状态）
      this.rootNode.dispose();
      // 使用Web构建器构建节点
      this.rootNode.build(wrapBuilder<WebBuilderParam[]>(webBuilder), webBuilderParam);
    }
  }

  /**
   * 更新Web节点方法
   * 使用新的构建参数更新Web节点
   * @param webBuilderParam Web构建器参数
   */
  updateWebNode(webBuilderParam: WebBuilderParam) {
    // 如果根节点存在
    if (this.rootNode) {
      // 更新根节点
      this.rootNode.update(webBuilderParam);
    }
  }
}

/**
 * JavaScript代理权限配置
 * 定义Web页面中JavaScript代理的权限配置
 * 包含URL权限列表和方法权限列表
 */
export const javascriptProxyPermission = `{
    "javascriptProxyPermission": {
      "urlPermissionList": [
        {
          "scheme": "resource",
          "host": "resfile",
          "port": "",
          "path": ""
        }
      ],
      "methodList": [
        {
          "methodName": "toHref",
          "urlPermissionList": [
            {
              "scheme": "resource",
              "host": "resfile",
              "port": "",
              "path": ""
            }
          ]
        },
        {
          "methodName": "jumpSampleDetail",
          "urlPermissionList": [
            {
              "scheme": "resource",
              "host": "resfile",
              "port": "",
              "path": ""
            }
          ]
        },
        {
          "methodName": "jumpComponentDetail",
          "urlPermissionList": [
            {
              "scheme": "resource",
              "host": "resfile",
              "port": "",
              "path": ""
            }
          ]
        }
      ]
    }
  }`;
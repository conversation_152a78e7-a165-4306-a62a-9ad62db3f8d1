// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入通用业务模块中的横幅数据类型
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的相关组件和类型
import {
  BannerCard,
  BaseHomeEventType,
  BaseHomeView,
  CalculateHeightParam,
  FullScreenNavigation,
  OffsetParam,
  TabBarType,
} from '@ohos/commonbusiness';
// 导入分类示例组件
import { CategorySamples } from '../component/CategorySamples';
// 导入示例分类类型
import type { SampleCategory } from '../model/SampleData';
// 导入实践状态类型
import type { PracticeState } from '../viewmodel/PracticeState';
// 导入实践视图模型和相关类型
import { LoadSamplePageParam, PracticeEventType, PracticeViewModel } from '../viewmodel/PracticeViewModel';

// 使用Component装饰器定义实践视图组件，设置非活跃时冻结
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 定义视图模型实例
  viewModel: PracticeViewModel = PracticeViewModel.getInstance();
  // 使用StorageProp和Watch装饰器获取全局信息模型并监听断点变化
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp和Watch装饰器获取系统颜色模式并监听变化
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 使用State装饰器定义导航状态高度
  @State naviStatusHeight: number = CommonConstants.NAVIGATION_HEIGHT + this.globalInfoModel.statusBarHeight;
  // 使用State装饰器定义当前索引
  @State currentIndex: number = 0;
  // 使用State装饰器定义实践状态
  @State practiceState: PracticeState = this.viewModel.getState();
  // 使用State装饰器定义是否显示顶部标签
  @State showTopTab: boolean = true;
  // 定义分类标签控制器私有变量
  private categoryTabController: TabsController = new TabsController();
  // 定义列表滚动器私有变量
  private listScroller: Scroller = new Scroller();
  // 定义头部滚动器私有变量
  private headerScroller: Scroller = new Scroller();
  // 定义示例页面上下文私有变量
  private samplePageContext: PageContext = AppStorage.get('samplePageContext')!;
  // 定义圆角数值私有变量
  private cornerNum: Length = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? $r('sys.float.corner_radius_level4') : '50%';

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 发送加载示例页面事件
    this.viewModel.sendEvent<LoadSamplePageParam>({
      type: PracticeEventType.LOAD_SAMPLE_PAGE,
      param: {
        callback: () => {
          // 获取分类数量
          const categorySize: number = this.practiceState.sampleCategories.length;
          // 创建预加载索引列表
          const preloadIndexList: number[] = [];
          // 遍历分类数量
          for (let i = 0; i < categorySize; i++) {
            // 添加索引到预加载列表
            preloadIndexList.push(i);
          }
          // 预加载标签项
          this.categoryTabController.preloadItems(preloadIndexList);
        }
      },
    });
  }

  // 定义处理断点变化的方法
  handleBreakPointChange() {
    // 发送处理断点变化事件
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_BREAKPOINT_CHANGE,
      param: { yOffset: (this.listScroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.SAMPLE },
    });
  }

  // 定义处理颜色模式变化的方法
  handleColorModeChange() {
    // 发送处理颜色变化事件
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_COLOR_CHANGE,
      param: { yOffset: (this.listScroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.SAMPLE },
    });
  }

  // 使用Builder装饰器定义分类头部构建器
  @Builder
  CategoryHeaderBuilder() {
    // 如果示例分类数量大于1
    if (this.practiceState.sampleCategories.length > 1) {
      // 创建滚动容器
      Scroll(this.headerScroller) {
        // 创建行容器
        Row({
          space: new BreakpointType({
            sm: CommonConstants.SPACE_10,
            md: CommonConstants.SPACE_12,
            lg: CommonConstants.SPACE_12,
          }).getValue(this.globalInfoModel.currentBreakpoint),
        }) {
          // 遍历示例分类
          ForEach(this.practiceState.sampleCategories, (item: SampleCategory, index: number) => {
            // 创建行容器
            Row() {
              // 如果有标签图标
              if (item.tabIcon) {
                // 创建图片组件
                Image($rawfile(this.currentIndex === index ? item.tabIcon : item.tabIconSelected))
                  // 设置图片高度
                  .height($r('app.float.tab_icon_height'))
                  // 设置图片适配方式
                  .objectFit(ImageFit.Contain)
                  // 设置图片右边距
                  .margin({ right: $r('sys.float.padding_level2') })
              }
              // 创建文本组件
              Text(item.categoryName)
                // 设置文本字体大小
                .fontSize($r('sys.float.Body_M'))
                // 设置文本字体粗细
                .fontWeight(FontWeight.Medium)
                // 根据当前索引设置文本颜色
                .fontColor(this.currentIndex === index ? $r('sys.color.font_on_primary') :
                $r('sys.color.font_tertiary'))
            }
            // 根据当前索引设置行背景颜色
            .backgroundColor(this.currentIndex === index ? $r('sys.color.brand') :
            $r('sys.color.ohos_id_color_button_normal'))
            // 设置行内边距
            .padding({
              left: $r('sys.float.padding_level8'),
              right: $r('sys.float.padding_level8'),
              top: $r('sys.float.padding_level4'),
              bottom: $r('sys.float.padding_level4'),
            })
            // 设置行圆角
            .borderRadius(this.cornerNum)
            // 设置行点击事件
            .onClick(() => {
              // 更新当前索引
              this.currentIndex = index;
              // 如果当前分类没有示例卡片
              if (!this.practiceState.sampleCategories[index].sampleCards) {
                // 计算顶部偏移量
                const topOffsetY =
                  (this.practiceState.bannerState.bannerHeight - this.globalInfoModel.naviIndicatorHeight -
                  CommonConstants.NAVIGATION_HEIGHT);
                // 如果当前滚动偏移量大于顶部偏移量
                if (this.listScroller?.currentOffset()?.yOffset > topOffsetY) {
                  // 滚动到指定位置
                  this.listScroller.scrollTo({ yOffset: topOffsetY, xOffset: 0 });
                }
              }
            })
          }, (item: SampleCategory) => item.id.toString())
        }
        // 设置行内边距
        .padding({
          left: new BreakpointType({
            sm: $r('sys.float.padding_level6'),
            md: $r('sys.float.padding_level12'),
            lg: $r('sys.float.padding_level16'),
          }).getValue(this.globalInfoModel.currentBreakpoint),
          right: $r('sys.float.padding_level6'),
          bottom: $r('sys.float.padding_level5'),
          top: $r('sys.float.padding_level8'),
        })
      }
      // 设置滚动容器对齐方式
      .align(Alignment.Start)
      // 设置滚动容器宽度
      .width('100%')
      // 隐藏滚动条
      .scrollBar(BarState.Off)
      // 设置水平滚动
      .scrollable(ScrollDirection.Horizontal)
    }
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建列表组件
    List({
      scroller: this.listScroller,
      space: this.practiceState.sampleCategories.length === 1 ? CommonConstants.SPACE_12 : 0,
    }) {
      // 创建列表项
      ListItem() {
        // 创建横幅卡片
        BannerCard({
          tabViewType: TabBarType.SAMPLE,
          bannerState: this.practiceState.bannerState,
          handleItemClick: (banner: BannerData) => {
            // 发送跳转横幅详情事件
            this.viewModel.sendEvent<BannerData>({ type: BaseHomeEventType.JUMP_BANNER_DETAIL, param: banner });
          },
        })
      }
      // 设置列表项高度
      .height(this.practiceState.bannerHeight)

      // 创建列表项组
      ListItemGroup({ header: this.CategoryHeaderBuilder() }) {
        // 创建列表项
        ListItem() {
          // 创建标签页组件
          Tabs({ index: this.currentIndex, controller: this.categoryTabController }) {
            // 遍历示例分类
            ForEach(this.practiceState.sampleCategories, (sampleCategory: SampleCategory) => {
              // 创建标签页内容
              TabContent() {
                // 创建分类示例组件
                CategorySamples({ sampleCategory })
              }
            }, (item: SampleCategory) => `${item.categoryType}_${item.sampleCards?.length}`)
          }
          // 禁用标签页滚动
          .scrollable(false)
          // 设置标签栏高度为0
          .barHeight(0)
        }
      }
      // 设置列表项组内边距
      .padding({
        left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? CommonConstants.TAB_BAR_WIDTH : 0,
      })
    }
    // 隐藏列表滚动条
    .scrollBar(BarState.Off)
    // 设置列表宽度为100%
    .width('100%')
    // 设置列表高度为100%
    .height('100%')
    // 禁用列表裁剪
    .clip(false)
    // 根据状态设置边缘效果
    .edgeEffect(this.practiceState.hasEdgeEffect ? EdgeEffect.Spring : EdgeEffect.None)
    // 设置粘性样式为头部
    .sticky(StickyStyle.Header)
    // 设置滚动帧开始事件
    .onScrollFrameBegin((offset: number, state: ScrollState) => {
      // 创建计算高度参数
      const param: CalculateHeightParam = { offset, state, yOffset: this.listScroller.currentOffset().yOffset };
      // 发送计算横幅高度事件
      const bannerChangeHeight: boolean | void = this.viewModel.sendEvent<CalculateHeightParam>({
        type: BaseHomeEventType.CALCULATE_BANNER_HEIGHT,
        param,
      });
      // 如果横幅高度发生变化
      if (bannerChangeHeight) {
        // 返回剩余偏移量为0
        return { offsetRemain: 0 };
      }
      // 返回原始偏移量
      return { offsetRemain: offset };
    })
    // 设置滚动完成事件
    .onDidScroll(() => {
      // 发送处理滚动偏移事件
      this.viewModel.sendEvent<OffsetParam>({
        type: BaseHomeEventType.HANDLE_SCROLL_OFFSET,
        param: { yOffset: this.listScroller.currentOffset().yOffset, tabIndex: TabBarType.SAMPLE },
      });
    })
    // 设置列表背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航组件
    FullScreenNavigation({
      topNavigationData: this.practiceState.topNavigationData,
      tabView: () => {
        // 调用分类头部构建器
        this.CategoryHeaderBuilder()
      },
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.samplePageContext.navPathStack) {
      // 创建基础主页视图
      BaseHomeView({
        loadingModel: this.practiceState.loadingModel,
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 发送加载示例页面事件
          this.viewModel.sendEvent({ type: PracticeEventType.LOAD_SAMPLE_PAGE, param: null });
        },
      })
    }
    // 设置导航模式为堆栈
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}
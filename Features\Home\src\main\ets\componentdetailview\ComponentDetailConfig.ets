// 导入操作表构建器类，用于构建操作表组件
import { ActionSheetBuilder } from './actionsheetview/component/ActionSheetBuilder'
// 导入操作表代码生成器类，用于生成操作表相关代码
import { ActionSheetCodeGenerator } from './actionsheetview/viewmodel/ActionSheetCodeGenerator'
// 导入操作表描述器类，用于描述操作表组件的属性和行为
import { ActionSheetDescriptor } from './actionsheetview/viewmodel/ActionSheetDescriptor'
// 导入AI字幕构建器类，用于构建AI字幕组件
import { AICaptionBuilder } from './aicaption/component/AICaptionBuilder'
// 导入AI字幕代码生成器类，用于生成AI字幕相关代码
import { AICaptionCodeGenerator } from './aicaption/viewmodel/AICaptionCodeGenerator'
// 导入警告对话框构建器类，用于构建警告对话框组件
import { AlertDialogBuilder } from './alertdialogview/component/AlertDialogBuilder'
// 导入警告对话框代码生成器类，用于生成警告对话框相关代码
import { AlertDialogCodeGenerator } from './alertdialogview/viewmodel/AlertDialogCodeGenerator'
// 导入警告对话框描述器类，用于描述警告对话框组件的属性和行为
import { AlertDialogDescriptor } from './alertdialogview/viewmodel/AlertDialogDescriptor'
// 导入应用链接构建器类，用于构建应用链接组件
import { AppLinkingBuilder } from './applinking/component/AppLinkingBuilder'
// 导入应用链接代码生成器类，用于生成应用链接相关代码
import { AppLinkingCodeGenerator } from './applinking/viewmodel/AppLinkingCodeGenerator'
// 导入应用链接描述器类，用于描述应用链接组件的属性和行为
import { AppLinkingDescriptor } from './applinking/viewmodel/AppLinkingDescriptor'
// 导入按钮构建器类，用于构建按钮组件
import { ButtonBuilder } from './buttonview/component/ButtonBuilder'
// 导入按钮代码生成器类，用于生成按钮相关代码
import { ButtonCodeGenerator } from './buttonview/viewmodel/ButtonCodeGenerator'
// 导入按钮描述器类，用于描述按钮组件的属性和行为
import { ButtonDescriptor } from './buttonview/viewmodel/ButtonDescriptor'
// 导入列布局构建器类，用于构建列布局组件
import { ColumnBuilder } from './columnview/component/ColumnBuilder'
// 导入列布局代码生成器类，用于生成列布局相关代码
import { ColumnCodeGenerator } from './columnview/viewmodel/ColumnCodeGenerator'
// 导入列布局描述器类，用于描述列布局组件的属性和行为
import { ColumnDescriptor } from './columnview/viewmodel/ColumnDescriptor'
// 导入通用代码生成器基类，用于生成通用组件代码
import { CommonCodeGenerator } from '../viewmodel/CommonCodeGenerator'
// 导入通用描述器基类，用于描述通用组件的属性和行为
import { CommonDescriptor } from '../viewmodel/CommonDescriptor'
// 导入描述器包装类，用于包装组件描述器
import { DescriptorWrapper } from '../viewmodel/DescriptorWrapper'
// 导入自定义对话框构建器类，用于构建自定义对话框组件
import { CustomDialogBuilder } from './customdialog/component/CustomDialogBuilder'
// 导入自定义对话框代码生成器类，用于生成自定义对话框相关代码
import { CustomDialogCodeGenerator } from './customdialog/viewmodel/CustomDialogCodeGenerator'
// 导入自定义对话框描述器类，用于描述自定义对话框组件的属性和行为
import { CustomDialogDescriptor } from './customdialog/viewmodel/CustomDialogDescriptor'
// 导入文档视图选择器构建器类，用于构建文档视图选择器组件
import { DocumentViewPickerBuilder } from './documentviewpicker/component/DocumentViewPickerBuilder'
// 导入文档视图选择器代码生成器类，用于生成文档视图选择器相关代码
import { DocumentViewPickerCodeGenerator } from './documentviewpicker/viewmodel/DocumentViewPickerCodeGenerator'
// 导入弹性布局构建器类，用于构建弹性布局组件
import { FlexBuilder } from './flex/component/FlexBuilder'
// 导入弹性布局代码生成器类，用于生成弹性布局相关代码
import { FlexCodeGenerator } from './flex/viewmodel/FlexCodeGenerator'
// 导入弹性布局描述器类，用于描述弹性布局组件的属性和行为
import { FlexDescriptor } from './flex/viewmodel/FlexDescriptor'
// 导入网格布局构建器类，用于构建网格布局组件
import { GridBuilder } from './grid/component/GridBuilder'
// 导入网格布局代码生成器类，用于生成网格布局相关代码
import { GridCodeGenerator } from './grid/viewmodel/GridCodeGenerator'
// 导入网格布局描述器类，用于描述网格布局组件的属性和行为
import { GridDescriptor } from './grid/viewmodel/GridDescriptor'
// 导入图片构建器类，用于构建图片组件
import { ImageBuilder } from './Image/component/ImageBuilder'
// 导入图片代码生成器类，用于生成图片相关代码
import { ImageCodeGenerator } from './Image/viewmodel/ImageCodeGenerator'
// 导入图片描述器类，用于描述图片组件的属性和行为
import { ImageDescriptor } from './Image/viewmodel/ImageDescriptor'
// 导入AI图片构建器类，用于构建AI图片分析组件
import { AIImageBuilder } from './imageaianalyzer/component/AIImageBuilder'
// 导入AI图片代码生成器类，用于生成AI图片分析相关代码
import { AIImageCodeGenerator } from './imageaianalyzer/viewmodel/AIImageCodeGenerator'
// 导入列表构建器类，用于构建列表组件
import { ListBuilder } from './list/component/ListBuilder'
// 导入列表代码生成器类，用于生成列表相关代码
import { ListCodeGenerator } from './list/viewmodel/ListCodeGenerator'
// 导入列表描述器类，用于描述列表组件的属性和行为
import { ListDescriptor } from './list/viewmodel/ListDescriptor'
// 导入手写笔工具包构建器类，用于构建手写笔工具包组件
import { PenKitBuilder } from './penkit/component/PenKitBuilder'
// 导入手写笔工具包代码生成器类，用于生成手写笔工具包相关代码
import { PenKitCodeGenerator } from './penkit/viewmodel/PenKitCodeGenerator'
// 导入照片视图选择器构建器类，用于构建照片视图选择器组件
import { PhotoViewPickerBuilder } from './photopicker/component/PhotoViewPickerBuilder'
// 导入照片视图选择器代码生成器类，用于生成照片视图选择器相关代码
import { PhotoViewPickerCodeGenerator } from './photopicker/viewmodel/PhotoViewPickerCodeGenerator'
// 导入日历选择器构建器类，用于构建日历选择器组件
import { CalendarPickerBuilder } from './picker/calendarPicker/component/CalendarPickerBuilder'
// 导入日历选择器代码生成器类，用于生成日历选择器相关代码
import { CalendarPickerCodeGenerator } from './picker/calendarPicker/viewmodel/CalendarPickerCodeGenerator'
// 导入日历选择器描述器类，用于描述日历选择器组件的属性和行为
import { CalendarPickerDescriptor } from './picker/calendarPicker/viewmodel/CalendarPickerDescriptor'
// 导入相机选择器构建器类，用于构建相机选择器组件
import { CameraPickerBuilder } from './picker/camerapicker/component/CameraPickerBuilder'
// 导入相机选择器代码生成器类，用于生成相机选择器相关代码
import { CameraPickerCodeGenerator } from './picker/camerapicker/viewmodel/CameraPickerCodeGenerator'
// 导入日期选择器构建器类，用于构建日期选择器组件
import { DatePickerBuilder } from './picker/datepicker/component/DatePickerBuilder'
// 导入日期选择器代码生成器类，用于生成日期选择器相关代码
import { DatePickerCodeGenerator } from './picker/datepicker/viewmodel/DatePickerCodeGenerator'
// 导入日期选择器描述器类，用于描述日期选择器组件的属性和行为
import { DatePickerDescriptor } from './picker/datepicker/viewmodel/DatePickerDescriptor'
// 导入弹出框构建器类，用于构建弹出框组件
import { PopupBuilder } from './popup/component/PopupBuilder'
// 导入弹出框代码生成器类，用于生成弹出框相关代码
import { PopupCodeGenerator } from './popup/viewmodel/PopupCodeGenerator'
// 导入弹出框描述器类，用于描述弹出框组件的属性和行为
import { PopupDescriptor } from './popup/viewmodel/PopupDescriptor'
// 导入进度条构建器类，用于构建进度条组件
import { ProgressBuilder } from './progress/component/ProgressBuilder'
// 导入进度条代码生成器类，用于生成进度条相关代码
import { ProgressCodeGenerator } from './progress/viewmodel/ProgressCodeGenerator'
// 导入进度条描述器类，用于描述进度条组件的属性和行为
import { ProgressDescriptor } from './progress/viewmodel/ProgressDescriptor'
// 导入评分组件构建器类，用于构建评分组件
import { RatingBuilder } from './rating/component/RatingBuilder'
// 导入评分组件代码生成器类，用于生成评分组件相关代码
import { RatingCodeGenerator } from './rating/viewmodel/RatingCodeGenerator'
// 导入评分组件描述器类，用于描述评分组件的属性和行为
import { RatingDescriptor } from './rating/viewmodel/RatingDescriptor'
// 导入行布局构建器类，用于构建行布局组件
import { RowBuilder } from './rowview/component/RowBuilder'
// 导入行布局代码生成器类，用于生成行布局相关代码
import { RowCodeGenerator } from './rowview/viewmodel/RowCodeGenerator'
// 导入行布局描述器类，用于描述行布局组件的属性和行为
import { RowDescriptor } from './rowview/viewmodel/RowDescriptor'
// 导入堆叠布局构建器类，用于构建堆叠布局组件
import { StackBuilder } from './stackview/component/StackBuilder'
// 导入堆叠布局代码生成器类，用于生成堆叠布局相关代码
import { StackCodeGenerator } from './stackview/viewmodel/StackCodeGenerator'
// 导入堆叠布局描述器类，用于描述堆叠布局组件的属性和行为
import { StackDescriptor } from './stackview/viewmodel/StackDescriptor'
// 导入样式文本构建器类，用于构建样式文本组件
import { StyleTextBuilder } from './styletext/component/StyleTextBuilder'
// 导入样式文本代码生成器类，用于生成样式文本相关代码
import { StyleTextCodeGenerator } from './styletext/viewmodel/StyleTextCodeGenerator'
// 导入样式文本描述器类，用于描述样式文本组件的属性和行为
import { StyleTextDescriptor } from './styletext/viewmodel/StyleTextDescriptor'
// 导入轮播图构建器类，用于构建轮播图组件
import { SwiperBuilder } from './swiperView/component/SwiperBuilder'
// 导入轮播图代码生成器类，用于生成轮播图相关代码
import { SwiperCodeGenerator } from './swiperView/viewmodel/SwiperCodeGenerator'
// 导入轮播图描述器类，用于描述轮播图组件的属性和行为
import { SwiperDescriptor } from './swiperView/viewmodel/SwiperDescriptor'
// 导入选项卡构建器类，用于构建选项卡组件
import { TabBuilder } from './tabview/component/TabBuilder'
// 导入选项卡代码生成器类，用于生成选项卡相关代码
import { TabCodeGenerator } from './tabview/viewmodel/TabCodeGenerator'
// 导入选项卡描述器类，用于描述选项卡组件的属性和行为
import { TabDescriptor } from './tabview/viewmodel/TabDescriptor'
// 导入文本区域构建器类，用于构建文本区域组件
import { TextAreaBuilder } from './textarea/component/TextAreaBuilder'
// 导入文本区域代码生成器类，用于生成文本区域相关代码
import { TextAreaCodeGenerator } from './textarea/viewmodel/TextAreaCodeGenerator'
// 导入文本区域描述器类，用于描述文本区域组件的属性和行为
import { TextAreaDescriptor } from './textarea/viewmodel/TextAreaDescriptor'
// 导入文本输入框构建器类，用于构建文本输入框组件
import { TextInputBuilder } from './textinput/component/TextInputBuilder'
// 导入文本输入框代码生成器类，用于生成文本输入框相关代码
import { TextInputCodeGenerator } from './textinput/viewmodel/TextInputCodeGenerator'
// 导入文本输入框描述器类，用于描述文本输入框组件的属性和行为
import { TextInputDescriptor } from './textinput/viewmodel/TextInputDescriptor'
// 导入文本选择器对话框构建器类，用于构建文本选择器对话框组件
import { TextPickerDialogBuilder } from './textpickerdialogview/component/TextPickerDialogBuilder'
// 导入文本选择器对话框代码生成器类，用于生成文本选择器对话框相关代码
import { TextPickerDialogCodeGenerator } from './textpickerdialogview/viewmodel/TextPickerDialogCodeGenerator'
// 导入文本选择器对话框描述器类，用于描述文本选择器对话框组件的属性和行为
import { TextPickerDialogDescriptor } from './textpickerdialogview/viewmodel/TextPickerDialogDescriptor'
// 导入文本转语音构建器类，用于构建文本转语音组件
import { TextToSpeechBuilder } from './texttospeech/component/TextToSpeechBuilder'
// 导入文本转语音代码生成器类，用于生成文本转语音相关代码
import { TextToSpeechCodeGenerator } from './texttospeech/viewmodel/TextToSpeechCodeGenerator'
// 导入文本转语音描述器类，用于描述文本转语音组件的属性和行为
import { TextToSpeechDescriptor } from './texttospeech/viewmodel/TextToSpeechDescriptor'
// 导入文本构建器类，用于构建文本组件
import { TextBuilder } from './textview/component/TextBuilder'
// 导入文本代码生成器类，用于生成文本相关代码
import { TextCodeGenerator } from './textview/viewmodel/TextCodeGenerator'
// 导入文本描述器类，用于描述文本组件的属性和行为
import { TextDescriptor } from './textview/viewmodel/TextDescriptor'
// 导入开关构建器类，用于构建开关组件
import { ToggleBuilder } from './toggleview/component/ToggleBuilder'
// 导入开关代码生成器类，用于生成开关相关代码
import { ToggleCodeGenerator } from './toggleview/viewmodel/ToggleCodeGenerator'
// 导入开关描述器类，用于描述开关组件的属性和行为
import { ToggleDescriptor } from './toggleview/viewmodel/ToggleDescriptor'
// 导入瀑布流构建器类，用于构建瀑布流组件
import { WaterFlowBuilder } from './waterflow/component/WaterFlowBuilder'
// 导入瀑布流代码生成器类，用于生成瀑布流相关代码
import { WaterFlowCodeGenerator } from './waterflow/viewmodel/WaterFlowCodeGenerator'
// 导入瀑布流描述器类，用于描述瀑布流组件的属性和行为
import { WaterFlowDescriptor } from './waterflow/viewmodel/WaterFlowDescriptor'
// 导入通用属性过滤器基类，用于过滤组件的通用属性
import { CommonAttributeFilter } from '../viewmodel/CommonAttributeFilter'
// 导入堆叠布局属性过滤器类，用于过滤堆叠布局组件的属性
import { StackAttributeFilter } from './stackview/viewmodel/StackAttributeFilter'
// 导入评分组件属性过滤器类，用于过滤评分组件的属性
import { RatingAttributeFilter } from './rating/viewmodel/RatingAttributeFilter'
// 导入按钮属性过滤器类，用于过滤按钮组件的属性
import { ButtonAttributeFilter } from './buttonview/viewmodel/ButtonAttributeFilter'
// 导入瀑布流属性过滤器类，用于过滤瀑布流组件的属性
import { WaterFlowAttributeFilter } from './waterflow/viewmodel/WaterflowAttributeFilter'
// 导入弹性布局属性过滤器类，用于过滤弹性布局组件的属性
import { FlexAttributeFilter } from './flex/viewmodel/FlexAttributeFilter'
// 导入进度条属性过滤器类，用于过滤进度条组件的属性
import { ProgressAttributeFilter } from './progress/viewmodel/ProgressAttributeFilter'
// 导入开关属性过滤器类，用于过滤开关组件的属性
import { ToggleAttributeFilter } from './toggleview/viewmodel/ToggleAttributeFilter'
// 导入列布局属性过滤器类，用于过滤列布局组件的属性
import { ColumnAttributeFilter } from './columnview/viewmodel/ColumnAttributeFilter'
// 导入行布局属性过滤器类，用于过滤行布局组件的属性
import { RowAttributeFilter } from './rowview/viewmodel/RowAttributeFilter'
// 导入相机选择器描述器类，用于描述相机选择器组件的属性和行为
import { CameraPickerDescriptor } from './picker/camerapicker/viewmodel/CameraPickerDescriptor'

/**
 * 组件详情配置对象
 * 用于配置各种UI组件的构建器、描述器、代码生成器和属性过滤器
 * 每个组件配置包含预览组件构建器、描述器工厂函数、代码生成器和可选的属性过滤器
 */
export const componentDetailConfig: Record<string, ConfigInterface> = {
  // 按钮组件配置
  'Button': {
    // 预览组件构建器，用于构建按钮预览组件
    previewComponentBuilder: wrapBuilder(ButtonBuilder),
    // 描述器工厂函数，返回按钮描述器实例
    descriptor: () => new ButtonDescriptor(),
    // 代码生成器，用于生成按钮相关代码
    codeGenerate: new ButtonCodeGenerator(),
    // 属性过滤器，用于过滤按钮属性
    attributeFilter: new ButtonAttributeFilter(),
  },
  // 列布局组件配置
  'Column': {
    // 预览组件构建器，用于构建列布局预览组件
    previewComponentBuilder: wrapBuilder(ColumnBuilder),
    // 描述器工厂函数，返回列布局描述器实例
    descriptor: () => new ColumnDescriptor(),
    // 代码生成器，用于生成列布局相关代码
    codeGenerate: new ColumnCodeGenerator(),
    // 属性过滤器，用于过滤列布局属性
    attributeFilter: new ColumnAttributeFilter(),
  },
  // 行布局组件配置
  'Row': {
    // 预览组件构建器，用于构建行布局预览组件
    previewComponentBuilder: wrapBuilder(RowBuilder),
    // 描述器工厂函数，返回行布局描述器实例
    descriptor: () => new RowDescriptor(),
    // 代码生成器，用于生成行布局相关代码
    codeGenerate: new RowCodeGenerator(),
    // 属性过滤器，用于过滤行布局属性
    attributeFilter: new RowAttributeFilter(),
  },
  // 堆叠布局组件配置
  'Stack': {
    // 预览组件构建器，用于构建堆叠布局预览组件
    previewComponentBuilder: wrapBuilder(StackBuilder),
    // 描述器工厂函数，返回堆叠布局描述器实例
    descriptor: () => new StackDescriptor(),
    // 代码生成器，用于生成堆叠布局相关代码
    codeGenerate: new StackCodeGenerator(),
    // 属性过滤器，用于过滤堆叠布局属性
    attributeFilter: new StackAttributeFilter(),
  },
  // 网格布局组件配置
  'Grid': {
    // 预览组件构建器，用于构建网格布局预览组件
    previewComponentBuilder: wrapBuilder(GridBuilder),
    // 描述器工厂函数，返回网格布局描述器实例
    descriptor: () => new GridDescriptor(),
    // 代码生成器，用于生成网格布局相关代码
    codeGenerate: new GridCodeGenerator(),
  },
  // 进度条组件配置
  'Progress': {
    // 预览组件构建器，用于构建进度条预览组件
    previewComponentBuilder: wrapBuilder(ProgressBuilder),
    // 描述器工厂函数，返回进度条描述器实例
    descriptor: () => new ProgressDescriptor(),
    // 代码生成器，用于生成进度条相关代码
    codeGenerate: new ProgressCodeGenerator(),
    // 属性过滤器，用于过滤进度条属性
    attributeFilter: new ProgressAttributeFilter(),
  },
  // 文本组件配置
  'Text': {
    // 预览组件构建器，用于构建文本预览组件
    previewComponentBuilder: wrapBuilder(TextBuilder),
    // 描述器工厂函数，返回文本描述器实例
    descriptor: () => new TextDescriptor(),
    // 代码生成器，用于生成文本相关代码
    codeGenerate: new TextCodeGenerator(),
  },
  // 文本区域组件配置
  'TextArea': {
    // 预览组件构建器，用于构建文本区域预览组件
    previewComponentBuilder: wrapBuilder(TextAreaBuilder),
    // 描述器工厂函数，返回文本区域描述器实例
    descriptor: () => new TextAreaDescriptor(),
    // 代码生成器，用于生成文本区域相关代码
    codeGenerate: new TextAreaCodeGenerator(),
  },
  // 文本输入框组件配置
  'TextInput': {
    // 预览组件构建器，用于构建文本输入框预览组件
    previewComponentBuilder: wrapBuilder(TextInputBuilder),
    // 描述器工厂函数，返回文本输入框描述器实例
    descriptor: () => new TextInputDescriptor(),
    // 代码生成器，用于生成文本输入框相关代码
    codeGenerate: new TextInputCodeGenerator(),
  },
  // 样式文本组件配置
  'TextStyle': {
    // 预览组件构建器，用于构建样式文本预览组件
    previewComponentBuilder: wrapBuilder(StyleTextBuilder),
    // 描述器工厂函数，返回样式文本描述器实例
    descriptor: () => new StyleTextDescriptor(),
    // 代码生成器，用于生成样式文本相关代码
    codeGenerate: new StyleTextCodeGenerator(),
  },
  // 图片组件配置
  'Image': {
    // 预览组件构建器，用于构建图片预览组件
    previewComponentBuilder: wrapBuilder(ImageBuilder),
    // 描述器工厂函数，返回图片描述器实例
    descriptor: () => new ImageDescriptor(),
    // 代码生成器，用于生成图片相关代码
    codeGenerate: new ImageCodeGenerator()
  },
  // 评分组件配置
  'Rating': {
    // 预览组件构建器，用于构建评分预览组件
    previewComponentBuilder: wrapBuilder(RatingBuilder),
    // 描述器工厂函数，返回评分描述器实例
    descriptor: () => new RatingDescriptor(),
    // 代码生成器，用于生成评分相关代码
    codeGenerate: new RatingCodeGenerator(),
    // 属性过滤器，用于过滤评分属性
    attributeFilter: new RatingAttributeFilter(),
  },
  // 开关组件配置
  'Toggle': {
    // 预览组件构建器，用于构建开关预览组件
    previewComponentBuilder: wrapBuilder(ToggleBuilder),
    // 描述器工厂函数，返回开关描述器实例
    descriptor: () => new ToggleDescriptor(),
    // 代码生成器，用于生成开关相关代码
    codeGenerate: new ToggleCodeGenerator(),
    // 属性过滤器，用于过滤开关属性
    attributeFilter: new ToggleAttributeFilter(),
  },
  // 文本转语音组件配置
  'TextToSpeech': {
    // 预览组件构建器，用于构建文本转语音预览组件
    previewComponentBuilder: wrapBuilder(TextToSpeechBuilder),
    // 描述器工厂函数，返回文本转语音描述器实例
    descriptor: () => new TextToSpeechDescriptor(),
    // 代码生成器，用于生成文本转语音相关代码
    codeGenerate: new TextToSpeechCodeGenerator(),
  },
  // AI字幕组件配置
  'AICaptionComponent': {
    // 预览组件构建器，用于构建AI字幕预览组件
    previewComponentBuilder: wrapBuilder(AICaptionBuilder),
    // 描述器工厂函数，返回通用描述器实例
    descriptor: () => new CommonDescriptor(),
    // 代码生成器，用于生成AI字幕相关代码
    codeGenerate: new AICaptionCodeGenerator(),
  },
  // 弹性布局组件配置
  'Flex': {
    // 预览组件构建器，用于构建弹性布局预览组件
    previewComponentBuilder: wrapBuilder(FlexBuilder),
    // 描述器工厂函数，返回弹性布局描述器实例
    descriptor: () => new FlexDescriptor(),
    // 代码生成器，用于生成弹性布局相关代码
    codeGenerate: new FlexCodeGenerator(),
    // 属性过滤器，用于过滤弹性布局属性
    attributeFilter: new FlexAttributeFilter(),
  },
  // 列表组件配置
  'List': {
    // 预览组件构建器，用于构建列表预览组件
    previewComponentBuilder: wrapBuilder(ListBuilder),
    // 描述器工厂函数，返回列表描述器实例
    descriptor: () => new ListDescriptor(),
    // 代码生成器，用于生成列表相关代码
    codeGenerate: new ListCodeGenerator(),
  },
  // 瀑布流组件配置
  'WaterFlow': {
    // 预览组件构建器，用于构建瀑布流预览组件
    previewComponentBuilder: wrapBuilder(WaterFlowBuilder),
    // 描述器工厂函数，返回瀑布流描述器实例
    descriptor: () => new WaterFlowDescriptor(),
    // 代码生成器，用于生成瀑布流相关代码
    codeGenerate: new WaterFlowCodeGenerator(),
    // 属性过滤器，用于过滤瀑布流属性
    attributeFilter: new WaterFlowAttributeFilter(),
  },
  // 选项卡组件配置
  'Tabs': {
    // 预览组件构建器，用于构建选项卡预览组件
    previewComponentBuilder: wrapBuilder(TabBuilder),
    // 描述器工厂函数，返回选项卡描述器实例
    descriptor: () => new TabDescriptor(),
    // 代码生成器，用于生成选项卡相关代码
    codeGenerate: new TabCodeGenerator(),
  },
  // 轮播图组件配置
  'Swiper': {
    // 预览组件构建器，用于构建轮播图预览组件
    previewComponentBuilder: wrapBuilder(SwiperBuilder),
    // 描述器工厂函数，返回轮播图描述器实例
    descriptor: () => new SwiperDescriptor(),
    // 代码生成器，用于生成轮播图相关代码
    codeGenerate: new SwiperCodeGenerator(),
  },
  // 手写笔工具包组件配置
  'Penkit': {
    // 预览组件构建器，用于构建手写笔工具包预览组件
    previewComponentBuilder: wrapBuilder(PenKitBuilder),
    // 描述器工厂函数，返回通用描述器实例
    descriptor: () => new CommonDescriptor(),
    // 代码生成器，用于生成手写笔工具包相关代码
    codeGenerate: new PenKitCodeGenerator(),
  },
  // 警告对话框组件配置
  'AlertDialog': {
    // 预览组件构建器，用于构建警告对话框预览组件
    previewComponentBuilder: wrapBuilder(AlertDialogBuilder),
    // 描述器工厂函数，返回警告对话框描述器实例
    descriptor: () => new AlertDialogDescriptor(),
    // 代码生成器，用于生成警告对话框相关代码
    codeGenerate: new AlertDialogCodeGenerator(),
  },
  // 文本选择器对话框组件配置
  'TextPickerDialog': {
    // 预览组件构建器，用于构建文本选择器对话框预览组件
    previewComponentBuilder: wrapBuilder(TextPickerDialogBuilder),
    // 描述器工厂函数，返回文本选择器对话框描述器实例
    descriptor: () => new TextPickerDialogDescriptor(),
    // 代码生成器，用于生成文本选择器对话框相关代码
    codeGenerate: new TextPickerDialogCodeGenerator(),
  },
  // 自定义对话框组件配置
  'CustomDialog': {
    // 预览组件构建器，用于构建自定义对话框预览组件
    previewComponentBuilder: wrapBuilder(CustomDialogBuilder),
    // 描述器工厂函数，返回自定义对话框描述器实例
    descriptor: () => new CustomDialogDescriptor(),
    // 代码生成器，用于生成自定义对话框相关代码
    codeGenerate: new CustomDialogCodeGenerator(),
  },
  // 操作表组件配置
  'ActionSheet': {
    // 预览组件构建器，用于构建操作表预览组件
    previewComponentBuilder: wrapBuilder(ActionSheetBuilder),
    // 描述器工厂函数，返回操作表描述器实例
    descriptor: () => new ActionSheetDescriptor(),
    // 代码生成器，用于生成操作表相关代码
    codeGenerate: new ActionSheetCodeGenerator(),
  },
  // 弹出框组件配置
  'Popup': {
    // 预览组件构建器，用于构建弹出框预览组件
    previewComponentBuilder: wrapBuilder(PopupBuilder),
    // 描述器工厂函数，返回弹出框描述器实例
    descriptor: () => new PopupDescriptor(),
    // 代码生成器，用于生成弹出框相关代码
    codeGenerate: new PopupCodeGenerator(),
  },
  // 日历选择器组件配置
  'CalendarPicker': {
    // 预览组件构建器，用于构建日历选择器预览组件
    previewComponentBuilder: wrapBuilder(CalendarPickerBuilder),
    // 描述器工厂函数，返回日历选择器描述器实例
    descriptor: () => new CalendarPickerDescriptor(),
    // 代码生成器，用于生成日历选择器相关代码
    codeGenerate: new CalendarPickerCodeGenerator(),
  },
  // 日期选择器组件配置
  'DatePicker': {
    // 预览组件构建器，用于构建日期选择器预览组件
    previewComponentBuilder: wrapBuilder(DatePickerBuilder),
    // 描述器工厂函数，返回日期选择器描述器实例
    descriptor: () => new DatePickerDescriptor(),
    // 代码生成器，用于生成日期选择器相关代码
    codeGenerate: new DatePickerCodeGenerator(),
  },
  // 文档视图选择器组件配置
  'DocumentViewPicker': {
    // 预览组件构建器，用于构建文档视图选择器预览组件
    previewComponentBuilder: wrapBuilder(DocumentViewPickerBuilder),
    // 描述器工厂函数，返回通用描述器实例
    descriptor: () => new CommonDescriptor(),
    // 代码生成器，用于生成文档视图选择器相关代码
    codeGenerate: new DocumentViewPickerCodeGenerator(),
  },
  // 应用链接组件配置
  'AppLinking': {
    // 预览组件构建器，用于构建应用链接预览组件
    previewComponentBuilder: wrapBuilder(AppLinkingBuilder),
    // 描述器工厂函数，返回应用链接描述器实例
    descriptor: () => new AppLinkingDescriptor(),
    // 代码生成器，用于生成应用链接相关代码
    codeGenerate: new AppLinkingCodeGenerator(),
  },
  // 相机选择器组件配置
  'CameraPicker': {
    // 预览组件构建器，用于构建相机选择器预览组件
    previewComponentBuilder: wrapBuilder(CameraPickerBuilder),
    // 描述器工厂函数，返回相机选择器描述器实例
    descriptor: () => new CameraPickerDescriptor(),
    // 代码生成器，用于生成相机选择器相关代码
    codeGenerate: new CameraPickerCodeGenerator(),
  },
  // 照片视图选择器组件配置
  'PhotoViewPicker': {
    // 预览组件构建器，用于构建照片视图选择器预览组件
    previewComponentBuilder: wrapBuilder(PhotoViewPickerBuilder),
    // 描述器工厂函数，返回通用描述器实例
    descriptor: () => new CommonDescriptor(),
    // 代码生成器，用于生成照片视图选择器相关代码
    codeGenerate: new PhotoViewPickerCodeGenerator(),
  },
  // AI抠图组件配置
  'AI Matting': {
    // 预览组件构建器，用于构建AI抠图预览组件
    previewComponentBuilder: wrapBuilder(AIImageBuilder),
    // 描述器工厂函数，返回通用描述器实例
    descriptor: () => new CommonDescriptor(),
    // 代码生成器，用于生成AI抠图相关代码
    codeGenerate: new AIImageCodeGenerator(),
  },
}

/**
 * 配置接口定义
 * 定义了组件配置对象的结构，包含预览构建器、描述器、代码生成器和可选的属性过滤器
 */
export interface ConfigInterface {
  // 预览组件构建器，用于构建组件的预览界面，接受描述器包装类作为参数
  previewComponentBuilder: WrappedBuilder<[DescriptorWrapper]>;
  // 描述器工厂函数，返回组件的描述器实例，用于描述组件的属性和行为
  descriptor: () => CommonDescriptor;
  // 代码生成器，用于生成组件相关的代码
  codeGenerate: CommonCodeGenerator;
  // 可选的属性过滤器，用于过滤和处理组件的属性
  attributeFilter?: CommonAttributeFilter;
}
// 导入通用模块中的基础视图模型、断点类型枚举、通用常量、全局信息模型、模块名称枚举、页面上下文、滚动方向枚举和Web工具
import {
  BaseVM,
  BreakpointTypeEnum,
  CommonConstants,
  GlobalInfoModel,
  ModuleNameEnum,
  PageContext,
  ScrollDirectionEnum,
  WebUtil
} from '@ohos/common';
// 导入代码预览状态
import { CodePreviewState } from './CodePreviewState';
// 导入详情页面常量
import { DetailPageConstant } from '../constant/DetailPageConstant';

// 定义代码预览页面视图模型类，继承基础视图模型
export class CodePreviewPageVM extends BaseVM<CodePreviewState> {
  // 定义静态实例变量
  private static instance: CodePreviewPageVM;
  // 定义私有全局信息模型，从应用存储获取
  private globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

  // 定义构造函数
  constructor() {
    // 调用父类构造函数，传入新的代码预览状态
    super(new CodePreviewState(0, 0, 1));
  }

  // 定义获取实例的静态方法
  public static getInstance(): CodePreviewPageVM {
    // 如果实例不存在则创建新实例
    if (!CodePreviewPageVM.instance) {
      CodePreviewPageVM.instance = new CodePreviewPageVM();
    }
    // 返回实例
    return CodePreviewPageVM.instance;
  }

  // 定义发送事件的方法
  sendEvent(event: CodePreviewEvent): void {
    // 判断事件类型是否为初始化
    if (event === CodePreviewEvent.INIT) {
      // 调用初始化方法
      this.init();
      // 重置导航视图状态
      this.resetNavigationViewState();
    }
  }

  // 定义弹出页面的公共方法
  public pop(animated?: boolean): void {
    // 根据断点类型获取页面上下文
    const pageContext: PageContext =
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('componentListPageContext')! :
        AppStorage.get('pageContext')!;
    // 弹出页面
    pageContext.popPage(animated);
  }

  // 定义私有初始化方法
  private init(): void {
    // 注册Web工具事件发射器
    WebUtil.registerEmitter(ModuleNameEnum.CODE_PREVIEW, this.changeScrollDirection);
  }

  // 定义私有重置导航视图状态方法
  private resetNavigationViewState() {
    // 设置导航透明度为1
    this.state.navigationOpacity = 1;
    // 设置顶部平移Y为0
    this.state.topTranslateY = 0;
    // 设置底部平移Y为0
    this.state.bottomTranslateY = 0;
  }

  // 定义私有改变滚动方向的箭头函数
  private changeScrollDirection = (direction: string, offset: number): void => {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get<GlobalInfoModel>('GlobalInfoModel')!;
    // 如果当前断点为XL则直接返回
    if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      return;
    }
    // 执行动画
    animateTo({
      duration: 300,
      curve: Curve.EaseOut,
    }, () => {
      // 判断滚动方向是否为向下
      if (direction === ScrollDirectionEnum.DOWN) {
        // 计算导航透明度
        this.state.navigationOpacity = 1 - Math.min(offset, DetailPageConstant.CODEPREVIEW_IMMERSIVE_HEIGHT) /
        DetailPageConstant.CODEPREVIEW_IMMERSIVE_HEIGHT;
        // 计算顶部平移Y值
        const topTranslateY = offset > CommonConstants.NAVIGATION_HEIGHT ? -CommonConstants.NAVIGATION_HEIGHT : -offset;
        // 计算底部平移Y值
        const bottomTranslateY = offset > (CommonConstants.TAB_BAR_HEIGHT + globalInfoModel.naviIndicatorHeight) ?
          (CommonConstants.TAB_BAR_HEIGHT + globalInfoModel.naviIndicatorHeight) : offset;
        // 设置标签栏高度和导航指示器高度
        CommonConstants.TAB_BAR_HEIGHT + globalInfoModel.naviIndicatorHeight;
        // 设置顶部平移Y值
        this.state.topTranslateY = topTranslateY;
        // 设置底部平移Y值
        this.state.bottomTranslateY = bottomTranslateY;
      } else if (direction === ScrollDirectionEnum.UP) {
        // 设置导航透明度为1
        this.state.navigationOpacity = 1;
        // 设置顶部平移Y为0
        this.state.topTranslateY = 0;
        // 设置底部平移Y为0
        this.state.bottomTranslateY = 0;
      }
    })
  }
}

// 定义代码预览事件枚举
export enum CodePreviewEvent {
  // 初始化代码预览页面事件
  INIT = 'INIT_CODE_PREVIEW_PAGE',
}
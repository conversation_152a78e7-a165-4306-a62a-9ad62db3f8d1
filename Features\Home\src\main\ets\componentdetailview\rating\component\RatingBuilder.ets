// 导入组件详情管理器，用于管理组件状态
import { ComponentDetailManager } from '../../../viewmodel/ComponentDetailManager';
// 导入属性变更事件，用于处理属性变化
import { ChangeAttributeEvent } from '../../../viewmodel/ComponentDetailPageVM';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入评分组件属性修改器，用于修改组件属性
import { RatingAttributeModifier } from '../viewmodel/RatingAttributeModifier';
// 导入评分组件描述器类型，用于描述组件配置
import type { RatingDescriptor } from '../viewmodel/RatingDescriptor';

/**
 * 评分组件构建器函数
 * 用于构建评分组件，支持评分值和指示器模式配置
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function RatingBuilder($$: DescriptorWrapper) {
  // 创建评分组件
  Rating({
    // 设置当前评分值
    rating: ($$.descriptor as RatingDescriptor).rating,
    // 设置是否为指示器模式（只读模式）
    indicator: ($$.descriptor as RatingDescriptor).indicator,
  })
    // 设置属性修改器，用于动态修改组件属性
    .attributeModifier(new RatingAttributeModifier($$.descriptor as RatingDescriptor))
    // 设置评分值变化回调
    .onChange((value: number) => {
      // 发送属性变更事件，更新评分值
      ComponentDetailManager.getInstance().getDetailViewModel('Rating')?.sendEvent(new ChangeAttributeEvent('rating',
        value.toString()));
    })
}
// 导入通用业务模块中的横幅数据类型
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的媒体类型枚举
import { MediaTypeEnum } from '@ohos/commonbusiness';

// 导出发现内容类
export class DiscoverContent {
  // 定义ID属性
  public id: number = 0;
  // 定义类型属性
  public type: ArticleTypeEnum = ArticleTypeEnum.UNKNOWN;
  // 定义媒体类型属性
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  // 定义媒体URL属性
  public mediaUrl: string = '';
  // 定义标题属性
  public title: string = '';
  // 定义副标题属性
  public subTitle: string = '';
  // 定义描述属性
  public desc: string = '';
  // 定义发布时间属性
  public publishTime: string = '';
  // 定义作者属性
  public author: string = '';
  // 定义详情URL属性
  public detailsUrl: string = '';
  // 定义URL数据属性
  public urlData: string = '';
}

// 使用Observed装饰器导出发现卡片数据类
@Observed
export class DiscoverCardData {
  // 定义ID属性
  public id: number = 0;
  // 定义名称属性
  public name: string = '';
  // 定义类型属性
  public type: ArticleTypeEnum = ArticleTypeEnum.UNKNOWN;
  // 定义内容数组属性
  public contents: DiscoverContent[] = [];
}

// 导出发现数据类
export class DiscoverData {
  // 定义横幅信息数组属性
  public bannerInfos?: BannerData[];
  // 定义发现数据数组属性
  public discoveryData: DiscoverCardData[] = [];
}

// 导出文章类型枚举
export enum ArticleTypeEnum {
  // 动态类型
  FEED = 1,
  // 体验类型
  EXPERIENCES = 2,
  // 架构类型
  ARCHITECTURE = 3,
  // 开发者类型
  DEVELOPER = 4,
  // 未知类型
  UNKNOWN = 0,
}
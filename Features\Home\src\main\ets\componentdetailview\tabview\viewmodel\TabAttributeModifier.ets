// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入标签页描述器类型，用于描述标签页组件配置
import type { TabDescriptor } from './TabDescriptor';

/**
 * 标签页属性修改器类
 * 继承自通用属性修改器，用于修改标签页组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class TabAttributeModifier extends CommonAttributeModifier<TabDescriptor, TabsAttribute> {
  /**
   * 应用普通属性到标签页组件实例
   * 将描述器中的属性值应用到实际的标签页组件上
   * @param instance 标签页属性实例，用于设置标签页组件属性
   */
  applyNormalAttribute(instance: TabsAttribute): void {
    // 分配垂直布局属性，将描述器中的vertical值应用到组件实例
    this.assignAttribute((descriptor => descriptor.vertical), (val) => instance.vertical(val));
    // 分配标签栏宽度属性，将描述器中的barWidth值应用到组件实例
    this.assignAttribute((descriptor => descriptor.barWidth), (val) => instance.barWidth(val));
    // 分配标签栏高度属性，将描述器中的barHeight值应用到组件实例
    this.assignAttribute((descriptor => descriptor.barHeight), (val) => instance.barHeight(val));
    // 分配渐变边缘属性，将描述器中的fadingEdge值应用到组件实例
    this.assignAttribute((descriptor => descriptor.fadingEdge), (val) => instance.fadingEdge(val));
    // 分配背景模糊样式属性，将描述器中的backgroundBlurStyle值应用到组件实例
    this.assignAttribute((descriptor => descriptor.backgroundBlurStyle), (val) => instance.backgroundBlurStyle(val));
    // 分配标签栏位置属性，将描述器中的barPosition值应用到组件实例
    this.assignAttribute((descriptor => descriptor.barPosition), (val) => instance.barPosition(val));
  }
}
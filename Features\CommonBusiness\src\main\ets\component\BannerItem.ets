// 导入基础服务包中的设备信息模块
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型和产品系列枚举
import { BreakpointType, ProductSeriesEnum } from '@ohos/common';
// 导入横幅数据模型类型
import type { BannerData } from '../model/BannerData';

/**
 * 横幅项组件
 * 可复用的横幅项组件，用于显示单个横幅内容
 * 包含背景图片、标题、副标题和描述信息
 * 支持响应式布局和组件复用优化
 */
@Reusable
@Component
export struct BannerItem {
  // 全局信息模型，用于获取设备信息和断点状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 横幅数据状态，可选类型
  @State bannerData?: BannerData = undefined;

  /**
   * 组件复用生命周期方法
   * 当组件被复用时调用，用于更新组件数据
   * @param params 包含横幅数据的参数对象
   */
  aboutToReuse(params: Record<string, BannerData>): void {
    // 输出复用日志
    console.log('aboutToReuse');
    // 更新横幅数据
    this.bannerData = params.bannerData;
  }

  /**
   * 构建横幅项组件的UI结构
   * 创建包含背景图片和文本信息的堆叠布局
   */
  build() {
    // 创建底部对齐的堆叠容器
    Stack({ alignContent: Alignment.Bottom }) {
      // 背景图片组件
      Image($rawfile(this.bannerData?.mediaUrl))
        // 设置占位符图片
        .alt($r('app.media.ic_placeholder'))
        // 设置图片适配方式为覆盖
        .objectFit(ImageFit.Cover)
        // 设置图片宽度为100%
        .width('100%')
        // 设置图片高度为100%
        .height('100%')
      // 文本信息列容器
      Column() {
        // 横幅标题文本
        Text(this.bannerData?.bannerTitle)
          // 设置字体颜色为主要字体颜色
          .fontColor($r('sys.color.font_on_primary'))
          // 根据产品系列设置字体大小
          .fontSize(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('sys.float.Subtitle_M') :
          $r('sys.float.Subtitle_L'))
          // 设置字体粗细为粗体
          .fontWeight(FontWeight.Bold)
        // 横幅副标题文本
        Text(this.bannerData?.bannerSubTitle)
          // 设置外边距
          .margin({ top: $r('sys.float.padding_level3'), bottom: $r('sys.float.padding_level2') })
          // 设置字体颜色为主要字体颜色
          .fontColor($r('sys.color.font_on_primary'))
          // 设置字体大小
          .fontSize($r('sys.float.Body_S'))
          // 设置字体粗细为中等
          .fontWeight(FontWeight.Medium)
        // 横幅描述文本
        Text(this.bannerData?.bannerDesc)
          // 设置字体颜色为次要字体颜色
          .fontColor($r('sys.color.font_on_secondary'))
          // 设置最大行数为1行
          .maxLines(1)
          // 设置文本溢出处理为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置字体大小
          .fontSize($r('sys.float.Body_S'))
          // 设置字体粗细为中等
          .fontWeight(FontWeight.Medium)
      }
      // 设置列容器内边距，根据断点类型调整
      .padding({
        // 设置顶部内边距
        top: $r('sys.float.padding_level6'),
        // 根据断点类型设置左内边距
        left: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level8'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        // 根据断点类型设置右内边距
        right: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level8'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        // 根据断点类型设置底部内边距
        bottom: new BreakpointType({
          sm: $r('sys.float.padding_level16'),
          md: $r('sys.float.padding_level16'),
          lg: $r('sys.float.padding_level8'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
      })
      // 设置列容器高度
      .height($r('app.float.banner_info_height'))
      // 设置列容器宽度为100%
      .width('100%')
      // 设置垂直对齐方式为底部对齐
      .justifyContent(FlexAlign.End)
      // 设置水平对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
    }
    // 启用渲染组优化
    .renderGroup(true)
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 根据断点类型设置圆角半径
    .borderRadius(new BreakpointType<Length>({
      sm: 0,
      md: 0,
      lg: $r('sys.float.corner_radius_level8'),
      xl: $r('sys.float.corner_radius_level8'),
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 启用裁剪，使圆角生效
    .clip(true)
  }
}
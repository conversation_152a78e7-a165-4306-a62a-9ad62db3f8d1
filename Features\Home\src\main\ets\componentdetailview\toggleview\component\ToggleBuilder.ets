// 导入详情页常量，包含切换组件的宽高等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入组件详情管理器，用于管理组件状态和事件
import { ComponentDetailManager } from '../../../viewmodel/ComponentDetailManager';
// 导入组件预览变更事件类，用于处理属性变更事件
import { ComPreviewChangeEvent } from '../../../viewmodel/ComponentDetailPageVM';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入切换组件描述器类型，用于描述切换组件配置
import type { ToggleDescriptor } from '../viewmodel/ToggleDescriptor';

/**
 * 切换组件构建器函数
 * 用于构建切换组件，支持开关和按钮两种类型
 * @param $$ 描述器包装对象，包含切换组件配置信息
 */
@Builder
export function ToggleBuilder($$: DescriptorWrapper) {
  // 创建行布局容器
  Row() {
    // 如果是开关类型，显示标签文本
    if (($$.descriptor as ToggleDescriptor).toggleType === ToggleType.Switch) {
      // 创建标签文本
      Text($r('app.string.toggle_type'))
        // 设置字体大小
        .fontSize($r('app.float.default_font_16'))
        // 设置字体颜色为次要颜色
        .fontColor($r('sys.color.font_secondary'))
      // 创建空白填充组件
      Blank()
    }
    // 创建切换组件
    Toggle({
      // 设置切换组件类型
      type: ($$.descriptor as ToggleDescriptor).toggleType,
      // 设置初始开关状态
      isOn: ($$.descriptor as ToggleDescriptor).isOn
    }) {
      // 创建按钮内部文本（仅按钮类型显示）
      Text($r('app.string.toggle_button_text'))
        // 设置文本颜色为主色调上的字体颜色
        .fontColor($r('sys.color.font_on_primary'))
        // 设置字体大小
        .fontSize($r('app.float.default_font_12'))
        // 根据切换类型控制文本可见性
        .visibility(($$.descriptor as ToggleDescriptor).toggleType === ToggleType.Button ? Visibility.Visible :
        Visibility.Hidden)
    }
    // 设置状态变更回调函数
    .onChange((isOn: boolean) => {
      // 通过组件详情管理器发送状态变更事件
      ComponentDetailManager.getInstance().getDetailViewModel('Toggle')?.sendEvent(
        new ComPreviewChangeEvent('isOn', String(isOn))
      )
    })
    // 根据切换类型设置宽度（按钮类型使用固定宽度）
    .width(($$.descriptor as ToggleDescriptor).toggleType === ToggleType.Button ? DetailPageConstant.TOGGLE_WIDTH :
      undefined)
    // 根据切换类型设置高度（按钮类型使用固定高度）
    .height(($$.descriptor as ToggleDescriptor).toggleType === ToggleType.Button ? DetailPageConstant.TOGGLE_HEIGHT :
      undefined)
    // 设置选中状态的背景颜色
    .selectedColor(($$.descriptor as ToggleDescriptor).backgroundColor)
  }
  // 设置行容器宽度为100%
  .width('100%')
  // 设置内容居中对齐
  .justifyContent(FlexAlign.Center)
  // 设置左右内边距
  .padding({ left: $r('sys.float.padding_level16'), right: $r('sys.float.padding_level16') })
}
// 导入语音工具包中的AI字幕相关组件和类型
import { AICaptionComponent, AICaptionController, AICaptionOptions, AudioData } from '@kit.SpeechKit';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的断点类型、全局信息模型和日志记录器
import { BreakpointType, GlobalInfoModel, Logger } from '@ohos/common';
// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[CaptionComponent]';

/**
 * AI字幕构建器函数
 * 用于构建AI字幕组件的UI界面
 * @param _$$ 描述器包装对象，包含AI字幕的配置信息（当前未使用）
 */
@Builder
export function AICaptionBuilder(_$$: DescriptorWrapper) {
  // 创建字幕组件实例
  CaptionComponent()
}

/**
 * 字幕组件结构体
 * 实现AI字幕功能，包含音频播放和字幕显示
 */
@Component
struct CaptionComponent {
  // 全局信息模型，用于获取当前断点信息，支持响应式布局
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 字幕显示状态，控制字幕组件是否显示
  @State isShown: boolean = true;
  // 音频读取状态，标识是否正在读取音频数据
  isReading: boolean = false;
  // 组件宽度，用于布局计算
  componentWidth: number = 0;
  // AI字幕选项配置，可选属性
  private captionOption?: AICaptionOptions;
  // AI字幕控制器，用于控制字幕的显示和音频处理
  private controller: AICaptionController = new AICaptionController();

  /**
   * 组件即将出现时的生命周期方法
   * 初始化AI字幕的配置选项
   */
  aboutToAppear(): void {
    // 配置AI字幕选项
    this.captionOption = {
      // 设置初始透明度
      initialOpacity: DetailPageConstant.OPACITY_SMALL,
      // 设置准备完成回调函数
      onPrepared: () => {
        // 记录准备完成的日志信息
        Logger.info(TAG, 'OnPrepared');
      },
      // 设置错误处理回调函数
      onError: (error: BusinessError) => {
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `AICaption component error. Error code: ${error.code}, message: ${error.message}`);
      },
    };
  }

  /**
   * 异步读取PCM音频文件并写入缓冲区的方法
   * 从应用资源中读取音频数据，分块处理并发送给AI字幕控制器
   */
  async readPcmAudio() {
    // 声明文件数据变量
    let fileData: Uint8Array;
    // 设置正在读取状态为true
    this.isReading = true;
    // 使用try-catch处理可能的异常
    try {
      // 从应用资源中获取16k音频文件的媒体内容
      fileData = await getContext(this).resourceManager.getMediaContent($r('app.media.16k'));
    } catch (err) {
      // 捕获异常并转换为业务错误类型
      const error: BusinessError = err as BusinessError;
      // 记录获取媒体内容失败的错误日志
      Logger.error(TAG, `GetMediaContent error, the code is ${error.code}}, the message is ${error.message}`);
      // 重置读取状态并返回
      this.isReading = false;
      return;
    }
    // 定义缓冲区大小为640字节
    const bufferSize = 640;
    // 获取文件数据的总字节长度
    const byteLength = fileData.byteLength;
    // 初始化偏移量为0
    let offset = 0;
    // 记录PCM数据总字节数的日志信息
    Logger.info(TAG, `Pcm data total bytes: ${byteLength.toString()}`);
    // 记录开始时间，用于计算音频播放时长
    const startTime = new Date().getTime();
    // 循环处理音频数据，直到处理完所有数据
    while (offset < byteLength) {
      // 计算下一个偏移量
      const nextOffset = offset + bufferSize;
      // 检查偏移量是否超出数据长度（这里的判断条件可能有误，应该是nextOffset > byteLength）
      if (offset > byteLength) {
        // 重置读取状态并返回
        this.isReading = false;
        return;
      }
      // 从文件数据中切片获取当前缓冲区的数据
      const arrayBuffer = fileData.buffer.slice(offset, nextOffset);
      // 创建Uint8Array数据
      const data = new Uint8Array(arrayBuffer);
      // 构造音频数据对象
      const audioData: AudioData = {
        data: data,
      };
      // 如果控制器存在，将音频数据写入控制器
      if (this.controller) {
        this.controller.writeAudio(audioData);
      }
      // 更新偏移量
      offset = offset + bufferSize;
      // 计算等待时间，模拟音频播放的时间间隔
      const waitTime = bufferSize / 32;
      // 等待指定时间后继续处理下一块数据
      await this.sleep(waitTime);
    }
    // 记录结束时间
    const endTime = new Date().getTime();
    // 重置读取状态
    this.isReading = false;
    // 记录音频播放总时长的日志信息
    Logger.info(TAG, `Audio play time: ${endTime - startTime}`);
  }

  /**
   * 睡眠方法
   * 返回一个Promise，在指定时间后resolve，用于实现异步等待
   * @param time 等待时间，单位为毫秒
   * @returns Promise<void> 等待指定时间的Promise对象
   */
  sleep(time: number): Promise<void> {
    // 返回一个Promise，使用setTimeout实现延时
    return new Promise(resolve => setTimeout(resolve, time));
  }

  /**
   * 构建UI界面的方法
   * 创建包含AI字幕组件和控制按钮的垂直布局
   */
  build() {
    // 创建垂直列布局，设置子组件间距
    Column({ space: DetailPageConstant.SPACE_LARGE }) {
      // 创建AI字幕组件
      AICaptionComponent({
        // 设置字幕显示状态
        isShown: this.isShown,
        // 设置字幕控制器
        controller: this.controller,
        // 设置字幕配置选项
        options: this.captionOption,
      })
        // 根据断点类型设置组件宽度，实现响应式布局
        .width(new BreakpointType({
          // 超小屏幕宽度
          xs: $r('app.float.ai_caption_width'),
          // 小屏幕宽度
          sm: $r('app.float.ai_caption_width'),
          // 中等屏幕宽度
          md: $r('app.float.ai_caption_width'),
          // 大屏幕宽度
          lg: $r('app.float.ai_caption_width'),
          // 超大屏幕宽度
          xl: $r('app.float.ai_caption_width_xl'),
        }).getValue(this.globalInfoModel.currentBreakpoint))
        // 设置组件高度
        .height($r('app.float.ai_caption_height'))
        // 根据断点类型设置左边距，实现响应式布局
        .margin({
          left: new BreakpointType({
            // 超小屏幕左边距（负值）
            xs: DetailPageConstant.MARGIN_NEGATIVE_LARGER,
            // 小屏幕左边距（负值）
            sm: DetailPageConstant.MARGIN_NEGATIVE_LARGER,
            // 中等屏幕左边距（负值）
            md: DetailPageConstant.MARGIN_NEGATIVE_LARGER,
            // 大屏幕左边距（负值）
            lg: DetailPageConstant.MARGIN_NEGATIVE_LARGER,
            // 超大屏幕左边距为0
            xl: 0,
          }).getValue(this.globalInfoModel.currentBreakpoint)
        })

      // 创建读取PCM音频的按钮
      Button($r('app.string.read_pcm_audio'))
        // 设置按钮背景色为次要背景色
        .backgroundColor($r('sys.color.background_secondary'))
        // 设置按钮高度
        .height($r('app.float.button_height_normal'))
        // 设置按钮字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置按钮字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置按钮字体大小
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮点击事件处理函数
        .onClick(() => {
          // 检查是否正在读取音频，避免重复操作
          if (!this.isReading) {
            // 开始读取PCM音频
            this.readPcmAudio();
          }
        })
    }
    // 设置列布局的高度
    .height($r('app.float.column_size_middle_one'))
    // 设置列布局的上边距
    .margin({ top: $r('sys.float.padding_level10') })
    // 监听区域变化事件，更新组件宽度
    .onAreaChange((_oldValue: Area, newValue: Area) => {
      // 更新组件宽度属性
      this.componentWidth = newValue.width as number;
    })
  }
}
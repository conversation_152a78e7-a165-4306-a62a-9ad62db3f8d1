// 导入通用模块中的全局信息模型和加载模型类型
import type { GlobalInfoModel, LoadingModel } from '@ohos/common';
// 导入通用模块中的加载失败视图、加载状态、加载视图和无网络视图
import { LoadingFailedView, LoadingStatus, LoadingView, NoNetworkView } from '@ohos/common';

/**
 * 基础首页视图组件
 * 通用的首页视图组件，提供统一的加载状态管理和视图切换
 * 支持成功、失败、加载中、无网络等状态的显示
 * 通过BuilderParam接收自定义的内容视图和标题视图
 */
@Component
export struct BaseHomeView {
  // 加载模型属性，必需，包含加载状态和分页信息
  @Prop @Require loadingModel: LoadingModel;
  // 内容视图构建器参数，必需
  @BuilderParam @Require contentView: () => void;
  // 顶部标题视图构建器参数，必需
  @BuilderParam @Require topTitleView: () => void;
  // 全局信息模型，用于获取设备信息和断点状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 重新加载数据的回调函数，可选
  reloadData?: Function;

  /**
   * 构建基础首页视图组件的UI结构
   * 根据加载状态显示不同的视图内容
   */
  build() {
    // 创建左上角对齐的堆叠容器
    Stack({ alignContent: Alignment.TopStart }) {
      // 如果加载成功，显示内容视图
      if (this.loadingModel.loadingStatus === LoadingStatus.SUCCESS) {
        this.contentView()
      } else if (this.loadingModel.loadingStatus === LoadingStatus.FAILED) {
        // 如果加载失败，显示加载失败视图，并提供重新加载功能
        LoadingFailedView(this.globalInfoModel.currentBreakpoint, () => {
          this.reloadData?.();
        })
      } else if (this.loadingModel.loadingStatus === LoadingStatus.LOADING) {
        // 如果正在加载，显示加载视图
        LoadingView(this.globalInfoModel.currentBreakpoint)

      } else if (this.loadingModel.loadingStatus === LoadingStatus.NO_NETWORK) {
        // 如果无网络连接，显示无网络视图，并提供重新加载功能
        NoNetworkView(this.globalInfoModel.currentBreakpoint, () => {
          this.reloadData?.();
        })
      }
      // 显示顶部标题视图，始终在最上层
      this.topTitleView()
    }
    // 设置堆叠容器背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 设置堆叠容器宽度为100%
    .width('100%')
    // 设置堆叠容器高度为100%
    .height('100%')
  }
}
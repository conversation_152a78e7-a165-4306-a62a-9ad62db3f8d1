// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../../viewmodel/CommonAttributeModifier';
// 导入日历选择器描述器类型，用于描述组件配置
import type { CalendarPickerDescriptor } from './CalendarPickerDescriptor';

/**
 * 日历选择器属性修改器类
 * 继承通用属性修改器，专门用于修改日历选择器组件的属性
 * 支持边缘对齐和文本样式的配置
 */
@Observed
export class CalendarPickerAttributeModifier extends CommonAttributeModifier<CalendarPickerDescriptor,
CalendarPickerAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性配置应用到日历选择器实例上
   * @param instance 日历选择器属性实例
   */
  public applyNormalAttribute(instance: CalendarPickerAttribute): void {
    // 分配边缘对齐属性，设置对齐类型和偏移量
    this.assignAttribute((descriptor => descriptor.edgeAlign),
      (val) => instance.edgeAlign(val?.alignType, val?.offset));
    // 分配文本样式属性，设置文本外观
    this.assignAttribute((descriptor => descriptor.textStyle), (val) => instance.textStyle(val));
  }
}

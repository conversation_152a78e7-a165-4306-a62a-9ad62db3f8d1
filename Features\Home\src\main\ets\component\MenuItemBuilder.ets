// 使用Builder装饰器定义菜单项构建器函数
@Builder
export function MenuItemBuilder(configuration: MenuItemConfiguration) {
  // 创建行布局容器
  Row() {
    // 显示配置值文本
    Text(configuration.value)
    // 添加空白填充
    Blank()
    // 如果项目被选中则显示选中标记
    if (configuration.selected) {
      SymbolGlyph($r('sys.symbol.checkmark')).fontSize($r('app.float.symbol_size_normal')).fontColor([$r('sys.color.ohos_id_color_foreground')])
    }
  }
  // 设置高度为菜单项高度
  .height($r('app.float.menu_item_height'))
  // 设置宽度为100%
  .width('100%')
  // 设置左右内边距
  .padding({ left: $r('sys.float.padding_level8'), right: $r('sys.float.padding_level8') })
  // 设置点击事件回调
  .onClick(() => {
    configuration.triggerSelect(configuration.index, configuration.value.valueOf().toString());
  })
}
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入行布局组件相关映射数据
import {
  paddingNumMapData,
  rowAlignMapData,
  rowJustifyContentMapData,
  rowPaddingMapData,
  rowSpaceMapData,
} from '../entity/RowAttributeMapping';

/**
 * 行布局组件代码生成器类
 * 实现通用代码生成器接口，用于生成行布局组件代码
 */
export class RowCodeGenerator implements CommonCodeGenerator {
  // 交叉轴对齐方式，默认使用映射数据中的默认值
  private alignItems: string = rowAlignMapData.get('Default')!.code;
  // 主轴对齐方式，默认使用映射数据中的默认值
  private flexAlign: string = rowJustifyContentMapData.get('Default')!.code;
  // 子组件间距，默认使用映射数据中的默认值
  private space: string = rowSpaceMapData.get('Default')!.code;
  // 内边距类型，默认使用映射数据中的默认值
  private padding: string = rowPaddingMapData.get('Default')!.code;
  // 内边距数值，默认使用映射数据中的默认值
  private paddingNum: string = paddingNumMapData.get('Default')!.code;

  /**
   * 生成行布局组件代码方法
   * 根据属性配置生成完整的行布局组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的行布局组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'alignItems':
          // 设置交叉轴对齐方式
          this.alignItems = rowAlignMapData.get(attribute.currentValue)?.code ?? this.alignItems;
          break;
        case 'flexAlign':
          // 设置主轴对齐方式
          this.flexAlign = rowJustifyContentMapData.get(attribute.currentValue)?.code ?? this.flexAlign;
          break;
        case 'space':
          // 设置子组件间距
          this.space = attribute.currentValue;
          break;
        case 'padding':
          // 设置内边距类型
          this.padding = attribute.currentValue;
          break;
        case 'paddingNum':
          // 设置内边距数值
          this.paddingNum = attribute.currentValue;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });

    // 根据内边距类型生成对应的内边距代码
    let codeStr = '';
    if (this.padding === 'Vertical') {
      // 垂直内边距：设置上下内边距
      codeStr = `.padding({
      top: ${this.paddingNum},
      bottom: ${this.paddingNum}
    })`;
    } else if (this.padding === 'Horizontal') {
      // 水平内边距：设置左右内边距
      codeStr = `.padding({
      left: ${this.paddingNum},
      right: ${this.paddingNum}
    })`;
    } else {
      // 全方向内边距：设置统一内边距
      codeStr = `.padding(${this.paddingNum})`;
    }

    // 返回生成的完整行布局组件代码字符串
    return `// 行布局组件
// 提供水平排列的布局容器，包含三个不同大小的列子组件
@Component
struct RowComponent {
  // 构建组件UI方法
  build() {
    // 创建行布局容器，设置子组件间距
    Row({ space: ${this.space} }) {
      // 第一个列子组件
      Column()
        // 设置第一个列的尺寸
        .size({ width: 40, height: 40 })
        // 设置背景颜色
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level4'))
      // 第二个列子组件
      Column()
        // 设置第二个列的尺寸
        .size({ width: 52, height: 52 })
        // 设置背景颜色
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level4'))
      // 第三个列子组件
      Column()
        // 设置第三个列的尺寸
        .size({ width: 64, height: 64 })
        // 设置背景颜色
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level4'))
    }
    // 根据内边距类型设置行布局宽度
    .width('${this.padding === 'None' ? '80%' : 'auto'}')
    // 根据内边距类型设置行布局高度
    .height('${this.padding === 'None' ? '50%' : 'auto'}')
    // 设置内边距
    ${codeStr}
    // 设置交叉轴对齐方式
    .alignItems(${this.alignItems})
    // 设置主轴对齐方式
    .justifyContent(${this.flexAlign})
    // 设置行布局圆角半径
    .borderRadius($r('sys.float.corner_radius_level6'))
    // 设置行布局边框样式
    .border({ width: 1, color: $r('sys.color.comp_background_emphasize') })
  }
}`;
  }
}
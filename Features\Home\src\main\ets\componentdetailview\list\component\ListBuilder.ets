// 导入通用常量，用于获取间距等常量值
import { CommonConstants } from '@ohos/common';
// 导入详情页面常量，用于获取List组件的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入时间表数据和类型，用于List组件的数据源
import { timeTable, TimeTable } from '../entity/ListAttributeMapping';
// 导入List属性修改器，用于动态修改List组件属性
import { ListAttributeModifier } from '../viewmodel/ListAttributeModifier';
// 导入List描述器类型，用于获取List的属性配置
import type { ListDescriptor } from '../viewmodel/ListDescriptor';
// 导入列表项头部构建器，用于创建列表项头部
import { itemHead } from './ItemHead';

/**
 * List构建器函数
 * 用于构建可配置的List组件，支持分组显示和多种布局模式
 * @param $$ 描述器包装对象，包含List组件的配置信息
 */
@Builder
export function ListBuilder($$: DescriptorWrapper) {
  // 创建列布局容器
  Column() {
    // 创建List组件，设置项目间距
    List({ space: DetailPageConstant.SPACE_NORMAL }) {
      // 遍历时间表数据创建列表项组
      ForEach(timeTable, (item: TimeTable) => {
        // 创建列表项组，包含头部和项目列表
        ListItemGroup({ header: itemHead(item.title, $$), space: CommonConstants.SPACE_8 }) {
          // 遍历项目数据创建列表项
          ForEach(item.projects, (project: string) => {
            // 调用列表项构建器创建具体项目
            listItemBuilder(project, $$);
          }, (item: string) => item)
        }
        // 设置列表项组的尺寸
        .height('100%')
        .width('100%')
        // 设置列表项组的内边距
        .padding({
          left: $r('sys.float.padding_level4'),
          right: $r('sys.float.padding_level4'),
          bottom: $r('sys.float.padding_level2'),
        })
      }, (item: TimeTable, _index: number) => item.title.toString())
    }
    // 根据描述器配置设置粘性头部样式
    .sticky(($$.descriptor as ListDescriptor).sticky ? StickyStyle.Header : StickyStyle.None)
    // 隐藏滚动条
    .scrollBar(BarState.Off)
    // 应用属性修改器，动态设置List属性
    .attributeModifier(new ListAttributeModifier($$.descriptor as ListDescriptor))
    // 设置List组件的尺寸
    .width('100%')
    .height('100%')
  }
  // 设置列容器的对齐方式
  .alignItems(HorizontalAlign.Center)
  .justifyContent(FlexAlign.Center)
  // 设置列容器的尺寸和样式
  .width('100%')
  .height('100%')
  // 启用裁剪
  .clip(true)
  // 设置圆角边框
  .borderRadius($r('sys.float.corner_radius_level8'))
}

/**
 * 列表项构建器函数
 * 用于构建单个列表项，根据列数配置调整项目尺寸和样式
 * @param param 列表项显示的文本内容
 * @param $$ 描述器包装对象，包含List组件的配置信息
 */
@Builder
function listItemBuilder(param: string, $$: DescriptorWrapper) {
  // 创建列表项
  ListItem() {
    // 创建列布局容器
    Column() {
      // 创建文本组件显示项目内容
      Text(param)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 设置字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置文本居中对齐
        .textAlign(TextAlign.Center)
    }
    // 设置列容器的尺寸和样式
    .width('100%')
    .height('100%')
    // 设置圆角边框
    .borderRadius($r('sys.float.corner_radius_level4'))
    // 设置内容垂直居中
    .justifyContent(FlexAlign.Center)
    // 设置边框样式
    .border({ width: $r('app.float.border_width_large'), color: $r('sys.color.comp_background_emphasize') })
  }
  // 设置列表项的尺寸
  .width('100%')
  // 根据列数阈值设置不同的高度
  .height(($$.descriptor as ListDescriptor).lanes.value >= DetailPageConstant.LIST_LANES_THRESHOLD ?
  $r('app.float.component_item_height') : $r('app.float.component_item_height_max'))
  // 根据列数阈值设置不同的宽高比
  .aspectRatio(($$.descriptor as ListDescriptor).lanes.value >= DetailPageConstant.LIST_LANES_THRESHOLD ?
  DetailPageConstant.ASPECT_RATIO_SQUARE : DetailPageConstant.ASPECT_RATIO_INVALID)
}
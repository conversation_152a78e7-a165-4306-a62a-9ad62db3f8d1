// 导入示例数据模型类型
import type { SampleCardData, SampleContent } from '../model/SampleData';
// 导入标签标签组件
import { TagLabel } from './TagLabel';

// 使用Component装饰器定义示例列表卡片组件
@Component
export struct SampleListCard {
  // 使用Prop装饰器定义示例卡片数据属性
  @Prop sampleCardData: SampleCardData;
  // 定义处理项目点击的可选函数
  handleItemClick?: Function;

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 显示卡片标题
      Text(this.sampleCardData.cardTitle)
        .fontSize($r('sys.float.Subtitle_L'))
        .fontColor($r('sys.color.font_primary'))
        .fontWeight(FontWeight.Bold)
      // 显示卡片副标题
      Text(this.sampleCardData.cardSubTitle)
        .fontSize($r('sys.float.Body_S'))
        .fontColor($r('sys.color.font_secondary'))
        .fontWeight(FontWeight.Regular)
        .margin({ top: $r('sys.float.padding_level2') })
      // 遍历示例内容的前两项
      ForEach(this.sampleCardData.sampleContents.slice(0, 2), (item: SampleContent, index: number) => {
        // 创建示例项行布局
        Row() {
          // 创建图片列布局
          Column() {
            // 显示示例项图片
            Image($rawfile(item.mediaUrl))
              .draggable(false)
              .alt($r('app.media.img_placeholder'))
              .objectFit(ImageFit.Contain)
              .height('100%')
          }
          // 设置图片列内边距
          .padding({ top: $r('sys.float.padding_level4'), bottom: $r('sys.float.padding_level2') })
          // 设置图片列宽度
          .width($r('app.float.card_img_width'))
          // 设置图片列背景颜色
          .backgroundColor($r('sys.color.comp_background_secondary'))

          // 创建内容列布局
          Column() {
            // 显示示例项标题
            Text(item.title)
              .fontSize($r('sys.float.Subtitle_M'))
              .fontWeight(FontWeight.Medium)
              .fontColor($r('sys.color.font_primary'))
            // 显示标签标签组件
            TagLabel({ tags: item.tags })
          }
          // 设置内容列高度为100%
          .height('100%')
          // 设置内容列内边距
          .padding($r('sys.float.padding_level8'))
          // 设置内容列左对齐
          .alignItems(HorizontalAlign.Start)
          // 设置内容列居中对齐
          .justifyContent(FlexAlign.Center)
          // 设置内容列权重为1
          .layoutWeight(1)
          // 设置内容列背景颜色
          .backgroundColor($r('sys.color.comp_background_tertiary'))
        }
        // 设置示例项行点击事件
        .onClick(() => this.handleItemClick?.(index, this.sampleCardData.sampleContents))
        // 设置示例项行边框圆角
        .borderRadius($r('sys.float.corner_radius_level5'))
        // 设置示例项行裁剪
        .clip(true)
        // 设置示例项行顶部边距
        .margin({ top: $r('sys.float.padding_level8') })
        // 设置示例项行背景颜色
        .backgroundColor($r('app.color.blur_bg'))
        // 设置示例项行宽度为100%
        .width('100%')
        // 设置示例项行高度
        .height($r('app.float.card_list_height'))
      }, (item: SampleContent) => item.id.toString())
    }
    // 设置主列裁剪
    .clip(true)
    // 设置主列左对齐
    .alignItems(HorizontalAlign.Start)
    // 设置主列背景颜色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置主列点击效果
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置主列边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 设置主列内边距
    .padding($r('sys.float.padding_level8'))
  }
}
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入行组件的内容对齐映射数据，用于列组件的垂直对齐
import { rowJustifyContentMapData } from '../../rowview/entity/RowAttributeMapping';
// 导入列组件相关的映射数据，包含各种属性的映射关系
import {
  columnAlignMapData,
  columnPaddingMapData,
  columnSpaceMapData,
  paddingNumMapData,
} from '../entity/ColumnAttributeMapping';

/**
 * 列代码生成器类
 * 实现通用代码生成器接口，用于生成列组件的代码
 */
export class ColumnCodeGenerator implements CommonCodeGenerator {
  // 对齐项目代码字符串，控制子元素的水平对齐方式
  private alignItems: string = columnAlignMapData.get('Default')!.code;
  // 弹性对齐代码字符串，控制子元素的垂直对齐方式
  private flexAlign: string = rowJustifyContentMapData.get('Default')!.code;
  // 间距代码字符串，控制子元素之间的间距
  private space: string = columnSpaceMapData.get('Default')!.code;
  // 内边距类型代码字符串，控制内边距的应用方式
  private padding: string = columnPaddingMapData.get('Default')!.code;
  // 内边距数值代码字符串，控制内边距的具体数值
  private paddingNum: string = paddingNumMapData.get('Default')!.code;

  /**
   * 生成列组件代码的方法
   * 根据传入的属性数组生成完整的列组件代码字符串
   * @param attributes 原始属性数组，包含组件的各种配置属性
   * @returns 生成的列组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理对齐项目属性
        case 'alignItems':
          // 从映射数据中获取对应的对齐代码，如果不存在则保持原值
          this.alignItems = columnAlignMapData.get(attribute.currentValue)?.code ?? this.alignItems;
          break;
        // 处理弹性对齐属性
        case 'flexAlign':
          // 从行组件映射数据中获取对应的对齐代码，如果不存在则保持原值
          this.flexAlign = rowJustifyContentMapData.get(attribute.currentValue)?.code ?? this.flexAlign;
          break;
        // 处理间距属性
        case 'space':
          // 直接使用属性的当前值
          this.space = attribute.currentValue;
          break;
        // 处理内边距类型属性
        case 'padding':
          // 直接使用属性的当前值
          this.padding = attribute.currentValue;
          break;
        // 处理内边距数值属性
        case 'paddingNum':
          // 直接使用属性的当前值
          this.paddingNum = attribute.currentValue;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });

    // 初始化内边距代码字符串
    let codeOne = '';
    // 根据内边距类型生成对应的内边距代码
    if (this.padding === 'Vertical') {
      // 垂直内边距，只设置上下边距
      codeOne = `.padding({
      top: ${this.paddingNum},
      bottom: ${this.paddingNum}
    })`;
    } else if (this.padding === 'Horizontal') {
      // 水平内边距，只设置左右边距
      codeOne = `.padding({
      left: ${this.paddingNum},
      right: ${this.paddingNum}
     })`;
    } else {
      // 全部内边距或无内边距时使用统一数值
      codeOne = `.padding(${this.paddingNum})`;
    }
    // 返回生成的完整列组件代码字符串
    return `@Component
struct ColumnComponent {
  build() {
    Column({ space: ${this.space} }){
      Column()
        .size({ width: 40, height: 40 })
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .borderRadius($r('sys.float.corner_radius_level4'))
      Column()
        .size({ width: 52, height: 52 })
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .borderRadius($r('sys.float.corner_radius_level4'))
      Column()
        .size({ width: 64, height: 64 })
        .backgroundColor($r('sys.color.comp_background_emphasize'))
        .borderRadius($r('sys.float.corner_radius_level4'))
    }
    .alignItems(${this.alignItems})
    .justifyContent(${this.flexAlign})
    .borderRadius($r('sys.float.corner_radius_level6'))
    .width('${this.padding === 'None' ? '50%' : 'auto'}')
    .height('${this.padding === 'None' ? '90%' : 'auto'}')
    .border({ width: 1, color: $r('sys.color.comp_background_emphasize') })
    ${codeOne}
  }
}`;
  }
}
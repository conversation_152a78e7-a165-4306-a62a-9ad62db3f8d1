// 导入方舟UI工具包中的曲线模块
import { curves } from '@kit.ArkUI';
// 导入方舟Web工具包中的webview模块
import { webview } from '@kit.ArkWeb';
// 导入通用模块中的通用常量、加载状态、日志记录器和Web工具
import { CommonConstants, LoadingStatus, Logger, WebUtil } from '@ohos/common';
// 导入文章Web组件
import { ArticleWebComponent } from '../component/ArticleWebComponent';
// 导入发现内容
import { DiscoverContent } from '../model/DiscoverData';
// 导入文章详情视图模型相关类型
import {
  ArticleDetailViewModel,
  DetailParam,
  ExplorationDetailEventType,
  PopParam,
} from '../viewmodel/ArticleDetailViewModel';
// 导入探索详情状态类型
import type { ExplorationDetailState } from '../viewmodel/ExplorationDetailState';

// 使用Builder装饰器定义横幅详情视图构建器函数
@Builder
export function BannerDetailViewBuilder() {
  // 创建横幅详情视图
  BannerDetailView()
}

// 定义标签常量
const TAG = '[BannerDetailView]';

// 使用Component装饰器定义横幅详情视图组件
@Component
struct BannerDetailView {
  // 定义视图模型
  viewModel: ArticleDetailViewModel = new ArticleDetailViewModel();
  // 定义Web控制器
  webController: webview.WebviewController = new webview.WebviewController();
  // 定义标签视图类型
  tabViewType: number = -1;
  // 定义发现内容
  discoverContent: DiscoverContent = new DiscoverContent();
  // 使用State装饰器定义动画延迟状态
  @State animationDelay: boolean = false;
  // 使用State装饰器定义详情状态
  @State detailState: ExplorationDetailState = this.viewModel.getState();
  // 使用State装饰器定义是否改变页面状态，用于检查是否设置几何过渡ID
  @State isChangePage: boolean = true;
  // 使用State装饰器定义是否弹出过渡状态
  @State isPopTransition: boolean = false;
  // 使用State装饰器定义加载状态
  @State loadingStatus: LoadingStatus = LoadingStatus.IDLE;

  // 定义检查预览的方法
  checkPreview(method: string): Promise<boolean> {
    // 返回Promise对象
    return new Promise((resolve, reject) => {
      // 获取Web控制器
      this.webController = WebUtil.getWebController(this.discoverContent.detailsUrl)!;
      try {
        // 如果方法在白名单中
        if (WebUtil.ARTICLE_WHITE_METHODS.indexOf(method) >= 0) {
          // 运行JavaScript扩展
          this.webController.runJavaScriptExt(
            method,
            (error, result) => {
              // 如果有错误
              if (error) {
                // 拒绝并提示运行JavaScript错误
                reject(`Run javascript error. ${JSON.stringify(error)}`);
              }
              // 如果有结果
              if (result) {
                // 获取类型
                const type = result.getType();
                // 如果类型为布尔值
                if (type === webview.JsMessageType.BOOLEAN) {
                  // 解析布尔值结果
                  resolve(result.getBoolean());
                } else {
                  // 拒绝并提示检查预览错误
                  reject(`CheckPreview error, type:${type}`);
                }
              }
            });
        } else {
          // 记录错误日志
          Logger.error(TAG, `Input method ${method} not in whitelist`);
        }
      } catch (error) {
        // 拒绝并提示运行JavaScript失败错误
        reject(`Run javascript failed error.${JSON.stringify(error)}`);
      }
    });
  }

  // 定义自定义返回按下的方法
  customBackPressed(): boolean {
    // 检查预览
    this.checkPreview('checkPreview()')
      // 处理成功响应
      .then((res) => {
        // 如果有结果
        if (res) {
          // 定义关闭预览方法
          const closePreviewMethod: string = 'closePreview()';
          // 如果关闭预览方法在白名单中
          if (WebUtil.ARTICLE_WHITE_METHODS.indexOf(closePreviewMethod) >= 0) {
            // 运行JavaScript
            this.webController.runJavaScript(closePreviewMethod);
          } else {
            // 记录错误日志
            Logger.error(TAG, `Input method ${closePreviewMethod} not in whitelist`);
          }
        } else {
          // 执行弹出操作
          this.popAction(false);
        }
      })
      // 处理错误响应
      .catch((error: string) => {
        // 记录错误日志
        Logger.error(TAG, `Run javascript error: ${error}`);
        // 执行弹出操作
        this.popAction(false);
      })
    // 返回true
    return true;
  }

  // 定义弹出操作方法
  popAction(isAnimation: boolean) {
    // 设置弹出过渡为true
    this.isPopTransition = true;
    // 设置改变页面为true
    this.isChangePage = true;
    // 执行动画
    animateTo({
      curve: curves.interpolatingSpring(0, 1, 363, 38),
    }, () => {
      // Web控制器变为非活动状态
      WebUtil.getWebController(this.discoverContent.detailsUrl)?.onInactive();
      // 发送弹出事件
      this.viewModel.sendEvent<PopParam>({
        type: ExplorationDetailEventType.POP,
        param: { animation: isAnimation, tabBarView: this.tabViewType },
      });
    });
  }

  // 定义构建方法
  build() {
    // 创建导航目标
    NavDestination() {
      // 创建文章Web组件
      ArticleWebComponent({
        viewModel: this.viewModel,
        detailsUrl: this.detailState.content.detailsUrl,
        tabViewType: this.tabViewType,
        loadingStatus: this.loadingStatus,
      })
        // 设置几何过渡
        .geometryTransition(this.isChangePage ? CommonConstants.BANNER_GEOMETRY + this.tabViewType.toString() : '')
        // 设置过渡效果
        .transition(this.isChangePage ? (this.isPopTransition ? TransitionEffect.OPACITY :
        TransitionEffect.OPACITY.animation({ duration: CommonConstants.TRANSITION_DURATION })) : undefined,
          (transitionIn: boolean) => {
            // 如果过渡进入
            if (transitionIn) {
              // 设置改变页面为false
              this.isChangePage = false;
              // 设置加载状态为加载中
              this.loadingStatus = LoadingStatus.LOADING;
            }
          })
    }
    // 设置默认焦点为true
    .defaultFocus(true)
    // 设置准备就绪事件
    .onReady((cxt: NavDestinationContext) => {
      // 获取参数
      const params = cxt.pathInfo.param as Record<string, string | number | boolean>;
      // 设置发现内容ID
      this.discoverContent.id = params.id as number;
      // 设置发现内容详情URL
      this.discoverContent.detailsUrl = params.detailsUrl as string;
      // 设置发现内容标题
      this.discoverContent.title = params.title as string;
      // 设置标签视图类型
      this.tabViewType = params.tabViewType as number;
      // 发送获取文章详情事件
      this.viewModel.sendEvent<DetailParam>({
        type: ExplorationDetailEventType.GET_ARTICLE_DETAIL,
        param: {
          content: this.discoverContent,
          onBackClick: () => {
            // 调用自定义返回按下方法
            this.customBackPressed?.();
          },
        },
      });
    })
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置返回按下事件
    .onBackPressed((): boolean => {
      // 返回自定义返回按下方法的结果
      return this.customBackPressed();
    })
    // 设置背景颜色为透明
    .backgroundColor(Color.Transparent)
  }
}
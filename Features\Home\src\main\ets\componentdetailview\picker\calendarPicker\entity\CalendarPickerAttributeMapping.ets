// 导入详情页面常量，用于获取配置参数
import { DetailPageConstant } from '../../../../constant/DetailPageConstant';

/**
 * 日历对齐映射类
 * 用于映射日历对齐类型的代码和值
 */
class CalendarAlignMapping {
  // 只读的代码字符串，用于代码生成
  public readonly code: string;
  // 只读的日历对齐值，用于实际应用
  public readonly value: CalendarAlign;

  /**
   * 构造函数
   * @param code 代码字符串
   * @param value 日历对齐值
   */
  constructor(code: string, value: CalendarAlign) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 日历对齐类型映射数据
 * 包含所有支持的日历对齐类型及其对应的映射关系
 */
export const calendarAlignTypeMapData: Map<string, CalendarAlignMapping> = new Map([
  // 结束对齐映射
  ['End', new CalendarAlignMapping('CalendarAlign.END', CalendarAlign.END)],
  // 开始对齐映射
  ['Start', new CalendarAlignMapping('CalendarAlign.START', CalendarAlign.START)],
  // 居中对齐映射
  ['Center', new CalendarAlignMapping('CalendarAlign.CENTER', CalendarAlign.CENTER)],
  // 默认对齐映射（使用结束对齐）
  ['Default', new CalendarAlignMapping('CalendarAlign.END', CalendarAlign.END)],
]);

/**
 * 边缘对齐接口
 * 定义日历组件的边缘对齐配置
 */
export interface EdgeAlign {
  // 对齐类型
  alignType: CalendarAlign;
  // 可选的偏移量
  offset?: Offset;
}

/**
 * 默认边缘对齐配置
 * 使用结束对齐，无偏移量
 */
export const edgeAlignDefault: EdgeAlign = { alignType: CalendarAlign.END, offset: { dx: 0, dy: 0 } };

/**
 * 默认文本样式配置
 * 定义日历选择器的默认文本外观
 */
export const textStyleDefault: PickerTextStyle = {
  // 设置文本颜色为应用默认日历文本颜色
  color: $r('app.color.calender_default_text_color'),
  // 设置字体大小和粗细
  font: { size: DetailPageConstant.CALENDAR_DEFAULT_FONT_SIZE, weight: FontWeight.Normal },
};
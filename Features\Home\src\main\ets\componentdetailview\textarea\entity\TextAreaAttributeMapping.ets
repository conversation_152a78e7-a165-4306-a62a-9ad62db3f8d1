/**
 * 文本溢出类型映射类
 * 用于存储文本溢出处理方式的代码字符串和实际值的映射关系
 */
class TextOverflowTypeMap {
  // 只读的代码字符串属性，用于代码生成
  public readonly code: string;
  // 只读的文本溢出值属性，存储实际的文本溢出处理方式
  public readonly value: TextOverflow;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 文本溢出值，实际的文本溢出处理方式
   */
  constructor(code: string, value: TextOverflow) {
    // 初始化代码字符串
    this.code = code;
    // 初始化文本溢出值
    this.value = value;
  }
}

export const textOverflowTypeMapData: Map<string, TextOverflowTypeMap> = new Map([
  ['None', new TextOverflowTypeMap('TextOverflow.None', TextOverflow.None)],
  ['Clip', new TextOverflowTypeMap('TextOverflow.Clip', TextOverflow.Clip)],
  ['Ellipsis', new TextOverflowTypeMap('TextOverflow.Ellipsis', TextOverflow.Ellipsis)],
  ['Marquee', new TextOverflowTypeMap('TextOverflow.MARQUEE', TextOverflow.MARQUEE)],
  ['Default', new TextOverflowTypeMap('TextOverflow.Clip', TextOverflow.Clip)],
]);

class TextAlignTypeMap {
  public readonly code: string;
  public readonly value: TextAlign;

  constructor(code: string, value: TextAlign) {
    this.code = code;
    this.value = value;
  }
}

export const textAlignTypeMapData: Map<string, TextAlignTypeMap> = new Map([
  ['Start', new TextAlignTypeMap('TextAlign.Start', TextAlign.Start)],
  ['Center', new TextAlignTypeMap('TextAlign.Center', TextAlign.Center)],
  ['End', new TextAlignTypeMap('TextAlign.End', TextAlign.End)],
  ['Justify', new TextAlignTypeMap('TextAlign.JUSTIFY', TextAlign.JUSTIFY)],
  ['Default', new TextAlignTypeMap('TextAlign.Start', TextAlign.Start)],
]);
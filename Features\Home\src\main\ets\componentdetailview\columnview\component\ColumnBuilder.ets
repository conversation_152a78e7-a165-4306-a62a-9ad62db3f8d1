// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入列属性修改器，用于动态修改列组件属性
import { ColumnAttributeModifier } from '../viewmodel/ColumnAttributeModifier';
// 导入列描述器类型，用于获取列组件配置信息
import type { ColumnDescriptor } from '../viewmodel/ColumnDescriptor';

/**
 * 列构建器函数
 * 用于构建列组件的UI界面，展示垂直布局容器的效果
 * @param $$ 描述器包装对象，包含列组件的配置信息
 */
@Builder
export function ColumnBuilder($$: DescriptorWrapper) {
  // 创建主列容器，设置子元素间距
  Column({ space: ($$.descriptor as ColumnDescriptor).space }) {
    // 创建第一个子列容器
    Column()
      // 设置第一个容器的尺寸
      .size({ width: $r('app.float.container_size_1'), height: $r('app.float.container_size_1') })
      // 设置背景颜色为强调色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
    // 创建第二个子列容器
    Column()
      // 设置第二个容器的尺寸
      .size({ width: $r('app.float.container_size_2'), height: $r('app.float.container_size_2') })
      // 设置背景颜色为强调色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
    // 创建第三个子列容器
    Column()
      // 设置第三个容器的尺寸
      .size({ width: $r('app.float.container_size_3'), height: $r('app.float.container_size_3') })
      // 设置背景颜色为强调色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
  }
  // 根据内边距类型动态设置宽度
  .width(($$.descriptor as ColumnDescriptor).padding === 'None' ? '50%' : 'auto')
  // 根据内边距类型动态设置高度
  .height(($$.descriptor as ColumnDescriptor).padding === 'None' ? '90%' : 'auto')
  // 根据内边距类型设置不同的内边距值
  .padding(($$.descriptor as ColumnDescriptor).padding === 'Vertical' ?
    {
      // 垂直内边距，只设置上下边距
      top: ($$.descriptor as ColumnDescriptor).paddingNum,
      bottom: ($$.descriptor as ColumnDescriptor).paddingNum,
    } : ($$.descriptor as ColumnDescriptor).padding === 'Horizontal' ? {
      // 水平内边距，只设置左右边距
      left: ($$.descriptor as ColumnDescriptor).paddingNum,
      right: ($$.descriptor as ColumnDescriptor).paddingNum,
    } : ($$.descriptor as ColumnDescriptor).paddingNum)
  // 设置属性修改器，用于动态修改列组件的样式和属性
  .attributeModifier(new ColumnAttributeModifier($$.descriptor as ColumnDescriptor))
  // 设置外层容器的圆角半径
  .borderRadius($r('sys.float.corner_radius_level6'))
  // 设置边框样式，包含边框宽度和颜色
  .border({ width: DetailPageConstant.CONTAINER_BORDER, color: $r('sys.color.comp_background_emphasize') })
}
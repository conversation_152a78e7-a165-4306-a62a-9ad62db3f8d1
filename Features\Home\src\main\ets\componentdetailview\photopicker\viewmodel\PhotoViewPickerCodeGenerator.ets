// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 照片视图选择器代码生成器类
 * 实现通用代码生成器接口，用于生成照片选择功能相关的代码
 */
export class PhotoViewPickerCodeGenerator implements CommonCodeGenerator {
  /**
   * 生成照片视图选择器代码方法
   * 生成完整的照片选择功能实现代码
   * @param _attributes 原始属性数组（此处未使用）
   * @returns 生成的照片选择器代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 返回生成的完整照片选择器代码字符串
    return `// 导入业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入照片访问助手，用于实现照片选择功能
import { photoAccessHelper } from '@kit.MediaLibraryKit';

// 照片视图选择器组件
// 提供照片选择和显示功能，支持背景模糊效果
@Component
struct PhotoViewPickerComponent {
  // 图片URI状态，存储单个图片的URI
  @State imageUri: string = '';
  // 图片数据数组状态，存储多个图片的URI
  @State imgDatas: string[] = [];
  // 选中的图片状态，用于显示选中的图片
  @State selectedImage: ResourceStr = '';
  // 背景模糊状态，控制按钮的背景模糊效果
  @State backBlur: boolean = false;

  // 构建组件UI方法
  // 创建包含图片显示和选择按钮的界面
  build() {
    // 创建堆叠布局，按钮位于底部
    Stack({ alignContent: Alignment.Bottom }) {
      // 创建图片组件显示选中的照片
      Image(this.selectedImage)
        // 设置图片适应方式为包含
        .objectFit(ImageFit.Contain)
        // 设置圆角边框
        .borderRadius($r('sys.float.corner_radius_level5'))
        // 设置图片尺寸
        .width('100%')
        .height('100%')

      // 创建照片选择按钮
      Button('选择图片')
        // 设置按钮样式为普通模式
        .buttonStyle(ButtonStyleMode.NORMAL)
        // 根据背景模糊状态设置背景模糊样式
        .backgroundBlurStyle(this.backBlur ? BlurStyle.BACKGROUND_THICK : BlurStyle.NONE,
          {
            colorMode: ThemeColorMode.SYSTEM,
            adaptiveColor: AdaptiveColor.DEFAULT,
            scale: 1.0
          })
        // 根据背景模糊状态设置字体颜色
        .fontColor(this.backBlur ? $r('sys.color.font_on_primary') : $r('sys.color.font_emphasize'))
        // 设置按钮高度
        .height(40)
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮底部边距
        .margin({ bottom: 36 })
        // 设置点击事件处理，启动照片选择器
        .onClick(() => {
          try {
            // 创建照片选择选项配置
            const photoSelectOptions = new photoAccessHelper.PhotoSelectOptions();
            // 设置MIME类型为图片类型
            photoSelectOptions.MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE;
            // 设置最大选择数量为1
            photoSelectOptions.maxSelectNumber = 1;
            // 创建照片视图选择器实例
            const photoPicker = new photoAccessHelper.PhotoViewPicker();
            // 调用选择方法并处理结果
            photoPicker.select(photoSelectOptions).then((photoSelectResult: photoAccessHelper.PhotoSelectResult) => {
              // 获取选择的照片URI数组
              this.imgDatas = photoSelectResult.photoUris;
              // 设置单个图片URI
              this.imageUri = this.imgDatas[0];
              // 设置选中的图片用于显示
              this.selectedImage = this.imgDatas[0];
              // 如果选择了图片，启用背景模糊效果
              if (this.selectedImage.length > 0) {
                this.backBlur = true;
              }
              // 记录成功日志
              console.info(\`PhotoViewPicker.select successfully, PhotoSelectResult uri: \${photoSelectResult.photoUris[0]}\`);

            }).catch((err: BusinessError) => {
              // 捕获并记录选择失败错误
              console.info(\`PhotoViewPicker.select failed with err: \${err.code}, \${err.message}\`);
            });
          } catch (error) {
             // 捕获并记录其他错误
             const err: BusinessError = error as BusinessError;
             console.error(\`PhotoViewPicker failed with err: \${err.code}, \${err.message}\`);
          }
        })
    }
    // 设置堆叠容器宽度
    .width('100%')
  }
}`;
  }
}
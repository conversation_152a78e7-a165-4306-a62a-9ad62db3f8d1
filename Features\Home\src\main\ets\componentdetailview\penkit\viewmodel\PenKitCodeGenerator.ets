// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * PenKit代码生成器类
 * 实现通用代码生成器接口，用于生成手写功能相关的代码示例
 */
export class PenKitCodeGenerator implements CommonCodeGenerator {
  /**
   * 生成PenKit手写功能代码方法
   * 生成完整的手写功能实现代码，包括上下文配置和组件实现
   * @param _attributes 原始属性数组（此处未使用）
   * @returns 生成的手写功能代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 返回生成的完整手写功能代码字符串
    return `// 在 src/main/ets/util 路径下创建 ContextConfig.ts 文件
// 用于配置全局UI能力上下文，为手写功能提供必要的上下文环境
import { common } from '@kit.AbilityKit';

// 声明全局命名空间，用于存储画笔引擎上下文
declare namespace globalThis {
  let _brushEngineContext: common.UIAbilityContext;
};

// 全局UI能力上下文管理类
// 提供静态方法来获取和设置应用上下文
export default class GlobalUIAbilityContext {
  // 获取全局上下文方法
  static getContext(): common.UIAbilityContext {
    return globalThis._brushEngineContext;
  }

  // 设置全局上下文方法
  static setContext(context: common.UIAbilityContext): void {
    globalThis._brushEngineContext = context;
  }
}

// 在 src/main/ets/entryability/EntryAbility.ets 文件中导入 GlobalUIAbilityContext
// 在应用入口能力中设置全局上下文
import GlobalUIAbilityContext from '../util/ContextConfig';

// 应用入口能力类
export default class EntryAbility extends UIAbility {
  // 窗口舞台创建时的生命周期方法
  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 设置全局上下文，为手写功能提供必要的环境
    GlobalUIAbilityContext.setContext(this.context);
    // 其他代码
    // ...
  }
}

// 在 src/main/ets/ 路径下创建 HandWritingComponent.ets 文件作为组件使用
// 导入手写组件和控制器，用于实现手写功能
import { HandwriteComponent, HandwriteController } from '@kit.Penkit';

// 手写组件结构体
// 提供完整的手写功能界面
@Component
struct HandWritingComponent {
  // 手写控制器，用于控制手写功能
  private controller: HandwriteController = new HandwriteController();
  // 初始化路径，用于保存和加载手写内容
  private initPath: string = 'savePath';

  // 组件即将出现时的生命周期方法
  // 检查设备是否支持手写功能
  aboutToAppear() {
    if (canIUse('SystemCapability.Stylus.Handwrite')) {
      console.log('This device supports SystemCapability.Stylus.Handwrite');
    } else {
      console.log('This device does not support SystemCapability.Stylus.Handwrite');
    }
  }

  // 组件即将消失时的生命周期方法
  // 在手写组件退出时保存手写内容
  aboutToDisappear() {
    try {
      // 保存手写内容到指定路径
      this.controller?.save(this.initPath);
    } catch (err) {
      // 捕获并记录保存错误
      const error: BusinessError = err as BusinessError;
      console.error(\`HandwriteController save error, the code is \${error.code}, the message is \${error.message}\`);
    }
  }

  // 构建组件UI方法
  // 创建包含手写区域的完整界面
  build() {
    Row() {
      Column() {
        // 创建手写组件
        HandwriteComponent({
          // 传入手写控制器
          handwriteController: this.controller,
          // 设置初始化回调，加载已保存的手写内容
          onInit: () => {
            try {
              // 从指定路径加载手写内容
              this.controller?.load(this.initPath);
            } catch (err) {
              // 捕获并记录加载错误
              const error: BusinessError = err as BusinessError;
              console.error(\`HandwriteController load error, the code is \${error.code}, the message is \${error.message}\`);
            }
          }
        })
      }
      .width('100%')
    }
    .height('100%')
  }
}`;
  }
}
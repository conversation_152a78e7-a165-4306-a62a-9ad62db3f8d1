// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入高亮颜色映射数据
import { highlightColorMap } from '../../common/entity/CommonMapData';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入文本溢出类型映射数据
import { textOverflowTypeMapData } from '../../textarea/entity/TextAreaAttributeMapping';

/**
 * 样式文本组件描述器类
 * 继承通用描述器，专门用于描述样式文本组件的配置
 * 包含文本缩进、最大行数、溢出处理、高亮颜色等配置信息
 */
@Observed
export class StyleTextDescriptor extends CommonDescriptor {
  // 文本缩进值，默认为0
  public textIndent: number = 0;
  // 最大行数，默认为2行
  public maxLines: number = 2;
  // 溢出处理方式，默认使用映射数据中的默认值
  public overflow: TextOverflow = textOverflowTypeMapData.get('Default')!.value;
  // 高亮颜色，默认使用映射数据中的默认值
  public highlightColor: string = highlightColorMap.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为样式文本组件的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'textIndent':
          // 设置文本缩进值
          this.textIndent = Number(attribute.currentValue);
          break;
        case 'maxLines':
          // 设置最大行数
          this.maxLines = Number(attribute.currentValue);
          break;
        case 'overflow':
          // 设置溢出处理方式
          this.overflow =
            textOverflowTypeMapData.get(attribute.currentValue)?.value ?? textOverflowTypeMapData.get('Default')!.value;
          break;
        case 'highlightColor':
          // 设置高亮颜色
          this.highlightColor = attribute.currentValue;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
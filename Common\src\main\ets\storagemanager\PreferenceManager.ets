// 导入ArkData包中的preferences模块
import { preferences } from '@kit.ArkData';
// 导入基础服务包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from '../util/Logger';

// 首选项存储名称常量
const PREFERENCES_NAME: string = 'HMosWorldStore';
// 日志标签常量
const TAG: string = '[PreferenceManager]';

/**
 * 首选项管理器类
 * 提供应用数据持久化存储功能
 * 使用单例模式确保全局唯一实例
 * 支持键值对数据的增删改查操作
 */
export class PreferenceManager {
  // 首选项实例，可选类型
  private preferences?: preferences.Preferences;
  // 静态单例实例
  private static instance: PreferenceManager;

  /**
   * 私有构造函数
   * 防止外部直接实例化，确保单例模式
   * 初始化首选项存储
   */
  private constructor() {
    // 初始化首选项存储
    this.initPreference(PREFERENCES_NAME);
  }

  /**
   * 获取单例实例方法
   * 如果实例不存在则创建新实例
   * @returns PreferenceManager 首选项管理器实例
   */
  public static getInstance(): PreferenceManager {
    // 如果实例不存在
    if (!PreferenceManager.instance) {
      // 创建新的实例
      PreferenceManager.instance = new PreferenceManager();
    }
    // 返回单例实例
    return PreferenceManager.instance;
  }

  /**
   * 初始化首选项存储方法
   * 获取指定名称的首选项存储实例
   * @param storeName 存储名称
   * @returns Promise<void> 初始化完成的Promise
   */
  private initPreference(storeName: string): Promise<void> {
    // 获取首选项存储实例
    return preferences.getPreferences(getContext(), storeName)
      .then((preferences: preferences.Preferences) => {
        // 保存首选项实例
        this.preferences = preferences;
      })
      .catch((err: BusinessError) => {
        // 记录获取首选项失败的错误日志
        Logger.error(TAG, `getPreferences failed, err code:${err.code},msg:${err.message}`);
      });
  }

  /**
   * 设置键值对数据方法
   * 将数据序列化为JSON字符串后存储
   * @param key 存储键名
   * @param value 要存储的值，支持泛型类型
   * @returns Promise<void> 设置完成的Promise
   */
  public async setValue<T>(key: string, value: T): Promise<void> {
    // 如果首选项实例已存在
    if (this.preferences) {
      // 将值序列化为JSON字符串并存储
      this.preferences.put(key, JSON.stringify(value)).then(() => {
        // 保存数据到持久化存储
        this.saveValue();
      })
    } else {
      // 如果首选项实例不存在，先初始化再设置值
      this.initPreference(PREFERENCES_NAME).then(() => {
        // 递归调用设置值方法
        this.setValue<T>(key, value);
      });
    }
  }

  /**
   * 获取键值对数据方法
   * 从存储中获取数据并反序列化为指定类型
   * @param key 存储键名
   * @returns Promise<T | null> 获取到的值或null
   */
  public async getValue<T>(key: string): Promise<T | null> {
    // 如果首选项实例已存在
    if (this.preferences) {
      // 获取存储的值
      return this.preferences.get(key, '').then((res: preferences.ValueType) => {
        // 初始化返回值为null
        let value: T | null = null;
        // 如果获取到了值
        if (res) {
          // 将JSON字符串反序列化为指定类型
          value = JSON.parse(res as string) as T;
        }
        // 返回解析后的值
        return value;
      });
    } else {
      // 如果首选项实例不存在，先初始化再获取值
      return this.initPreference(PREFERENCES_NAME).then(() => {
        // 递归调用获取值方法
        return this.getValue<T>(key);
      });
    }
  }

  /**
   * 检查键是否存在方法
   * 判断指定键名的数据是否存在于存储中
   * @param key 要检查的键名
   * @returns Promise<boolean> 是否存在的布尔值Promise
   */
  public async hasValue(key: string): Promise<boolean> {
    // 如果首选项实例已存在
    if (this.preferences) {
      // 检查键是否存在
      return this.preferences.has(key);
    } else {
      // 如果首选项实例不存在，先初始化再检查
      return this.initPreference(PREFERENCES_NAME).then(() => {
        // 递归调用检查方法
        return this.hasValue(key);
      });
    }
  }

  /**
   * 删除键值对数据方法
   * 从存储中删除指定键名的数据
   * @param key 要删除的键名
   * @returns Promise<void> 删除完成的Promise
   */
  public async deleteValue(key: string): Promise<void> {
    // 如果首选项实例已存在
    if (this.preferences) {
      // 删除指定键的数据
      this.preferences.delete(key).then(() => {
        // 保存更改到持久化存储
        this.saveValue();
      });
    } else {
      // 如果首选项实例不存在，先初始化再删除
      this.initPreference(PREFERENCES_NAME).then(() => {
        // 递归调用删除方法
        this.deleteValue(key);
      });
    }
  }

  /**
   * 保存数据到持久化存储方法
   * 将内存中的更改刷新到磁盘存储
   */
  saveValue() {
    // 调用flush方法将数据持久化到磁盘
    this.preferences?.flush();
  }
}
// 导入滑块组件属性类型定义
import type { SliderComAttribute } from '../viewmodel/ComponentDetailState';

// 使用Component装饰器定义滑块公共组件
@Component
export struct SliderComponent {
  // 使用ObjectLink装饰器链接属性对象
  @ObjectLink attribute: SliderComAttribute;
  // 定义回调函数
  callback: (name: string, value: string, mode?: SliderChangeMode) => void = (name: string, value: string) => {
  };

  // 定义构建方法
  build() {
    // 创建列布局
    Column() {
      // 创建标题行
      Row() {
        // 显示属性显示名称文本
        Text(this.attribute.disPlayName)
          .fontWeight(FontWeight.Medium)
          .fontSize($r('sys.float.Subtitle_M'))
          .fontColor($r('sys.color.font_primary'))
        // 添加空白填充
        Blank()
        // 显示当前值文本
        Text(this.attribute.currentValue)
          .fontWeight(FontWeight.Regular)
          .fontSize($r('sys.float.Body_M'))
          .fontColor($r('sys.color.font_secondary'))
          .padding({ right: $r('sys.float.padding_level2') })
      }
      // 设置行高度
      .height($r('app.float.common_component_height'))
      // 设置垂直对齐方式为居中
      .alignItems(VerticalAlign.Center)
      // 设置底部外边距
      .margin({ bottom: $r('sys.float.padding_level1') })
      // 设置宽度为100%
      .width('100%')
      // 设置左右内边距
      .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })

      // 创建滑块行
      Row() {
        // 创建滑块组件
        Slider({
          // 设置当前值
          value: Number(this.attribute.currentValue),
          // 设置最小值
          min: this.attribute.leftRange,
          // 设置最大值
          max: this.attribute.rightRange,
          // 设置样式为外置
          style: SliderStyle.OutSet,
          // 设置步长
          step: this.attribute.step,
        })
          // 设置值变化回调
          .onChange((value: number, mode: SliderChangeMode) => {
            // 如果当前值不等于新值则更新
            if (this.attribute.currentValue !== String(value)) {
              // 更新属性当前值
              this.attribute.currentValue = String(value);
              // 调用回调函数
              this.callback(this.attribute.name, String(value), mode);
            }
          })
          // 设置滑块交互模式为滑动和点击
          .sliderInteractionMode(SliderInteraction.SLIDE_AND_CLICK_UP)
          // 设置滑块大小
          .blockSize({
            width: $r('app.float.common_component_block_size'),
            height: $r('app.float.common_component_block_size'),
          })
          // 设置选中颜色
          .selectedColor($r('sys.color.icon_emphasize'))
          // 设置轨道厚度
          .trackThickness($r('app.float.common_component_track_thickness'))
          // 设置左右内边距
          .padding({ left: $r('sys.float.padding_level4'), right: $r('sys.float.padding_level4') })
          // 设置宽度为100%
          .width('100%')
      }
      // 设置行高度
      .height($r('app.float.common_component_height'))
      // 设置宽度为100%
      .width('100%')
    }
    // 设置垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
    // 设置水平对齐方式为开始
    .alignItems(HorizontalAlign.Start)
    // 设置宽度为100%
    .width('100%')
  }
}
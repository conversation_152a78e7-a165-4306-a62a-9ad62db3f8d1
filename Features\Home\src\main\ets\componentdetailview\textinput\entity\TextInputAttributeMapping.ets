/**
 * 文本输入类型映射类
 * 用于存储文本输入类型的代码字符串和实际值的映射关系
 */
class TextInputTypeMap {
  // 只读的代码字符串属性，用于代码生成
  public readonly code: string;
  // 只读的输入类型值属性，存储实际的输入类型
  public readonly value: InputType;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 输入类型值，实际的输入类型
   */
  constructor(code: string, value: InputType) {
    // 初始化代码字符串
    this.code = code;
    // 初始化输入类型值
    this.value = value;
  }
}

export const textInputTypeMapData: Map<string, TextInputTypeMap> = new Map([
  ['Normal', new TextInputTypeMap('InputType.Normal', InputType.Normal)],
  ['Number', new TextInputTypeMap('InputType.Number', InputType.Number)],
  ['NewPassword', new TextInputTypeMap('InputType.NEW_PASSWORD', InputType.NEW_PASSWORD)],
  ['Default', new TextInputTypeMap('InputType.Default', InputType.Normal)],
]);

class TextInputFontMap {
  public readonly code: string;
  public readonly value: Font;

  constructor(code: string, value: Font) {
    this.code = code;
    this.value = value;
  }
}

export const textInputFontMapData: Map<string, TextInputFontMap> = new Map([
  ['较细字体', new TextInputFontMap('{ size: $r(\'sys.float.Body_L\'), weight: FontWeight.Lighter}',
    { size: $r('sys.float.Body_L'), weight: FontWeight.Lighter })],
  ['正常字体', new TextInputFontMap('{ size: $r(\'sys.float.Body_L\'), weight: FontWeight.Regular }',
    { size: $r('sys.float.Body_L'), weight: FontWeight.Regular })],
  ['较粗字体', new TextInputFontMap('{ size: $r(\'sys.float.Body_L\'), weight: FontWeight.Bold }',
    { size: $r('sys.float.Body_L'), weight: FontWeight.Bold })],
  ['Default', new TextInputFontMap('{ size: $r(\'sys.float.Body_L\'), weight: FontWeight.Regular }',
    { size: $r('sys.float.Body_L'), weight: FontWeight.Regular })],
]);
// 导出详情页面常量类
export class DetailPageConstant {
  // 网格弹窗Y轴偏移量，向上偏移10像素
  public static GRID_POPUP_OFFSET_Y: number = -10;
  // 图片弹窗Y轴偏移量，向上偏移50像素
  public static IMAGE_POPUP_OFFSET_Y: number = -50;
  // 长时间持续时长，2000毫秒
  public static LONG_DURATION: number = 2000;
  // 动画持续时长，500毫秒
  public static ANIMATION_DURATION: number = 500;
  // 短动画持续时长，300毫秒
  public static ANIMATION_DURATION_SHORT: number = 300;
  // 自定义对话框内容高度，100像素
  public static CUSTOM_DIALOG_CONTENT_HEIGHT: number = 100;
  // 详情选择组件高度，48像素
  public static DETAIL_SELECT_COMPONENT_HEIGHT: number = 48;
  // 瀑布流最小尺寸，92像素
  public static WATER_FLOW_MIN_SIZE: number = 92;
  // 瀑布流最大尺寸，180像素
  public static WATER_FLOW_MAX_SIZE: number = 180;
  // 轮播图背景颜色，蓝色半透明
  public static SWIPER_BACKGROUND_COLOR: string = 'rgba(0, 85, 255, 0.1)';
  // 图片透明度，0.4透明度
  public static IMAGE_OPACITY: number = 0.4;
  // 图片背景透明度，0.2透明度
  public static IMAGE_OPACITY_BG: number = 0.2;
  // 进度条最大值，100
  public static PROGRESS_MAX_VALUE: number = 100;
  // 圆形进度条宽度，64像素
  public static PROGRESS_CIRCLE_WIDTH: number = 64;
  // 线性进度条高度，48像素
  public static PROGRESS_LINE_HEIGHT: number = 48;
  // 胶囊进度条高度，24像素
  public static PROGRESS_CAPSULE_HEIGHT: number = 24;
  // 缩放级别1，1.0倍缩放
  public static SCALE_LEVEL1: number = 1.0;
  // 正方形宽高比，1:1比例
  public static ASPECT_RATIO_SQUARE: number = 1;
  // 代码预览沉浸式高度，16像素
  public static CODEPREVIEW_IMMERSIVE_HEIGHT = 16;
  // 宽高比无效值，-1表示无效
  public static ASPECT_RATIO_INVALID: number = -1;
  // 列数阈值，4列
  public static LIST_LANES_THRESHOLD: number = 4;
  // 警告对话框Y轴偏移量，向上偏移20像素
  public static ALERT_DIALOG_OFFSET_Y: number = -20;
  // 操作表单Y轴偏移量，向上偏移10像素
  public static ACTION_SHEET_OFFSET_Y: number = -10;
  // 选择结果对话框尺寸，300像素
  public static SELECT_RESULT_DIALOG_SIZE: number = 300;
  // 负边距较大值，向上偏移95像素
  public static MARGIN_NEGATIVE_LARGER: number = -95;
  // 普通间距，8像素
  public static SPACE_NORMAL: number = 8;
  // 小间距，5像素
  public static SPACE_SMALL: number = 5;
  // 大间距，20像素
  public static SPACE_LARGE: number = 20;
  // 小透明度级别，0.2透明度
  public static OPACITY_SMALL: number = 0.2;
  // 开关宽度，120像素
  public static TOGGLE_WIDTH: number = 120;
  // 开关高度，28像素
  public static TOGGLE_HEIGHT: number = 28;
  // 容器边框宽度，1像素
  public static CONTAINER_BORDER: number = 1;
  // 弹性布局间距，6像素
  public static FLEX_SPACE: number = 6;
  // 小屏预览高度，284像素
  public static PREVIEW_HEIGHT_SM: number = 284;
  // 小屏预览子高度，230像素
  public static PREVIEW_SUB_HEIGHT_SM: number = 230;
  // 属性项高度，56像素
  public static ATTRIBUTE_ITEM_HEIGHT: number = 56;
  // 文本提示高度，50像素
  public static TEXT_TIP_HEIGHT: number = 50;
  // 大字体行高，16像素
  public static LINE_HEIGHT_LARGE: number = 16;
  // 滚动Y轴偏移量，20像素
  public static SCROLL_OFFSET_Y: number = 20;
  // 菜单项高度，46像素
  public static MENU_ITEM_HEIGHT: number = 46;
  // 文本最大行数，3行
  public static TEXT_MAX_LINES: number = 3;
  // 日历选择器提示圆角半径，10像素
  public static DATE_HINT_RADIUS: number = 10;
  // 键盘出现时组件位移的动画持续时长，300毫秒
  public static UP_DURATION: number = 300;
  // 日历默认字体大小，20像素
  public static CALENDAR_DEFAULT_FONT_SIZE: number = 20;
  // 日历默认字体大小2，16像素
  public static CALENDAR_DEFAULT_FONT_SIZE2: number = 16;
  // 组件间隙尺寸，20像素
  public static COMPONENT_GAP_SIZE: number = 20;
  // 组件间隙尺寸2，40像素
  public static COMPONENT_GAP_SIZE2: number = 40;
  // 悬停弹窗左偏移量，10像素
  public static HOVER_POPUP_LEFT: number = 10;
  // 悬停弹窗顶部偏移量，8像素
  public static HOVER_POPUP_TOP: number = 8;
  // 笔关闭顶部偏移量，56像素
  public static PEN_CLOSE_TOP: number = 56;
}
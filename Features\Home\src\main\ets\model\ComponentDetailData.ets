// 定义组件结果接口
export interface ComponentResult {
  // 响应代码
  code: number;
  // 响应消息
  message: string;
  // 组件详情数据
  data: ComponentDetailData;
}

// 定义组件详情数据接口
export interface ComponentDetailData {
  // 组件ID
  id: number;
  // 组件名称
  componentName: string;
  // 组件类型
  type: number;
  // 是否收藏
  isFavorite: boolean;
  // 属性数据数组
  props: AttributeData[];
  // 推荐列表，可选属性
  recommendList?: RecommendData[];
}

// 定义属性数据接口
export interface AttributeData {
  // 属性名称
  propertyName: string;
  // 属性描述
  propertyDesc: string;
  // 显示类型
  displayType: string;
  // 默认属性值
  defaultProperty: string;
  // 属性值
  propertyValues: string;
}

// 定义推荐数据接口
export interface RecommendData {
  // ID，可选属性
  id?: number;
  // 标题
  title: ResourceStr;
  // 副标题，可选属性
  subTitle?: string;
  // 文章类型
  articleType: number;
  // URL地址
  url: string;
}
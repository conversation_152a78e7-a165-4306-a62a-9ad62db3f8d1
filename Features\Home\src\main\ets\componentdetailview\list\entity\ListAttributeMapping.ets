/**
 * List方向类型类
 * 用于存储List组件方向的代码字符串和实际值的映射关系
 */
class ListDirectionType {
  // List方向的代码字符串表示
  public readonly code: string;
  // List方向的实际枚举值
  public readonly value: Axis;

  /**
   * 构造函数
   * @param code List方向的代码字符串
   * @param value List方向的枚举值
   */
  constructor(code: string, value: Axis) {
    this.code = code;
    this.value = value;
  }
}

// List方向映射数据，用于配置List的滚动方向
export const listDirectionMapData: Map<string, ListDirectionType> = new Map([
  ['Vertical', new ListDirectionType('Axis.Vertical', Axis.Vertical)],
  ['Horizontal', new ListDirectionType('Axis.Horizontal', Axis.Horizontal)],
  ['Default', new ListDirectionType('Axis.Vertical', Axis.Vertical)],
]);

/**
 * 边缘效果映射类
 * 用于存储List组件边缘效果的代码字符串和实际值的映射关系
 */
class EdgeEffectMap {
  // 边缘效果的代码字符串表示
  public readonly code: string;
  // 边缘效果的实际枚举值
  public readonly value: EdgeEffect;

  /**
   * 构造函数
   * @param code 边缘效果的代码字符串
   * @param value 边缘效果的枚举值
   */
  constructor(code: string, value: EdgeEffect) {
    this.code = code;
    this.value = value;
  }
}

// 边缘效果映射数据，用于配置List的滚动边缘效果
export const edgeEffectMapData: Map<string, EdgeEffectMap> = new Map([
  ['Spring', new EdgeEffectMap('EdgeEffect.Spring', EdgeEffect.Spring)],
  ['Fade', new EdgeEffectMap('EdgeEffect.Fade', EdgeEffect.Fade)],
  ['None', new EdgeEffectMap('EdgeEffect.None', EdgeEffect.None)],
  ['Default', new EdgeEffectMap('EdgeEffect.Spring', EdgeEffect.Spring)],
]);

/**
 * 时间表接口
 * 定义List组件中分组数据的结构
 */
export interface TimeTable {
  // 分组标题
  title: string;
  // 分组下的项目列表
  projects: string[];
}

/**
 * 列样式接口
 * 定义List组件多列布局的样式配置
 */
export interface LanesStyle {
  // 列数值
  value: number;
  // 列间距
  gutter: Dimension;
}

// 时间表数据，用于List组件的分组显示
export const timeTable: TimeTable[] = [
  {
    // 第一组数据
    title: 'ONE',
    projects: ['item 1', 'item 2', 'item 3', 'item 4'],
  },
  {
    // 第二组数据
    title: 'TWO',
    projects: ['item 5', 'item 6', 'item 7', 'item 8'],
  },
  {
    // 第三组数据
    title: 'THREE',
    projects: ['item 9', 'item 10', 'item 11', 'item 12'],
  },
  {
    // 第四组数据
    title: 'FOUR',
    projects: ['item 13', 'item 14', 'item 15', 'item 16'],
  },
];

// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../../viewmodel/DescriptorWrapper';
// 导入日期选择器属性修改器，用于修改组件属性
import { DatePickerAttributeModifier } from '../viewmodel/DatePickerAttributeModifier';
// 导入日期选择器描述器类型，用于描述组件配置
import type { DatePickerDescriptor } from '../viewmodel/DatePickerDescriptor';

/**
 * 日期选择器构建器函数
 * 用于构建日期选择器组件，提供日期选择功能
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function DatePickerBuilder($$: DescriptorWrapper) {
  // 创建列布局容器
  Column() {
    // 创建日期选择器组件
    DatePicker({
      // 设置开始日期为1970年1月1日
      start: new Date('1970-1-1'),
      // 设置结束日期为2100年1月1日
      end: new Date('2100-1-1'),
      // 设置默认选中日期为2021年8月8日
      selected: new Date('2021-08-08'),
    })
      // 设置组件边距
      .margin({
        left: $r('sys.float.padding_level12'),
        right: $r('sys.float.padding_level12'),
        top: $r('sys.float.padding_level8'),
        bottom: $r('sys.float.padding_level8'),
      })
      // 设置选中文本样式
      .selectedTextStyle(($$.descriptor as DatePickerDescriptor).selectedTextStyle)
      // 设置普通文本样式
      .textStyle(($$.descriptor as DatePickerDescriptor).textStyle)
      // 设置属性修改器，用于动态修改组件属性
      .attributeModifier(new DatePickerAttributeModifier($$.descriptor as DatePickerDescriptor))
  }
  // 设置列布局宽度为100%
  .width('100%')
  // 设置列布局高度为100%
  .height('100%')
  // 设置列布局垂直对齐方式为居中
  .justifyContent(FlexAlign.Center)
}
{"continue_1": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/app-continuation-guide#section17575828642", "continue_2": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-continue", "continue_3": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-content-creation#section1973014141516", "continue_4": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-application-continue-progess#section16702516134216", "continue_5": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-application-continue-progess#section12439210434", "continue_6": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-application-continue-progess#section3512987460", "continue_7": "https://developer.huawei.com/consumer/cn/training/course/slightMooc/C101705112214146396", "continue_8": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/app-continuation-overview", "continue_9": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/app-continuation-guide", "continue_10": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_NEXT-DistributedMail", "layer_1": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/store-moduleinstall_arkts", "layer_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/foreword", "liveview_1": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/liveview-introduction#section4266105713209", "liveview_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/liveview-design-formula#section19663288592", "liveview_3": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/liveview-design-formula#section168541692013", "liveview_4": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/liveview-design-formula#section887024865911", "liveview_5": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/live-view-kit-guide", "multidevice_1": "https://developer.huawei.com/consumer/cn/doc/design-guides/design-concepts-0000001795698445", "multidevice_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/introduction#工程结构", "multidevice_3": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/page-development", "multidevice_4": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/adaptive-layout", "multidevice_5": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/responsive-layout", "multidevice_6": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-layered-architecture-design", "multidevice_7": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-multi-device-bp-practice", "multidevice_8": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/ts-container-gridrow", "multidevice_9": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/responsive-layout#媒体查询", "safety_1": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/security-component-overview", "safety_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/use-picker", "safety_3": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/security-component-overview#运作机制", "safety_4": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/pastebutton", "safety_5": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/savebutton", "safety_6": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/save-user-file", "safety_7": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/system-app-startup", "safety_8": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/js-apis-photoaccesshelper", "safety_9": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/component-guidelines-photoviewpicker", "safety_10": "https://developer.huawei.com/consumer/cn/training/course/slightMooc/C101718767749261306", "safety_11": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_CloudFoundationKit-ArkTS", "safety_12": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/contacts-intro", "safety_13": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/restricted-permissions#section09041234151715", "safety_14": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/js-apis-file-picker#documentviewpicker", "safety_15": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_NEXT-FilesManger", "safety_16": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/camera-overview", "safety_17": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/js-apis-camerapicker", "safety_18": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/vision-cardrecognition", "safety_19": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-VisionKit", "safety_20": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/vision-documentscanner", "safety_21": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-VisionKit", "safety_22": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/map-introduction", "safety_23": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/map-location-details", "safety_24": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/map-location-selecting", "safety_25": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/map-location-division", "safety_26": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/js-apis-file-picker#audioviewpicker", "safety_27": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/select-user-file#选择音频类文件", "safety_28": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/save-user-file#保存音频类文件", "safety_29": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/system-security", "safety_30": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-app-data-security", "shared_1": "https://developer.huawei.com/consumer/cn/doc/design-guides/transition-animation-0000001750078488", "shared_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/arkts-shared-element-transition", "shared_3": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-one-shot-to-the-end", "imageanalyzer_1": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/vision-imageanalyzer", "imageanalyzer_2": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/core-vision-text-recognition", "imageanalyzer_3": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/core-vision-subject-segmentation", "imageanalyzer_4": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/vision-image-analyzer", "imageanalyzer_5": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/vision-imageanalyzer", "imageanalyzer_6": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/core-vision-object-detection", "imageanalyzer_7": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/vision-image-analyzer#section18771735164320", "imageanalyzer_8": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/ts-basic-components-image#enableanalyzer11", "imageanalyzer_9": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/ts-media-components-video#enableanalyzer12", "imageanalyzer_10": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/ts-basic-components-xcomponent#enableanalyzer12", "imageanalyzer_11": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/vision-image-analyzer", "imageanalyzer_12": "https://developer.huawei.com/consumer/cn/training/course/slightMooc/C101718769126547693", "imageanalyzer_13": "https://developer.huawei.com/consumer/cn/training/course/slightMooc/C101722224502553398", "imageanalyzer_14": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-CoreVisionKit", "imageanalyzer_15": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-VisionKit", "imageanalyzer_16": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/js-apis-image#pixelmap7", "imageanalyzer_17": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/ts-basic-components-image#enableanalyzer11", "imageanalyzer_18": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-content-creation#section4144207172817", "imageanalyzer_19": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/vision-introduction", "guildline_1": "https://developer.huawei.com/consumer/cn/doc/design-guides/ux-guidelines-overview-0000001760867048#section19443712173014", "design_1": "https://developer.huawei.com/consumer/cn/doc/", "design_2": "https://developer.huawei.com/consumer/cn/training/", "design_3": "https://developer.huawei.com/consumer/cn/forum/", "design_4": "https://developer.huawei.com/consumer/cn/activity/", "empowerment_1": "https://developer.huawei.com/consumer/cn/doc/guidebook/harmonyecoapp-guidebook-0000001761818040", "empowerment_2": "https://developer.huawei.com/consumer/cn/app/knowledge-map/", "empowerment_3": "https://developer.huawei.com/consumer/cn/teaching-video/", "empowerment_4": "https://developer.huawei.com/consumer/cn/codelabsPortal/getstarted/101718800110527001", "empowerment_5": "https://developer.huawei.com/consumer/cn/codelabsPortal/serviceTypes", "empowerment_6": "https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/application-dev-guide", "empowerment_7": "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/development-intro-api", "empowerment_8": "https://developer.huawei.com/consumer/cn/samples/", "empowerment_9": "https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-develop-once-deploy-everywhere", "empowerment_10": "https://developer.huawei.com/consumer/cn/forum/", "empowerment_11": "https://developer.huawei.com/consumer/cn/customerService/#/bot-dev-top/fap-top/faq-talk-top", "uxexperirnce_1": "https://developer.huawei.com/consumer/cn/doc/design-guides/multi-device-responsive-design-0000001796704861", "uxexperirnce_2": "https://developer.huawei.com/consumer/cn/doc/design-guides/ux-guidelines-overview-0000001760867048", "uxexperirnce_3": "https://developer.huawei.com/consumer/cn/doc/design-guides/responsive-design-overview-0000001746498066", "uxexperirnce_4": "https://developer.huawei.com/consumer/cn/design/resource/", "quickstart_1": "https://developer.huawei.com/consumer/cn/codelabsPortal/getstarted/101718800110527001", "quickstart_2": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-HelloWorld", "quickstart_3": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-SwiperBanner", "quickstart_4": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-BuildItem", "quickstart_5": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-GridAndList", "quickstart_6": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-BasicArchitectureDesignPart1", "quickstart_7": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-BasicArchitectureDesignPart2", "quickstart_8": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-ArkwebPageAdaptation", "quickstart_9": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-DataDrivenUIUpdates", "quickstart_10": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-SettingUpComponentNavigation", "quickstart_11": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-TTS", "quickstart_12": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-DevelopOnceDeployAnywhere", "quickstart_13": "https://developer.huawei.com/consumer/cn/codelabsPortal/carddetails/tutorials_Next-DistributedFlow", "uxanimation_1": "https://developer.huawei.com/consumer/cn/doc/design-guides/design-animation-overview-0000001750985338", "main_domain": "https://developer.huawei.com"}
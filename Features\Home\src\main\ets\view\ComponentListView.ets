// 导入能力工具包中的配置常量类
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的全局信息模型和页面上下文类型定义
import type { GlobalInfoModel, PageContext } from '@ohos/common';
// 导入通用模块中的断点类型和通用常量
import { BreakpointType, CommonConstants } from '@ohos/common';
// 导入通用业务模块中的横幅数据类型定义
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的各种组件和类型定义
import {
  BannerCard,
  BaseHomeEventType,
  BaseHomeView,
  CalculateHeightParam,
  CardStyleTypeEnum,
  CardTypeEnum,
  FullScreenNavigation,
  LoadingMoreItemBuilder,
  OffsetParam,
  TabBarType,
} from '@ohos/commonbusiness';
// 导入代码实验室卡片组件
import { CodeLabCard } from '../component/CodeLabCard';
// 导入列表卡片组件
import { ListCard } from '../component/ListCard';
// 导入图片列表卡片组件
import { PictureListCard } from '../component/PictureListCard';
// 导入组件卡片数据和组件内容类型定义
import type { ComponentCardData, ComponentContent } from '../model/ComponentData';
// 导入组件列表状态类型定义
import type { ComponentListState } from '../viewmodel/ComponentListState';
// 导入组件列表事件类型和视图模型类
import { ComponentListEventType, ComponentListViewModel } from '../viewmodel/ComponentListViewModel';

// 使用Component装饰器标记为组件，启用非活跃时冻结优化
@Component({ freezeWhenInactive: true })
// 导出组件列表视图结构体
export struct ComponentListView {
  // 使用StorageProp装饰器绑定全局信息模型，监听断点变化
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp装饰器绑定系统颜色模式，监听颜色模式变化
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 创建视图模型实例用于数据管理
  viewModel: ComponentListViewModel = ComponentListViewModel.getInstance();
  // 定义组件列表状态属性
  // 使用State装饰器定义组件列表状态，从视图模型获取初始状态
  @State componentListState: ComponentListState = this.viewModel.getState();
  // 定义组件列表页面上下文，从应用存储中获取
  private componentListPageContext: PageContext = AppStorage.get('componentListPageContext')!;
  // 创建滚动控制器实例用于控制滚动行为
  private scroller: Scroller = new Scroller();

  // 定义组件即将出现时的生命周期回调方法
  aboutToAppear(): void {
    // 向视图模型发送加载组件页面数据的事件
    this.viewModel.sendEvent({ type: ComponentListEventType.LOAD_COMPONENT_PAGE, param: null });
  }

  // 定义处理断点变化的方法
  handleBreakPointChange() {
    // 向视图模型发送更新流式布局分区的事件
    this.viewModel.sendEvent({ type: ComponentListEventType.UPDATE_FLOW_SECTION, param: null });
    // 向视图模型发送处理断点变化的事件，传递当前滚动偏移量和标签索引
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_BREAKPOINT_CHANGE,
      param: { yOffset: (this.scroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.HOME },
    });
  }

  // 定义处理颜色模式变化的方法
  handleColorModeChange() {
    // 向视图模型发送处理颜色变化的事件，传递当前滚动偏移量和标签索引
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_COLOR_CHANGE,
      param: { yOffset: (this.scroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.HOME },
    });
  }

  // 定义跳转到组件详情视图的方法
  jumpComponentDetailView(componentContent: ComponentContent) {
    // 向视图模型发送跳转详情页面的事件，传递组件内容数据
    this.viewModel.sendEvent<ComponentContent>({
      type: ComponentListEventType.JUMP_DETAIL_DETAIL,
      param: componentContent,
    });
  }

  // 定义跳转到代码实验室详情视图的方法
  jumpCodelabDetailView(componentCard: ComponentCardData) {
    // 判断组件卡片类型是否为组件类型
    if (componentCard.cardType === CardTypeEnum.COMPONENT) {
      // 跳转到组件详情视图，传递卡片内容的第一项
      this.jumpComponentDetailView(componentCard.cardContents?.[0]);
    }
  }

  // 定义跳转到横幅详情的方法
  jumpBannerDetail(banner: BannerData) {
    // 向视图模型发送跳转横幅详情的事件，传递横幅数据
    this.viewModel.sendEvent<BannerData>({ type: BaseHomeEventType.JUMP_BANNER_DETAIL, param: banner });
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建瀑布流容器组件
    WaterFlow({
      // 设置滚动控制器
      scroller: this.scroller,
      // 设置分区配置数组
      sections: this.componentListState.sections,
    }) {
      // 创建横幅卡片流项
      FlowItem() {
        // 创建横幅卡片组件
        BannerCard({
          // 设置标签视图类型为首页
          tabViewType: TabBarType.HOME,
          // 设置横幅状态数据
          bannerState: this.componentListState.bannerState,
          // 设置点击事件处理函数
          handleItemClick: (banner: BannerData) => {
            // 调用跳转横幅详情方法
            this.jumpBannerDetail(banner);
          },
        })
      }
      // 设置横幅卡片流项宽度为100%
      .width('100%')

      // 使用懒加载遍历组件卡片数据源
      LazyForEach(this.componentListState.cardSource, (item: ComponentCardData) => {
        // 创建组件卡片流项
        FlowItem() {
          // 判断卡片样式类型是否为图片在上列表样式
          if (item.cardStyleType === CardStyleTypeEnum.PICTURE_ABOVE_LIST) {
            // 创建图片列表卡片组件
            PictureListCard({
              // 传递组件卡片数据
              componentCardData: item,
              // 设置项目点击事件处理函数
              handleItemClick: (componentContent: ComponentContent) => {
                // 调用跳转组件详情视图方法
                this.jumpComponentDetailView(componentContent);
              },
            })
              // 设置复用ID为图片在上列表样式类型字符串
              .reuseId(CardStyleTypeEnum.PICTURE_ABOVE_LIST.toString())
          } else if (item.cardStyleType === CardStyleTypeEnum.LIST) {
            // 创建列表卡片组件
            ListCard({
              // 传递组件卡片数据
              componentCardData: item,
              // 设置项目点击事件处理函数
              handleItemClick: (componentContent: ComponentContent) => {
                // 调用跳转组件详情视图方法
                this.jumpComponentDetailView(componentContent);
              },
            })
              // 设置复用ID为列表样式类型字符串
              .reuseId(CardStyleTypeEnum.LIST.toString())
          } else {
            // 创建代码实验室卡片组件
            CodeLabCard({ componentCardData: item })
              // 设置复用ID为图片样式类型字符串
              .reuseId(CardStyleTypeEnum.PICTURE.toString())
              // 设置点击事件处理函数
              .onClick(() => {
                // 调用跳转代码实验室详情视图方法
                this.jumpCodelabDetailView(item);
              })
          }
        }
        // 设置流项宽度为100%
        .width('100%')
      }, (item: ComponentCardData) => item.id.toString())
      // 创建加载更多项流项
      FlowItem() {
        // 创建加载更多项构建器组件
        LoadingMoreItemBuilder(this.componentListState.loadingModel)
      }
      // 设置加载更多项宽度为100%
      .width('100%')
      // 设置加载更多项内边距
      .padding({
        // 根据导航指示器高度和标签栏高度计算底部内边距
        bottom: (this.globalInfoModel.naviIndicatorHeight +
          // 创建断点类型对象并根据当前断点获取标签栏高度
          (new BreakpointType({
            sm: CommonConstants.TAB_BAR_HEIGHT,
            md: CommonConstants.TAB_BAR_HEIGHT,
            lg: 0,
          }).getValue(this.globalInfoModel.currentBreakpoint))),
      })
    }
    // 根据断点类型设置瀑布流列间距
    .columnsGap(new BreakpointType({
      sm: $r('sys.float.padding_level6'),
      md: $r('sys.float.padding_level6'),
      lg: $r('sys.float.padding_level8'),
      xl: $r('sys.float.padding_level8'),
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 根据断点类型设置瀑布流行间距
    .rowsGap(new BreakpointType({
      sm: $r('sys.float.padding_level8'),
      md: $r('sys.float.padding_level6'),
      lg: $r('sys.float.padding_level8'),
      xl: $r('sys.float.padding_level12')
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 根据状态设置瀑布流边缘效果
    .edgeEffect(this.componentListState.hasEdgeEffect ? EdgeEffect.Spring : EdgeEffect.None)
    // 设置滚动帧开始事件处理函数
    .onScrollFrameBegin((offset: number, state: ScrollState) => {
      // 构建计算高度参数对象
      const param: CalculateHeightParam = { offset, state, yOffset: this.scroller.currentOffset().yOffset };
      // 向视图模型发送计算横幅高度事件并获取返回值
      const bannerHeightChange: boolean | void = this.viewModel.sendEvent<CalculateHeightParam>({
        type: BaseHomeEventType.CALCULATE_BANNER_HEIGHT,
        param,
      }) as boolean;
      // 判断横幅高度是否发生变化
      if (bannerHeightChange) {
        // 如果发生变化则消费所有偏移量
        return { offsetRemain: 0 };
      }
      // 否则保持原有偏移量不变
      // 返回保持原有偏移量的对象
      return { offsetRemain: offset };
    })
    // 设置瀑布流宽度为100%
    .width('100%')
    // 设置瀑布流高度为100%
    .height('100%')
    // 设置缓存数量为9个项目
    .cachedCount(9)
    // 设置滚动完成事件处理函数
    .onDidScroll(() => {
      // 向视图模型发送处理滚动偏移事件
      this.viewModel.sendEvent<OffsetParam>({
        type: BaseHomeEventType.HANDLE_SCROLL_OFFSET,
        param: { yOffset: this.scroller.currentOffset().yOffset, tabIndex: TabBarType.HOME },
      });
    })
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航组件
    FullScreenNavigation({
      // 设置顶部导航数据
      topNavigationData: this.componentListState.topNavigationData,
    })
  }

  // 定义构建方法用于创建组件列表视图的主要UI结构
  build() {
    // 创建导航容器组件
    Navigation(this.componentListPageContext.navPathStack) {
      // 创建基础主页视图组件
      BaseHomeView({
        // 设置加载模型数据
        loadingModel: this.componentListState.loadingModel,
        // 设置内容视图构建器函数
        contentView: () => {
          // 调用内容视图构建器方法
          this.ContentViewBuilder()
        },
        // 设置顶部标题视图构建器函数
        topTitleView: () => {
          // 调用顶部标题视图构建器方法
          this.TopTitleViewBuilder()
        },
        // 设置重新加载数据回调函数
        reloadData: () => {
          // 向视图模型发送加载组件页面事件
          this.viewModel.sendEvent({ type: ComponentListEventType.LOAD_COMPONENT_PAGE, param: null });
        },
      })
    }
    // 设置导航组件默认焦点为true
    .defaultFocus(true)
    // 设置导航模式为堆栈模式
    .mode(NavigationMode.Stack)
    // 隐藏导航标题栏
    .hideTitleBar(true)
  }
}
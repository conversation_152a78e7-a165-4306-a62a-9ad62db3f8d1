// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入标签页相关的属性映射数据
import {
  barHeightMapData,
  barPositionMapData,
  barWidthMapData,
  blurStyleMapData,
  fadingEdgeMapData,
  verticalMapData,
} from '../entity/TabAttributeMapping';

/**
 * 标签页描述器类
 * 继承自通用描述器，用于描述标签页组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TabDescriptor extends CommonDescriptor {
  // 标签页控制器，用于控制标签页的切换和状态
  public controller: TabsController = new TabsController();
  // 标签栏位置属性，默认使用默认值
  public barPosition: BarPosition = barPositionMapData.get('Default')!.value as BarPosition;
  public vertical: boolean = verticalMapData.get('Default')!.value as boolean;
  public barWidth: string = barWidthMapData.get('Default')!.value as string;
  public barHeight: string = barHeightMapData.get('Default')!.value as string;
  public backgroundBlurStyle: BlurStyle = blurStyleMapData.get('Default')!.value as BlurStyle;
  public fadingEdge: boolean = fadingEdgeMapData.get('Default')!.value as boolean;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'barPosition':
          this.barPosition = barPositionMapData.get(attribute.currentValue)?.value as BarPosition ??
            barPositionMapData.get('Default')!.value as BarPosition;
          break;
        case 'vertical':
          this.vertical = JSON.parse(attribute.currentValue) ?? verticalMapData.get('Default')!.value as boolean;
          if (this.vertical) {
            this.barWidth = '64vp';
            this.barHeight = '100%';
          } else {
            this.barWidth = '100%';
            this.barHeight = '30';
          }
          break;
        case 'backgroundBlurStyle':
          this.backgroundBlurStyle = blurStyleMapData.get(attribute.currentValue)?.value as BlurStyle ??
            blurStyleMapData.get('Default')!.value as BlurStyle;
          break;
        case 'fadingEdge':
          this.fadingEdge = JSON.parse(attribute.currentValue) ?? fadingEdgeMapData.get('Default')!.value as boolean;
          break;
        default:
          break;
      }
    });
  }
}
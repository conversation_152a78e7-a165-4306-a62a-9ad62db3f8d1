/**
 * 全局信息模型类
 * 用于存储和管理应用的全局状态信息
 * 包含设备信息、断点状态、屏幕尺寸等全局数据
 */
@Observed
export class GlobalInfoModel {
  // 折叠屏展开状态，true为展开，false为折叠
  public foldExpanded: boolean = false;
  // 当前断点类型，默认为中等屏幕断点
  public currentBreakpoint: BreakpointTypeEnum = BreakpointTypeEnum.MD;
  // 导航指示器高度
  public naviIndicatorHeight: number = 0;
  // 状态栏高度
  public statusBarHeight: number = 0;
  // 装饰区域高度
  public decorHeight: number = 0;
  // 设备屏幕高度
  public deviceHeight: number = 0;
  // 设备屏幕宽度
  public deviceWidth: number = 0;
}

/**
 * 断点类型枚举
 * 定义不同屏幕尺寸对应的断点类型
 * 用于响应式布局的断点判断
 */
export enum BreakpointTypeEnum {
  // 超小屏幕断点
  XS = 'xs',
  // 小屏幕断点
  SM = 'sm',
  // 中等屏幕断点
  MD = 'md',
  // 大屏幕断点
  LG = 'lg',
  // 超大屏幕断点
  XL = 'xl',
}
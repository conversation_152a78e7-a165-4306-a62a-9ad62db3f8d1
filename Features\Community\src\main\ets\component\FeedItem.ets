// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型
import { BreakpointType } from '@ohos/common';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';

// 使用Component装饰器定义动态项目组件
@Component
export struct FeedItem {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop装饰器定义发现内容属性
  @Prop discoverContent: DiscoverContent;

  // 定义构建方法
  build() {
    // 创建列布局
    Column() {
      // 创建图片组件
      Image($rawfile(this.discoverContent.mediaUrl))
        // 禁用拖拽
        .draggable(false)
        // 设置边框圆角
        .borderRadius($r('sys.float.corner_radius_level4'))
        // 设置占位图片
        .alt($r('app.media.img_placeholder'))
        // 设置宽度为100%
        .width('100%')
        // 设置布局权重为1
        .layoutWeight(1)
      // 创建列布局
      Column() {
        // 创建标题文本
        Text(this.discoverContent.title)
          // 设置字体大小为Body_M
          .fontSize($r('sys.float.Body_M'))
          // 设置字体颜色为主要字体色
          .fontColor($r('sys.color.font_primary'))
          // 设置字体粗细为中等
          .fontWeight(FontWeight.Medium)
          // 设置行高
          .lineHeight($r('app.float.feed_item_card_title_height'))
          // 设置最大行数为2
          .maxLines(2)
          // 设置文本溢出处理为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
        // 创建描述文本
        Text(this.discoverContent.desc)
          // 设置字体大小为Body_S
          .fontSize($r('sys.float.Body_S'))
          // 设置字体颜色为次要字体色
          .fontColor($r('sys.color.font_secondary'))
          // 设置字体粗细为常规
          .fontWeight(FontWeight.Regular)
          // 设置行高
          .lineHeight($r('app.float.feed_item_card_subtitle_height'))
          // 设置最大行数为1
          .maxLines(1)
          // 设置文本溢出处理为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置顶部边距
          .margin({ top: $r('sys.float.padding_level1') })
      }
      // 设置宽度为100%
      .width('100%')
      // 设置内边距
      .padding({
        left: $r('sys.float.padding_level2'),
        right: $r('sys.float.padding_level2'),
      })
      // 启用渲染组
      .renderGroup(true)
      // 设置顶部边距
      .margin({ top: $r('sys.float.padding_level4') })
      // 设置项目对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
    }
    // 根据断点类型设置高度
    .height(new BreakpointType({
      sm: $r('app.float.feed_item_card_height_sm'),
      md: $r('app.float.feed_item_card_height_md'),
      lg: $r('app.float.feed_item_card_height_lg'),
      xl: $r('app.float.feed_item_card_height_xl'),
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 设置宽度为100%
    .width('100%')
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 设置背景颜色为列表卡片背景色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置内边距
    .padding({
      left: $r('sys.float.padding_level4'),
      right: $r('sys.float.padding_level4'),
      top: $r('sys.float.padding_level4'),
      bottom: $r('sys.float.padding_level8'),
    })
    // 设置点击效果为重度
    .clickEffect({ level: ClickEffectLevel.HEAVY })
  }
}
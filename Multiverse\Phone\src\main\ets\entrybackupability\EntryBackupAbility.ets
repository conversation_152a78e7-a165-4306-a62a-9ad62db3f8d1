// 导入核心文件工具包中的备份扩展能力和包版本
import { BackupExtensionAbility, BundleVersion } from '@kit.CoreFileKit';
// 导入通用模块中的日志工具
import { Logger } from '@ohos/common';

// 日志标签常量
const TAG = '[EntryBackupAbility]';

/**
 * 入口备份能力类
 * 继承自BackupExtensionAbility，提供应用数据备份和恢复功能
 * 负责处理应用的备份和恢复操作
 */
export default class EntryBackupAbility extends BackupExtensionAbility {
  /**
   * 备份操作的异步方法
   * 当系统触发备份操作时被调用
   * 在此方法中实现应用数据的备份逻辑
   */
  async onBackup() {
    // 记录备份操作成功的日志
    Logger.info(TAG, 'onBackup ok');
  }

  /**
   * 恢复操作的异步方法
   * 当系统触发恢复操作时被调用
   * 在此方法中实现应用数据的恢复逻辑
   * @param bundleVersion 包版本信息，包含版本号等元数据
   */
  async onRestore(bundleVersion: BundleVersion) {
    // 记录恢复操作成功的日志，包含版本信息
    Logger.info(TAG, `onRestore ok: ${JSON.stringify(bundleVersion)}`);
  }
}
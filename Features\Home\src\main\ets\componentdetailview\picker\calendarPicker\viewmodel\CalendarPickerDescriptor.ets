// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../../viewmodel/CommonDescriptor';
// 导入字体粗细映射数据
import { fontWeightMapData } from '../../../common/entity/CommonMapData';
// 导入日历选择器相关的映射数据和类型定义
import {
  calendarAlignTypeMapData,
  EdgeAlign,
  edgeAlignDefault,
  textStyleDefault,
} from '../entity/CalendarPickerAttributeMapping';

/**
 * 日历选择器描述器类
 * 继承通用描述器，专门用于描述日历选择器组件的配置
 * 包含边缘对齐和文本样式的配置信息
 */
@Observed
export class CalendarPickerDescriptor extends CommonDescriptor {
  // 边缘对齐配置，默认使用预设的对齐方式
  public edgeAlign: EdgeAlign = edgeAlignDefault;
  // 文本样式配置，默认使用预设的文本样式
  public textStyle: PickerTextStyle = textStyleDefault;

  /**
   * 转换属性方法
   * 将原始属性数组转换为日历选择器的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'calendarAlignType':
          // 设置日历对齐类型，保持原有偏移量
          this.edgeAlign =
            {
              alignType: calendarAlignTypeMapData.get(attribute.currentValue)?.value ?? CalendarAlign.END,
              offset: this.edgeAlign.offset,
            };
          break;
        case 'calendarOffsetX':
          // 设置日历X轴偏移量，保持原有对齐类型和Y轴偏移
          this.edgeAlign =
            {
              alignType: this.edgeAlign.alignType,
              offset: { dx: Number(attribute.currentValue), dy: this.edgeAlign.offset?.dy || 0 },
            };
          break;
        case 'calendarOffsetY':
          // 设置日历Y轴偏移量，保持原有对齐类型和X轴偏移
          this.edgeAlign =
            {
              alignType: this.edgeAlign.alignType,
              offset: { dx: this.edgeAlign.offset?.dx || 0, dy: Number(attribute.currentValue) },
            };
          break;
        case 'pickerFontColor':
          // 设置选择器字体颜色，保持原有字体配置
          this.textStyle =
            { color: attribute.currentValue, font: this.textStyle.font };
          break;
        case 'pickerFontSize':
          // 设置选择器字体大小，保持原有颜色和字体粗细
          this.textStyle =
            {
              color: this.textStyle.color,
              font: { size: Number(attribute.currentValue), weight: this.textStyle.font?.weight },
            };
          break;
        case 'pickerFontWeight':
          // 设置选择器字体粗细，保持原有颜色和字体大小
          this.textStyle =
            {
              color: this.textStyle.color,
              font: { size: this.textStyle.font?.size, weight: fontWeightMapData.get(attribute.currentValue)?.value },
            };
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
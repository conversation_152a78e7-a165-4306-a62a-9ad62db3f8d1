// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入警告对话框对齐方式映射数据，包含各种对齐方式的配置选项
import { alertDialogAlignmentMapData } from '../entity/AlertDialogAttributeMapping';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 警告对话框代码生成器类
 * 实现通用代码生成器接口，用于生成警告对话框组件的代码
 */
export class AlertDialogCodeGenerator implements CommonCodeGenerator {
  // 警告对话框对齐方式的代码字符串，默认使用映射数据中的默认值
  private alertDialogAlignment: string = alertDialogAlignmentMapData.get('Default')!.code;

  /**
   * 生成警告对话框组件代码的方法
   * 根据传入的属性数组生成完整的警告对话框组件代码字符串
   * @param attributes 原始属性数组，包含组件的各种配置属性
   * @returns 生成的警告对话框组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理对话框对齐方式属性
        case 'dialogAlignment':
          // 从映射数据中获取对应的对齐方式代码，如果不存在则保持原值
          this.alertDialogAlignment =
            alertDialogAlignmentMapData.get(attribute.currentValue)?.code ?? this.alertDialogAlignment;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 返回生成的警告对话框组件代码字符串，包含完整的组件定义和配置
    return `import { promptAction } from '@kit.ArkUI';

@Component
struct AlertDialogComponent {
  build() {
    Column({ space: 5 }) {
      Button('点击警告弹窗')
        .buttonStyle(ButtonStyleMode.NORMAL)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))
        .onClick(() => {
          AlertDialog.show(
            {
              title: '标题',
              subtitle: '副标题',
              message: '内容',
              autoCancel: true,
              alignment: ${this.alertDialogAlignment},
              offset: { dx: 0, dy: -20 },
              buttonDirection: DialogButtonDirection.HORIZONTAL,
              buttons: [
                {
                  value: '按钮1',
                  action: () => {
                    try {
                      promptAction.showToast({
                        message: 'Callback when button1 is clicked',
                        duration: 2000
                      });
                    } catch (err) {
                      const error: BusinessError = err as BusinessError;
                      console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                    }
                  }
                },
                {
                  value: '按钮2',
                  action: () => {
                    try {
                      promptAction.showToast({
                        message: 'Callback when button2 is clicked',
                        duration: 2000
                      });
                    } catch (err) {
                      const error: BusinessError = err as BusinessError;
                      console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                    }
                  }
                }
              ],
              cancel: () => {
                console.log('Closed callbacks');
              },
              onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
                if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
                  dismissDialogAction.dismiss();
                }
                if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
                  dismissDialogAction.dismiss();
                }
              }
            })
        })
    }
    .width('100%')
  }
}`;
  }
}
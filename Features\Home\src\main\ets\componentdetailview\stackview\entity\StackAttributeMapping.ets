/**
 * 堆叠布局对齐方式映射类
 * 用于定义堆叠布局对齐的代码字符串和实际枚举值
 */
class StackAlignMapping {
  // 对齐方式代码字符串，用于代码生成
  public readonly code: string;
  // 对齐方式枚举值，用于实际设置
  public readonly value: Alignment;

  /**
   * 构造函数
   * @param code 对齐方式代码字符串
   * @param value 对齐方式枚举值
   */
  constructor(code: string, value: Alignment) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 堆叠布局对齐映射数据
 * 将对齐方式名称映射到具体的对齐配置
 */
export const stackAlignMapData: Map<string, StackAlignMapping> = new Map([
  // 顶部对齐
  ['Top', new StackAlignMapping('Alignment.Top', Alignment.Top)],
  // 左上角对齐
  ['TopStart', new StackAlignMapping('Alignment.TopStart', Alignment.TopStart)],
  // 右上角对齐
  ['TopEnd', new StackAlignMapping('Alignment.TopEnd', Alignment.TopEnd)],
  // 左侧对齐
  ['Start', new StackAlignMapping('Alignment.Start', Alignment.Start)],
  // 居中对齐
  ['Center', new StackAlignMapping('Alignment.Center', Alignment.Center)],
  // 右侧对齐
  ['End', new StackAlignMapping('Alignment.End', Alignment.End)],
  // 左下角对齐
  ['BottomStart', new StackAlignMapping('Alignment.BottomStart', Alignment.BottomStart)],
  // 右下角对齐
  ['BottomEnd', new StackAlignMapping('Alignment.BottomEnd', Alignment.BottomEnd)],
  // 底部对齐
  ['Bottom', new StackAlignMapping('Alignment.Bottom', Alignment.Bottom)],
  // 默认为居中对齐
  ['Default', new StackAlignMapping('Alignment.Center', Alignment.Center)],
]);
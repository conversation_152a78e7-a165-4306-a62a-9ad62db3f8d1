<!DOCTYPE html>
<html lang="zh-cn" xml:lang="zh-cn">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0" />
  <title>跨设备互联，打造无缝流转极致体验</title>
  <link rel="stylesheet" href="../../common/dist/main.css">
  <link rel="stylesheet" href="../../common/css/article.css">
</head>

<body>
  <div class="nested0" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002210449390"><a name="ZH-CN_TOPIC_0000002210449390"></a><a
      name="ZH-CN_TOPIC_0000002210449390"></a>
    <h1 class="topictitle1"><span class="topictitlenumber1">1</span> 跨设备互联，打造无缝流转快速体验</h1>
    <div class="topicbody" id="body39451090"></div>
    <div>
      <ul class="ullinks">
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002245529225">1.1 应用接续介绍</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002245569313">1.2 应用接续的运行原理</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002210609170">1.3 应用接续的场景化案例</a></strong><br>
        </li>
      </ul>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002245529225"><a
        name="ZH-CN_TOPIC_0000002245529225"></a><a name="ZH-CN_TOPIC_0000002245529225"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.1</span> 应用接续介绍</h2>
      <div class="topicbody" id="body0000002162564457">
        <p id="ZH-CN_TOPIC_0000002245529225__p10714163917815">
          应用接续，指当用户在一个设备上操作某个应用时，可以在另一个设备的同一个应用中快速切换，并无缝衔接上一个设备的应用体验。</p>
        <p id="ZH-CN_TOPIC_0000002245529225__p09363519718">
          比如在用户使用过程中，使用情景发生了变化，之前使用的设备不再适合继续当前任务，或者周围有更合适的设备，此时用户可以选择使用新的设备来继续当前的任务。接续完成后，之前设备的应用可退出或保留，用户可以将注意力集中在被拉起的设备上，继续执行任务。
        </p>
        <p id="ZH-CN_TOPIC_0000002245529225__p2936651171">如图所示，在手机上编辑备忘录，到办公室后切换到平板上继续编辑，完成任务的无缝衔接。</p>
        <div class="fignone" id="ZH-CN_TOPIC_0000002245529225__fig12671171415018"><span class="figcap"><span
              class="figurenumber">图1-1</span> 流转效果</span><br><img id="ZH-CN_TOPIC_0000002245529225__image1967131455017"
            src="ManulImages/1.gif"></div>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002245569313"><a
        name="ZH-CN_TOPIC_0000002245569313"></a><a name="ZH-CN_TOPIC_0000002245569313"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.2</span> 应用接续的运行原理</h2>
      <div class="topicbody" id="body0000002127244834">
        <div class="fignone" id="ZH-CN_TOPIC_0000002245569313__fig95384110112"><span class="figcap"><span
              class="figurenumber">图1-2</span> 流转运行原理</span><br><img
            id="ZH-CN_TOPIC_0000002245569313__image740216407446" src="ManulImages/2.png"></div>
        <p id="ZH-CN_TOPIC_0000002245569313__p115111621174417"></p>
        <ol id="ZH-CN_TOPIC_0000002245569313__ol156721452151219">
          <li id="ZH-CN_TOPIC_0000002245569313__li13672952111220">在源端，通过UIAbility的onContinue()回调，开发者可以保存待接续的业务数据。<p
              id="ZH-CN_TOPIC_0000002245569313__p16672135212123"><a
                name="ZH-CN_TOPIC_0000002245569313__li13672952111220"></a><a
                name="li13672952111220"></a>例如，浏览器应用完成应用接续，在源端浏览一个页面，到对端继续浏览。系统将自动保存页面状态，如当前页面的浏览进度；开发者需要通过onContinue()接口保存页面url等业务内容。
            </p>
          </li>
          <li id="ZH-CN_TOPIC_0000002245569313__li4672155216121">分布式框架提供跨设备应用界面、页面栈以及业务数据的保存和恢复机制，负责将数据从源端发送到对端。</li>
          <li id="ZH-CN_TOPIC_0000002245569313__li9672135219123">在对端，同一UIAbility通过onCreate()/onNewWant()接口恢复业务数据。</li>
        </ol>
        <p id="ZH-CN_TOPIC_0000002245569313__p6484143019101">具体限制参考应用接续<a href="article_continue_1" target="_blank"
            rel="noopener noreferrer">约束与规格</a>。</p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002210609170"><a
        name="ZH-CN_TOPIC_0000002210609170"></a><a name="ZH-CN_TOPIC_0000002210609170"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.3</span> 应用接续的场景化案例</h2>
      <div class="topicbody" id="body0000002162682901"></div>
      <div></div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002245569321"><a
          name="ZH-CN_TOPIC_0000002245569321"></a><a name="ZH-CN_TOPIC_0000002245569321"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.1</span> 典型案例1-内容编辑流转</h3>
        <div class="topicbody" id="body0000002169110881">
          <p id="ZH-CN_TOPIC_0000002245569321__p491612531162">
            在图文编创场景中，使用Ability的自由流转能力，使得编辑内容可以流转到其他更方便的设备上进行接续编辑，这样方便用户在不同设备上进行内容编辑。</p>
          <ol id="ZH-CN_TOPIC_0000002245569321__ol150216301284">
            <li id="ZH-CN_TOPIC_0000002245569321__li450233002815">用户在A设备编辑页面选择用户照片，并编辑标题、正文等文字信息。</li>
            <li id="ZH-CN_TOPIC_0000002245569321__li1450233011282">用户点击B设备Dock栏图标，在A设备输入的图片和文字信息衔接到B设备上</li>
          </ol>
          <p id="ZH-CN_TOPIC_0000002245569321__p17201411142210"><strong
              id="ZH-CN_TOPIC_0000002245569321__b121111162210">参考案例</strong></p>
          <p id="ZH-CN_TOPIC_0000002245569321__p1215252513818">场景使用：适用于文件类型数据接续，比如图片、视频以及PDF等。</p>
          <ol id="ZH-CN_TOPIC_0000002245569321__ol2211211102211">
            <li id="ZH-CN_TOPIC_0000002245569321__li1521161119222">最佳实践《<a href="article_continue_2" target="_blank"
                rel="noopener noreferrer">内容编辑多设备协同</a>》、《<a href="article_continue_3" target="_blank"
                rel="noopener noreferrer">AI辅助图文内容高效编创</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002245569321__li1721171112229">Sample示例代码《<a href="gitee_continue_1" target="_blank"
                rel="noopener noreferrer">基于应用接续及跨设备互通功能实现内容发布功能</a>》、《<a href="gitee_continue_2" target="_blank"
                rel="noopener noreferrer">AI辅助图文内容高效编创</a>》。</li>
          </ol>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002210609174"><a
          name="ZH-CN_TOPIC_0000002210609174"></a><a name="ZH-CN_TOPIC_0000002210609174"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.2</span> 典型案例2-列表进度流转</h3>
        <div class="topicbody" id="body0000002133671540">
          <p id="ZH-CN_TOPIC_0000002210609174__p8060118">
            系统支持用户接续上次浏览位置，无需重新从列表顶部开始滑动查找，精准定位到之前离开的条目附近，节省用户的时间和操作成本，提升在长列表内容浏览时的便利性和体验感。</p>
          <p id="ZH-CN_TOPIC_0000002210609174__p14371738132212"><strong
              id="ZH-CN_TOPIC_0000002210609174__b1537123814226">参考案例</strong></p>
          <p id="ZH-CN_TOPIC_0000002210609174__p64751025123">场景使用：适用于List、Grid以及Swiper封装的列表内容流转，比如瀑布流、轮播内容。</p>
          <ol id="ZH-CN_TOPIC_0000002210609174__ol1337038202218">
            <li id="ZH-CN_TOPIC_0000002210609174__li63743822214">最佳实践《<a href="article_continue_4" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002210609174__li2037038172217">Sample代码《<a href="gitee_continue_3" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
          </ol>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002245529221"><a
          name="ZH-CN_TOPIC_0000002245529221"></a><a name="ZH-CN_TOPIC_0000002245529221"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.3</span> 典型案例3-媒体播放进度流转</h3>
        <div class="topicbody" id="body0000002133829652">
          <p id="ZH-CN_TOPIC_0000002245529221__p192732343317">
            系统支持从源设备暂停的位置无缝续播视频，确保播放进度、画质和音效设置保持一致，为用户提供连贯的观影体验。无论是流媒体平台的影视内容，还是本地存储的视频文件，均能实现流畅的跨设备续播。</p>
          <p id="ZH-CN_TOPIC_0000002245529221__p1267305482213"><strong
              id="ZH-CN_TOPIC_0000002245529221__b46733545222">参考案例</strong></p>
          <p id="ZH-CN_TOPIC_0000002245529221__p92651841161517">场景使用：适用于媒体播放内容的流转，包括音频、视频等。</p>
          <ol id="ZH-CN_TOPIC_0000002245529221__ol16738547228">
            <li id="ZH-CN_TOPIC_0000002245529221__li1667335410226">最佳实践《<a href="article_continue_5" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002245529221__li1667365442210">Sample代码《<a href="gitee_continue_3" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
          </ol>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002245569317"><a
          name="ZH-CN_TOPIC_0000002245569317"></a><a name="ZH-CN_TOPIC_0000002245569317"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.4</span> 典型案例4-Web浏览进度流转</h3>
        <div class="topicbody" id="body0000002169229321">
          <p id="ZH-CN_TOPIC_0000002245569317__p84691721104314">系统支持迅速定位到源设备浏览的网页位置，保证用户的浏览连续性，避免重复查找信息的繁琐过程，提高信息获取的效率。
          </p>
          <p id="ZH-CN_TOPIC_0000002245569317__p57211665236"><strong
              id="ZH-CN_TOPIC_0000002245569317__b1372114602317">参考案例</strong></p>
          <p id="ZH-CN_TOPIC_0000002245569317__p18608736184317">场景使用：适用于Web组件嵌套H5页面内容的流转，主要使用JsBridge桥接。</p>
          <ol id="ZH-CN_TOPIC_0000002245569317__ol17721196152313">
            <li id="ZH-CN_TOPIC_0000002245569317__li272111642319">最佳实践《<a href="article_continue_6" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002245569317__li1721116112313">Sample代码《<a href="gitee_continue_3" target="_blank"
                rel="noopener noreferrer">浏览进度接续</a>》。</li>
          </ol>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002210449386"><a
          name="ZH-CN_TOPIC_0000002210449386"></a><a name="ZH-CN_TOPIC_0000002210449386"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.5</span> 参考资料</h3>
        <div class="topicbody" id="body0000002169114229">
          <ul id="ZH-CN_TOPIC_0000002210449386__ul918219240124">
            <li id="ZH-CN_TOPIC_0000002210449386__li9182132415121">HarmonyOS第一课《<a href="article_continue_7"
                target="_blank" rel="noopener noreferrer">自由流转介绍</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002210449386__li1812843320717">官网指南《<a href="article_continue_8" target="_blank"
                rel="noopener noreferrer">应用接续特性简介</a>》和《<a href="article_continue_9" target="_blank"
                rel="noopener noreferrer">应用接续开发指导</a>》。</li>
            <li id="ZH-CN_TOPIC_0000002210449386__li99431149301">Codelab《<a href="article_continue_10" target="_blank"
                rel="noopener noreferrer">分布式邮件</a>》。</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>
<script type="module" src="../../common/js/changehref.js"></script>
<script type="module" src="../../common/dist/main.js"></script>
</html>
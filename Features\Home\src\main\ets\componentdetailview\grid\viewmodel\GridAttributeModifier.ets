// 导入通用属性修改器基类，用于继承属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入Grid描述器类型，用于获取Grid的属性配置
import type { GridDescriptor } from './GridDescriptor';

/**
 * Grid属性修改器类
 * 继承通用属性修改器，专门用于动态修改Grid组件的属性
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class GridAttributeModifier extends CommonAttributeModifier<GridDescriptor, GridAttribute> {
  /**
   * 应用普通属性方法
   * 将Grid描述器中的属性值应用到Grid组件实例上
   * @param instance Grid属性实例，用于设置Grid组件的各种属性
   */
  public applyNormalAttribute(instance: GridAttribute): void {
    // 设置列间距属性
    this.assignAttribute((descriptor => descriptor.columnsGap), (val) => instance.columnsGap(val));
    // 设置行间距属性
    this.assignAttribute((descriptor => descriptor.rowsGap), (val) => instance.rowsGap(val));
    // 设置列模板属性，定义Grid的列布局
    this.assignAttribute((descriptor => descriptor.columnsTemplate), (val) => instance.columnsTemplate(val));
    // 设置行模板属性，定义Grid的行布局
    this.assignAttribute((descriptor => descriptor.rowsTemplate), (val) => instance.rowsTemplate(val));
    // 设置编辑模式属性，控制Grid是否支持编辑操作
    this.assignAttribute((descriptor => descriptor.operationMode), (val) => instance.editMode(val));
  }
}
// 定义指示器类型，支持点指示器、数字指示器或布尔值
type IndicatorType = DotIndicator | DigitIndicator | boolean;

/**
 * 指示器映射类
 * 用于存储指示器的代码字符串和实际值的映射关系
 */
class IndicatorMap {
  // 只读的代码字符串属性，用于代码生成
  public readonly code: string;
  // 只读的指示器值属性，存储实际的指示器对象
  public readonly value: IndicatorType;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 指示器值，实际的指示器对象
   */
  constructor(code: string, value: IndicatorType) {
    // 初始化代码字符串
    this.code = code;
    // 初始化指示器值
    this.value = value;
  }
}

// 点指示器代码字符串，用于生成点指示器的代码
const dotIndicatorCode: string = `new DotIndicator()
      .itemWidth(6)
      .itemHeight(6)
      .selectedItemWidth(12)
      .selectedItemHeight(6)
      .color($r('sys.color.comp_background_secondary'))
      .selectedColor($r('sys.color.comp_background_emphasize'))`;

// 数字指示器代码字符串，用于生成数字指示器的代码
const digitIndicatorCode: string = `new DigitIndicator()
      .fontColor($r('sys.color.font_primary'))
      .selectedFontColor($r('sys.color.font_primary'))
      .digitFont({ size: 16, weight: FontWeight.Bold })`;

// 导出指示器样式映射数据，包含不同类型的指示器配置
export const indicatorStyleMapData: Map<string, IndicatorMap> = new Map([
  // 点指示器配置，设置点的宽高和颜色
  ['DotIndicator', new IndicatorMap(dotIndicatorCode, new DotIndicator().itemWidth(6)
    .itemHeight(6)
    .selectedItemWidth(12)
    .selectedItemHeight(6)
    .color($r('sys.color.comp_background_secondary'))
    .selectedColor($r('sys.color.comp_background_emphasize')))],
  // 数字指示器配置，设置字体颜色和样式
  ['DigitIndicator', new IndicatorMap(digitIndicatorCode, new DigitIndicator().fontColor($r('sys.color.font_primary'))
    .selectedFontColor($r('sys.color.font_primary'))
    .digitFont({ size: 16, weight: FontWeight.Bold }))],
  // 无指示器配置
  ['None', new IndicatorMap('false', false)],
  ['Default', new IndicatorMap(dotIndicatorCode, new DotIndicator().itemWidth(6)
    .itemHeight(6)
    .selectedItemWidth(12)
    .selectedItemHeight(6)
    .color($r('sys.color.comp_background_secondary'))
    .selectedColor($r('sys.color.comp_background_emphasize')))],
]);

export class IndicatorEffectMap {
  public readonly code: string;
  public readonly value: EdgeEffect;

  constructor(code: string, value: EdgeEffect) {
    this.code = code;
    this.value = value;
  }
}

export const indicatorEffectMapping: Map<string, IndicatorEffectMap> = new Map([
  ['Default', new IndicatorEffectMap('EdgeEffect.Spring', EdgeEffect.Spring)],
  ['Spring', new IndicatorEffectMap('EdgeEffect.Spring', EdgeEffect.Spring)],
  ['Fade', new IndicatorEffectMap('EdgeEffect.Fade', EdgeEffect.Fade)],
  ['None', new IndicatorEffectMap('EdgeEffect.None', EdgeEffect.None)],
]);
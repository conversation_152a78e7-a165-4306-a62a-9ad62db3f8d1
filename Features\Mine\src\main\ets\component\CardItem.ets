// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入断点类型枚举和通用常量
import { BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入关于页面构建器
import { AboutBuilder } from '../view/AboutView';

// 使用Component装饰器定义卡片项目组件
@Component
export struct CardItem {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop装饰器定义是否显示属性
  @Prop isShow: boolean;
  // 定义文本内容资源
  textContent?: Resource;
  // 定义图标资源
  symbolSrc?: Resource;
  // 定义点击回调函数
  onclick: Function = () => {
  };
  // 定义关闭回调函数
  onClose: Function = () => {
  };

  // 定义构建方法
  build() {
    // 创建行布局
    Row() {
      // 显示图标
      SymbolGlyph(this.symbolSrc)
        .fontSize($r('sys.float.Title_M'))
        .fontColor([$r('sys.color.icon_secondary')])
      // 显示文本内容
      Text(this.textContent)
        .fontSize($r('sys.float.Subtitle_M'))
        .fontWeight(FontWeight.Medium)
        .fontColor($r('sys.color.font_primary'))
        .margin({ left: $r('sys.float.padding_level8') })
      // 添加空白填充
      Blank()
      // 显示右箭头图标
      SymbolGlyph($r('sys.symbol.chevron_forward'))
        .fontSize($r('sys.float.Subtitle_L'))
        .fontColor([$r('sys.color.icon_fourth')])
    }
    // 设置行宽度为100%
    .width('100%')
    // 设置行高度
    .height($r('app.float.mine_listItem_height'))
    // 设置点击事件
    .onClick(() => {
      // 调用点击回调函数
      this.onclick();
    })
    // 绑定底部弹窗
    .bindSheet(this.isShow, AboutBuilder(), {
      // 根据断点类型设置弹窗类型
      preferType: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? SheetType.BOTTOM :
      SheetType.CENTER,
      // 设置弹窗标题
      title: { title: $r('app.string.about') },
      // 根据断点类型设置弹窗高度
      height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
        ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
        CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,
      // 根据断点类型设置弹窗宽度
      width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SHEET_WIDTH_XL :
        undefined,
      // 设置弹窗即将消失回调
      onWillDisappear: () => {
        // 调用关闭回调函数
        this.onClose();
      },
    })
  }
}
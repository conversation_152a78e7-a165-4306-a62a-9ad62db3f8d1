// 导入通用模块中的通用常量
import { CommonConstants } from '@ohos/common';
// 导入标签栏数据模型和标签列表
import { TabBarData, TABS_LIST } from '../model/TabBarModel';

/**
 * 自定义侧边栏组件
 * 用于大屏设备的侧边导航栏，显示应用图标、标题和标签项
 * 支持悬停效果和选中状态的视觉反馈
 */
@Component
export struct CustomSideBar {
  // 当前选中的标签索引，必需属性
  @Prop @Require currentIndex: number;
  // 焦点状态，用于控制悬停效果
  @State focus: boolean = false;
  // 侧边栏切换回调函数
  sideBarChange: (index: number) => void = (index: number) => {
  };

  /**
   * 标签项构建器
   * 构建单个标签项的UI，包含图标和文本
   * @param item 标签栏数据
   */
  @Builder
  BarItemBuilder(item: TabBarData) {
    // 创建水平排列的行容器
    Row() {
      // 符号图标
      SymbolGlyph(item.icon)
        // 设置图标字体大小
        .fontSize($r('sys.float.Title_M'))
        // 根据是否选中设置图标颜色
        .fontColor(item.id === this.currentIndex ? [$r('sys.color.icon_emphasize')] :
          [$r('sys.color.icon_secondary')])
        // 设置渲染策略为多重透明度
        .renderingStrategy(SymbolRenderingStrategy.MULTIPLE_OPACITY)
        // 设置符号效果，选中时显示弹跳动画
        .symbolEffect(new BounceSymbolEffect(EffectScope.LAYER, EffectDirection.UP), item.id === this.currentIndex)
        // 设置图标内边距
        .padding({ left: $r('sys.float.padding_level4'), right: $r('sys.float.padding_level4') })
      // 标签文本
      Text(item.title)
        // 设置文本字体大小
        .fontSize($r('sys.float.Body_L'))
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 根据是否选中设置文本颜色
        .fontColor(item.id === this.currentIndex ? $r('sys.color.interactive_active') :
        $r('sys.color.font_tertiary'))
    }
    // 设置垂直居中对齐
    .alignItems(VerticalAlign.Center)
    // 根据选中状态和焦点状态设置背景颜色
    .backgroundColor(this.currentIndex !== item.id ? Color.Transparent :
      this.focus ? $r('app.color.hmos_side_bar_background_color') : $r('sys.color.interactive_hover'))
    // 设置宽度为100%
    .width('100%')
    // 设置高度
    .height($r('app.float.side_bar_height'))
    // 设置外边距
    .margin({
      top: $r('sys.float.padding_level4'),
      bottom: $r('sys.float.padding_level1'),
    })
    // 设置圆角半径
    .borderRadius($r('sys.float.corner_radius_level4'))
    // 设置点击事件处理
    .onClick(() => {
      // 如果点击的不是当前选中项，触发切换
      if (this.currentIndex !== item.id) {
        this.sideBarChange(item.id);
      }
    })
  }

  /**
   * 构建自定义侧边栏组件的UI结构
   * 包含应用标题区域和标签项列表
   */
  build() {
    // 创建垂直布局的列容器
    Column() {
      // 应用标题区域
      Row() {
        // 应用图标
        Image($r('app.media.ic_start_icon'))
          // 设置图标宽度
          .width($r('app.float.app_icon_width'))
          // 设置宽高比为1:1
          .aspectRatio(1)
          // 设置圆角半径
          .borderRadius($r('sys.float.corner_radius_level4'))
          // 设置右边距
          .margin({ right: $r('sys.float.padding_level6') })
        // 应用标题文本
        Text($r('app.string.EntryAbility_label'))
          // 设置文本颜色
          .fontColor($r('sys.color.font_primary'))
          // 设置字体大小
          .fontSize($r('sys.float.Body_L'))
      }
      // 设置标题行的外边距
      .margin({ left: $r('sys.float.padding_level4'), right: $r('sys.float.padding_level4') })
      // 设置标题行高度
      .height($r('app.float.side_bar_title_height'))
      // 设置标题行宽度为100%
      .width('100%')

      // 遍历标签列表，创建标签项
      ForEach(TABS_LIST, (item: TabBarData) => {
        // 调用标签项构建器
        this.BarItemBuilder(item)
      }, (item: TabBarData) => JSON.stringify(item) + this.currentIndex)
    }
    // 设置侧边栏宽度
    .width(CommonConstants.SIDE_BAR_WIDTH)
    // 设置侧边栏高度为100%
    .height('100%')
    // 设置侧边栏内边距
    .padding({
      left: $r('sys.float.padding_level8'),
      right: $r('sys.float.padding_level8'),
    })
    // 设置悬停事件处理
    .onHover((isHover: boolean) => {
      // 更新焦点状态
      this.focus = isHover;
    })
  }
}
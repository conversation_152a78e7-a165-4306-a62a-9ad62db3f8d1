// 导入方舟UI工具包中的窗口模块，用于监听软键盘状态
import { window } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型和日志记录器
import { GlobalInfoModel, Logger } from '@ohos/common';
// 导入详情页常量，包含动画持续时间等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入文本区域描述器类型，用于描述文本区域组件配置
import type { TextAreaDescriptor } from '../viewmodel/TextAreaDescriptor';
// 导入文本区域属性修改器，用于修改文本区域组件属性
import { TextAreaAttributeModifier } from '../viewmodel/TextAreaAttributeModifier';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[TextAreaBuilder]';

@Builder
export function TextAreaBuilder($$: DescriptorWrapper) {
  TextAreaComponent({ textAreaDescriptor: $$.descriptor as TextAreaDescriptor })
}

@Component
struct TextAreaComponent {
  @Prop textAreaDescriptor: TextAreaDescriptor;
  // Height of the component preview area.
  @State textAreaInputHeight: number = 0;
  @State contentOffset: number = 0;

  aboutToAppear(): void {
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
    window.getLastWindow(getContext(this)).then(currentWindow => {
      // Monitor the appearance and disappearance of the soft keyboard.
      try {
        currentWindow.on('avoidAreaChange', async data => {
          if (data.type !== window.AvoidAreaType.TYPE_KEYBOARD) {
            return;
          }
          const keyboardHeight: number = px2vp(data.area.bottomRect.height);
          /* When the size of the component preview area exceeds half the screen, the keyboard
           will cover the preview area. At this time, the component needs to move up when the keyboard appears.
           */
          if (keyboardHeight > 0 && this.textAreaInputHeight / globalInfoModel.deviceHeight > 0.5) {
            this.contentOffset = keyboardHeight / 2;
          } else {
            this.contentOffset = 0;
          }
        });
      } catch (err) {
        const error: BusinessError = err as BusinessError;
        Logger.error(TAG, `CurrentWindow invoke error, the code is ${error.message}, the message is ${error.message}`);
      }
    });
  }

  build() {
    Column() {
      TextArea({
        text: $r('app.string.textarea_text'),
        placeholder: $r('app.string.text_placeholder'),
      })
        .margin({
          left: $r('app.float.common_left_right_margin'),
          right: $r('app.float.common_left_right_margin'),
          bottom: this.contentOffset,
        })
        .fontWeight(FontWeight.Regular)
        .fontSize($r('sys.float.Body_L'))
        .enterKeyType(EnterKeyType.Done)
        .borderRadius($r('sys.float.corner_radius_level8'))
        .backgroundColor($r('sys.color.comp_background_tertiary'))
        .maxLines(this.textAreaDescriptor.maxLines)
        .attributeModifier(new TextAreaAttributeModifier(this.textAreaDescriptor))
        .animation({
          duration: DetailPageConstant.UP_DURATION,
          curve: Curve.Linear,
          playMode: PlayMode.Normal,
        })
    }
    .height('100%')
    .width($r('app.float.multiline_text_width'))
    .justifyContent(FlexAlign.Center)
    .onAreaChange((_: Area, newArea: Area) => {
      this.textAreaInputHeight = Number(newArea.height);
    })
  }
}
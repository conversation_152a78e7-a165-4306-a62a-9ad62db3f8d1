// 导入全局信息模型类型
import type { GlobalInfoModel } from '../model/GlobalInfoModel';
// 导入断点系统工具
import { BreakpointType } from '../util/BreakpointSystem';
// 导入Web工具类
import { WebUtil } from '../util/WebUtil';
// 导入加载视图组件
import { LoadingView } from '../view/LoadingView';
// 导入无网络视图组件
import { NoNetworkView } from '../view/NoNetworkView';

/**
 * Web URL类型枚举
 * 定义支持的不同Web站点类型
 */
export enum WebUrlType {
  // Gitee网站类型
  GITEE = 0,
  // HarmonyOS网站类型
  HARMONYOS = 1,
}

// Gitee网站基础宽度常量
const GITEE_WEB_BASE_WIDTH: number = 540.00;
// Gitee网站中等屏幕顶部高度常量
const GITEE_TOP_HEIGHT_MD: number = 57;
// Gitee网站超大屏幕顶部高度常量
const GITEE_TOP_HEIGHT_XL: number = 64;
// HarmonyOS网站顶部高度常量
const HARMONYOS_TOP_HEIGHT: number = 56;

/**
 * Web页面组件
 * 用于在应用内嵌入和显示Web页面内容
 * 支持不同类型的Web站点和响应式布局
 */
@Component
struct WebSheet {
  // 全局信息模型，用于获取设备信息和断点状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // Web页面加载状态
  @StorageProp('webIsLoading') isLoading: boolean = false;
  // Web页面URL地址
  @Prop url: string;
  // Web页面URL类型
  @Prop urlType: WebUrlType;
  // 加载失败状态
  @State loadFailed: boolean = false;

  /**
   * 组件即将出现时的生命周期方法
   * 检查Web页面是否已加载
   */
  aboutToAppear(): void {
    // 检查Web页面加载状态
    this.checkWebLoaded();
  }

  /**
   * 检查Web页面加载状态方法
   * 通过WebUtil工具类检查指定URL的Web页面是否加载成功
   */
  checkWebLoaded() {
    // 如果Web页面未加载成功
    if (!WebUtil.checkWebLoaded(this.url)) {
      // 设置加载失败状态为true
      this.loadFailed = true;
    } else {
      // 设置加载失败状态为false
      this.loadFailed = false;
    }
  }

  /**
   * 构建Web页面组件的UI结构
   * 包含Web内容容器和加载状态视图
   */
  build() {
    // 创建堆叠容器，用于叠加显示Web内容和状态视图
    Stack() {
      // 创建Web节点容器，用于显示Web页面内容
      NodeContainer(WebUtil.getWebNode(this.url))
        // 设置容器宽度为100%
        .width('100%')
        // 设置容器顶部边距，根据不同URL类型和断点计算偏移量
        .margin({
          top: -(this.urlType === WebUrlType.GITEE ? new BreakpointType({
            // 小屏幕断点的顶部偏移量计算
            sm: (this.globalInfoModel.deviceWidth / GITEE_WEB_BASE_WIDTH) * GITEE_TOP_HEIGHT_XL,
            // 中等屏幕断点的顶部偏移量
            md: GITEE_TOP_HEIGHT_MD,
            // 大屏幕断点的顶部偏移量
            lg: GITEE_TOP_HEIGHT_MD,
            // 超大屏幕断点的顶部偏移量
            xl: GITEE_TOP_HEIGHT_XL,
          }).getValue(this.globalInfoModel.currentBreakpoint) : HARMONYOS_TOP_HEIGHT),
        })
      // 如果正在加载
      if (this.isLoading) {
        // 显示加载视图
        LoadingView(this.globalInfoModel.currentBreakpoint)
      } else if (this.loadFailed) {
        // 如果加载失败，显示无网络视图
        NoNetworkView(this.globalInfoModel.currentBreakpoint, () => {
          // 重新检查Web页面加载状态
          this.checkWebLoaded();
        })
      }
    }
    // 设置堆叠容器宽度为100%
    .width('100%')
    // 设置堆叠容器高度为100%
    .height('100%')
  }
}

/**
 * Web页面构建器函数
 * 用于创建Web页面组件的便捷构建器
 * @param url Web页面URL地址
 * @param urlType Web页面URL类型
 */
@Builder
export function WebSheetBuilder(url: string, urlType: WebUrlType) {
  // 创建列容器
  Column() {
    // 创建Web页面组件实例
    WebSheet({ url, urlType })
  }
}
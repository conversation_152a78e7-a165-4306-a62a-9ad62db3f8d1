// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、列枚举、通用常量、加载更多、加载状态和无更多组件
import { BreakpointType, ColumnEnum, CommonConstants, LoadingMore, LoadingStatus, NoMore } from '@ohos/common';
// 导入通用业务模块中的卡片样式类型枚举和示例详情参数
import { CardStyleTypeEnum, SampleDetailParams } from '@ohos/commonbusiness';
// 导入示例常量
import { SampleConstant } from '../common/SampleConstant';
// 导入示例数据模型类型
import type { SampleCardData, SampleCategory, SampleContent } from '../model/SampleData';
// 导入实践事件类型和实践视图模型
import { PracticeEventType, PracticeViewModel } from '../viewmodel/PracticeViewModel';
// 导入基础分类视图
import { BaseCategoryView } from './BaseCategoryView';
// 导入图片上方文本卡片
import { PictureAboveTextCard } from './PictureAboveTextCard';
// 导入图片卡片
import { PictureCard } from './PictureCard';
// 导入示例列表卡片
import { SampleListCard } from './SampleListCard';
// 导入示例滚动卡片
import { SampleScrollCard } from './SampleScrollCard';

// 使用Component装饰器定义分类示例组件，设置非活跃时冻结
@Component({ freezeWhenInactive: true })
export struct CategorySamples {
  // 创建滚动控制器
  scroller: Scroller = new Scroller();
  // 创建实践视图模型实例
  viewModel: PracticeViewModel = PracticeViewModel.getInstance();
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop装饰器定义示例分类属性
  @Prop sampleCategory: SampleCategory;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 如果示例卡片不存在
    if (!this.sampleCategory.sampleCards) {
      // 发送加载示例列表事件
      this.viewModel.sendEvent<SampleCategory>({
        type: PracticeEventType.LOAD_SAMPLE_LIST,
        param: this.sampleCategory,
      });
    }
  }

  // 定义跳转到详情的方法
  jumpToDetail(currentIndex: number, sampleCardId: number) {
    // 发送跳转详情事件
    this.viewModel.sendEvent<SampleDetailParams>({
      type: PracticeEventType.JUMP_DETAIL_DETAIL,
      param: { currentIndex, sampleCardId },
    });
  }

  // 使用Builder装饰器定义分类内容视图构建器
  @Builder
  CategoryContentViewBuilder() {
    // 创建网格行布局
    GridRow({
      // 设置不同断点的列数
      columns: { sm: ColumnEnum.SM, md: ColumnEnum.MD, lg: ColumnEnum.LG },
      // 设置网格间距
      gutter: { y: $r('sys.float.padding_level8'), x: $r('sys.float.padding_level8') },
      // 设置网格方向为行
      direction: GridRowDirection.Row,
    }) {
      // 使用Repeat组件遍历示例卡片
      Repeat(this.sampleCategory.sampleCards)
        // 定义每个重复项的渲染
        .each((repeatItem: RepeatItem<SampleCardData>) => {
          // 创建网格列
          GridCol({ span: CommonConstants.SPAN_4 }) {
            // 创建图片上方文本卡片
            PictureAboveTextCard({ sampleCardData: repeatItem.item })
              // 设置点击事件
              .onClick(() => this.jumpToDetail(0, repeatItem.item.id))
          }
        })
        // 设置重复项的键
        .key((item: SampleCardData) => item.id.toString())
        // 设置模板ID
        .templateId((item: SampleCardData) => item.cardStyleType.toString())
        // 定义图片到轮播图模板
        .template(CardStyleTypeEnum.PICTURE_TO_SWIPER.toString(),
          (repeatItem: RepeatItem<SampleCardData>) => {
            // 创建网格列
            GridCol({ span: CommonConstants.SPAN_4 }) {
              // 创建图片上方文本卡片
              PictureAboveTextCard({ sampleCardData: repeatItem.item })
                // 设置点击事件
                .onClick(() => this.jumpToDetail(repeatItem.index, repeatItem.item.detailCardId))
            }
          })
        // 定义图片上方文本模板
        .template(CardStyleTypeEnum.PICTURE_ABOVE_TEXT.toString(),
          (repeatItem: RepeatItem<SampleCardData>) => {
            // 创建网格列
            GridCol({ span: CommonConstants.SPAN_4 }) {
              // 创建图片上方文本卡片
              PictureAboveTextCard({ sampleCardData: repeatItem.item })
                // 设置点击事件
                .onClick(() => this.jumpToDetail(0, repeatItem.item.id))
            }
          })
        // 定义列表模板
        .template(CardStyleTypeEnum.LIST.toString(), (repeatItem: RepeatItem<SampleCardData>) => {
          // 如果示例内容数量大于2
          if (repeatItem.item.sampleContents.length > 2) {
            // 创建跨度较大的网格列
            GridCol({
              span: { sm: CommonConstants.SPAN_4, md: CommonConstants.SPAN_8, lg: CommonConstants.SPAN_12 }
            }) {
              // 创建示例滚动卡片
              SampleScrollCard({
                sampleCardData: repeatItem.item,
                handleItemClick: (index: number, samples: SampleContent[]) => {
                  // 处理项目点击事件
                  this.jumpToDetail(index, repeatItem.item.id);
                },
              })
                // 设置边距
                .margin({
                  left: new BreakpointType({
                    sm: SampleConstant.SCROLL_MARGIN_SM,
                    md: SampleConstant.SCROLL_MARGIN_MD,
                    lg: SampleConstant.SCROLL_MARGIN_LG,
                  }).getValue(this.globalInfoModel.currentBreakpoint),
                  right: new BreakpointType({
                    sm: SampleConstant.SCROLL_MARGIN_SM,
                    md: SampleConstant.SCROLL_MARGIN_MD,
                    lg: SampleConstant.SCROLL_MARGIN_LG,
                  }).getValue(this.globalInfoModel.currentBreakpoint),
                })
            }
            // 设置不裁剪
            .clip(false)
          } else {
            // 否则创建普通网格列
            GridCol({ span: CommonConstants.SPAN_4 }) {
              // 创建示例列表卡片
              SampleListCard({
                sampleCardData: repeatItem.item,
                handleItemClick: (index: number, samples: SampleContent[]) => {
                  // 处理项目点击事件
                  this.jumpToDetail(index, repeatItem.item.id);
                },
              })
            }
          }
        })
        // 定义图片模板
        .template(CardStyleTypeEnum.PICTURE.toString(), (repeatItem: RepeatItem<SampleCardData>) => {
          // 创建网格列
          GridCol({ span: { sm: CommonConstants.SPAN_4, md: CommonConstants.SPAN_4, lg: CommonConstants.SPAN_8 } }) {
            // 创建图片卡片
            PictureCard({ sampleCardData: repeatItem.item })
              // 设置点击事件
              .onClick(() => this.jumpToDetail(0, repeatItem.item.id))
          }
        })
      // 创建底部网格列
      GridCol({ span: { sm: CommonConstants.SPAN_4, md: CommonConstants.SPAN_8, lg: CommonConstants.SPAN_12 } }) {
        // 创建列布局
        Column() {
          // 如果加载更多状态为加载中
          if (this.sampleCategory.loadingModel.loadingMoreStatus === LoadingStatus.LOADING) {
            // 显示加载更多组件
            LoadingMore()
          } else if (this.sampleCategory.sampleCards.length > 0 && !this.sampleCategory.loadingModel.hasNextPage) {
            // 如果有数据且没有下一页则显示无更多组件
            NoMore()
          }
        }
      }
      // 设置底部内边距
      .padding({
        bottom: this.globalInfoModel.naviIndicatorHeight +
          (new BreakpointType({
            sm: CommonConstants.TAB_BAR_HEIGHT,
            md: CommonConstants.TAB_BAR_HEIGHT,
            lg: 0,
          }).getValue(this.globalInfoModel.currentBreakpoint)),
      })
    }
    // 设置网格行内边距
    .padding({
      left: new BreakpointType({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
      right: new BreakpointType({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
      top: $r('sys.float.padding_level4')
    })
    // 设置不裁剪
    .clip(false)
  }

  // 定义构建方法
  build() {
    // 创建基础分类视图
    BaseCategoryView({
      // 设置加载模型
      loadingModel: this.sampleCategory.loadingModel,
      // 设置内容视图
      contentView: () => {
        // 调用分类内容视图构建器
        this.CategoryContentViewBuilder();
      },
      // 设置重新加载数据方法
      reloadData: () => {
        // 发送加载示例列表事件
        this.viewModel.sendEvent<SampleCategory>({
          type: PracticeEventType.LOAD_SAMPLE_LIST,
          param: this.sampleCategory,
        });
      },
    })
  }
}
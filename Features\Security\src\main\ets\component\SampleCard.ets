// 导入能力工具包中的通用类型
import { common } from '@kit.AbilityKit';
// 导入方舟数据工具包中的统一类型描述符
import { uniformTypeDescriptor as utd } from '@kit.ArkData';
// 导入基础服务工具包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入分享工具包中的系统分享功能
import { systemShare } from '@kit.ShareKit';
// 导入通用模块中的各种工具和类型
import {
  Logger,
  BreakpointType,
  BreakpointTypeEnum,
  CommonConstants,
  WebSheetBuilder,
  WebUrlType,
  WebUtil,
  type GlobalInfoModel,
} from '@ohos/common';
// 导入示例详情常量
import { SampleDetailConstant } from '../constant/CommonConstants';
// 导入示例详情页面视图模型和相关事件
import { BindSheetEvent, SampleDetailPageVM, LoadSampleEvent } from '../viewmodel/SampleDetailPageVM';
// 导入示例卡片数据类型
import type { SampleCardData } from '../viewmodel/SampleDetailState';

// 定义日志标签
const TAG = '[SampleCard]';

// 使用Component装饰器定义示例卡片组件
@Component
export struct SampleCard {
  // 创建示例详情页面视图模型实例
  viewModel: SampleDetailPageVM = SampleDetailPageVM.getInstance();
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Consume装饰器获取当前索引
  @Consume currentIndex: number;
  // 使用Prop装饰器定义示例索引属性
  @Prop sampleIndex: number;
  // 使用ObjectLink装饰器定义示例卡片对象链接
  @ObjectLink sampleCard: SampleCardData;
  // 使用State装饰器定义返回图标背景颜色状态
  @State backIconBgColor: ResourceColor =
    this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? Color.Transparent :
    $r('sys.color.comp_background_tertiary');

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 创建Web节点
    WebUtil.createWebNode(this.sampleCard.originalUrl, this.getUIContext(), NestedScrollMode.SELF_ONLY);
  }

  // 定义即将消失生命周期方法
  aboutToDisappear(): void {
    // 移除Web节点
    WebUtil.removeNode(this.sampleCard.originalUrl);
  }

  // 定义处理分享的私有方法
  private handleShare(): void {
    // 检查是否支持系统分享功能
    if (canIUse('SystemCapability.Collaboration.SystemShare')) {
      // 创建分享数据对象
      const shareData: systemShare.SharedData = new systemShare.SharedData({
        utd: utd.UniformDataType.HYPERLINK,
        content: this.sampleCard.originalUrl,
        title: this.sampleCard.title,
        description: this.sampleCard.originalUrl
      });
      // 创建分享控制器
      let controller: systemShare.ShareController = new systemShare.ShareController(shareData);
      // 获取上下文
      let context = getContext(this) as common.UIAbilityContext;
      // 显示分享界面
      controller.show(context, {
        selectionMode: systemShare.SelectionMode.SINGLE,
        previewMode: systemShare.SharePreviewMode.DEFAULT
      }).catch((error: BusinessError) => {
        // 记录分享错误日志
        Logger.error(TAG, `Sample link sharing error. Code: ${error.code}, message: ${error.message}`);
      });
    }
  }

  // 使用Builder装饰器定义标题组件
  @Builder
  TitleComponent() {
    // 创建标题行布局
    Row() {
      // 显示示例代码文本
      Text($r('app.string.sample_code'))
        .maxLines(1)
        .height($r('app.float.samplename_height'))
        .lineHeight($r('app.float.cardtitle_lineheight'))
        .fontColor($r('sys.color.font_primary'))
        .fontWeight(FontWeight.Bold)
        .fontSize($r('sys.float.Title_S'))
        .textOverflow({ overflow: TextOverflow.Ellipsis })
      // 创建分享按钮
      Button({ type: ButtonType.Circle }) {
        // 显示分享符号
        SymbolGlyph($r('sys.symbol.share'))
          .fontColor([$r('sys.color.icon_primary')])
          .fontSize($r('sys.float.Title_M'))
      }
      // 设置按钮高度
      .height($r('app.float.back_button_height'))
      // 设置按钮宽高比为1:1
      .aspectRatio(1)
      // 设置按钮背景颜色
      .backgroundColor(this.backIconBgColor)
      // 设置按钮点击事件
      .onClick(() => this.handleShare())
      // 设置按钮悬停事件
      .onHover((isHover: boolean) => {
        this.backIconBgColor = isHover ? $r('sys.color.comp_background_tertiary') : Color.Transparent;
      })
    }
    // 设置标题行两端对齐
    .justifyContent(FlexAlign.SpaceBetween)
    // 设置标题行宽度为100%
    .width('100%')
  }

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 显示示例卡片标题
      Text(this.sampleCard.title)
        .maxLines(1)
        .height($r('app.float.samplename_height'))
        .lineHeight($r('app.float.cardtitle_lineheight'))
        .fontColor($r('sys.color.font_primary'))
        .fontWeight(FontWeight.Bold)
        .fontSize($r('sys.float.Subtitle_L'))
        .margin({ bottom: $r('sys.float.padding_level1') })
        .textOverflow({ overflow: TextOverflow.Ellipsis })

      // 显示示例卡片描述
      Text(this.sampleCard.desc)
        .maxLines(SampleDetailConstant.SUBTITLE_MAXLINE)
        .height($r('app.float.sampledesc_height'))
        .fontSize($r('sys.float.Subtitle_S'))
        .fontWeight(FontWeight.Regular)
        .lineHeight($r('app.float.cardsubtitle_lineheight'))
        .fontColor($r('sys.color.font_secondary'))
        .margin({
          bottom: $r('sys.float.padding_level12'),
        })
        .textOverflow({ overflow: TextOverflow.Ellipsis })

      // 创建按钮行布局
      Row({
        space: new BreakpointType({
          sm: CommonConstants.SPACE_16,
          md: CommonConstants.SPACE_16,
          lg: CommonConstants.SPACE_24,
        }).getValue(this.globalInfoModel.currentBreakpoint),
      }) {
        // 创建阅读代码按钮
        Button($r('app.string.read_code'))
          .cardButtonStyle($r('sys.color.comp_background_tertiary'), $r('sys.color.background_emphasize'))
          .onClick(() => {
            // 发送绑定表单事件
            this.viewModel.sendEvent(new BindSheetEvent(this.sampleIndex, true));
          })
          // 绑定Web表单
          .bindSheet(this.sampleCard.bindSheetShow,
            WebSheetBuilder(this.sampleCard.originalUrl, WebUrlType.GITEE), {
              // 设置表单标题
              title: () => this.TitleComponent(),
              // 设置表单类型为居中
              preferType: SheetType.CENTER,
              // 根据断点设置表单高度
              height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
                ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
                CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,
              // 根据断点设置表单宽度
              width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SHEET_WIDTH_XL :
                undefined,
              // 设置表单即将消失回调
              onWillDisappear: () => {
                // 移除Web节点
                WebUtil.getWebNode(this.sampleCard.originalUrl)?.remove();
                // 发送绑定表单事件
                this.viewModel.sendEvent(new BindSheetEvent(this.sampleIndex, false));
              },
              // 设置表单弹回回调
              onWillSpringBackWhenDismiss: () => {
                // 记录弹回信息日志
                Logger.info(TAG,
                  `The springBack is not registered, causing the half-modal sheet to lack bounce-back behavior when pulled down.`);
              }
            })

        // 如果正在下载且当前索引匹配且下载进度大于等于0
        if (this.sampleCard.downloading && this.currentIndex === this.sampleIndex &&
          (this.sampleCard.downloadProgress >= 0)) {
          // 显示下载进度条
          Progress({
            value: SampleDetailConstant.PROGRESS_START,
            total: SampleDetailConstant.PROGRESS_FINISH,
            type: ProgressType.Capsule,
          })
            .value(this.sampleCard.downloadProgress)
            .layoutWeight(1)
            .borderRadius($r('sys.float.corner_radius_level7'))
            .height($r('app.float.progress_height'))
            .color($r('sys.color.background_emphasize'))
            .backgroundColor($r('sys.color.comp_background_primary_contrary'))
            .style({
              borderColor: $r('sys.color.background_emphasize'),
              borderWidth: $r('sys.float.border_small'),
              content: `${this.sampleCard.downloadProgress}%`,
              font: { size: $r('sys.float.Subtitle_S'), style: FontStyle.Normal, weight: FontWeight.Medium },
              fontColor: Color.Black,
            })
        } else {
          // 否则显示体验或下载按钮
          Button(this.sampleCard.installed ? $r('app.string.experience_sample') : $r('app.string.download_sample'))
            .cardButtonStyle($r('sys.color.background_emphasize'), $r('sys.color.font_on_primary'))
            .onClick(() => {
              // 发送加载示例事件
              this.viewModel.sendEvent(new LoadSampleEvent());
            })
        }
      }
      // 设置按钮行宽度为100%
      .width('100%')
      // 设置按钮行均匀分布
      .justifyContent(FlexAlign.SpaceAround)
    }
    // 设置主列边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 设置主列背景颜色
    .backgroundColor($r('sys.color.comp_background_primary'))
    // 设置主列高度
    .height($r('app.float.sample_card_height'))
    // 设置主列左对齐
    .alignItems(HorizontalAlign.Start)
    // 设置主列裁剪
    .clip(true)
    // 根据断点设置主列顶部边距
    .margin({
      top: new BreakpointType({
        sm: $r('app.float.margin_36'),
        md: $r('sys.float.padding_level12'),
        lg: $r('sys.float.padding_level12'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
    })
    // 根据断点设置主列内边距
    .padding({
      top: $r('sys.float.padding_level9'),
      bottom: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? $r('sys.float.padding_level12') :
      $r('sys.float.padding_level8'),
      left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? $r('sys.float.padding_level16') :
      $r('sys.float.padding_level8'),
      right: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? $r('sys.float.padding_level16') :
      $r('sys.float.padding_level8'),
    })
  }
}

// 使用Extend装饰器扩展Button组件的卡片按钮样式
@Extend(Button)
function cardButtonStyle(backgroundColor: ResourceColor, fontColor: ResourceColor) {
  // 设置按钮权重为1
  .layoutWeight(1)
  // 设置按钮字体大小
  .fontSize($r('sys.float.Subtitle_S'))
  // 设置按钮字体粗细
  .fontWeight(FontWeight.Medium)
  // 设置按钮字体颜色
  .fontColor(fontColor)
  // 设置按钮背景颜色
  .backgroundColor(backgroundColor)
  // 设置按钮控制大小
  .controlSize(ControlSize.NORMAL)
  // 设置按钮边框圆角
  .borderRadius($r('sys.float.corner_radius_level7'))
}
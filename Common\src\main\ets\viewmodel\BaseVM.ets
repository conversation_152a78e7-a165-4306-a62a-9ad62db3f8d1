// 导入基础状态类
import { BaseState } from './BaseState';
// 导入基础视图模型事件类
import { BaseVMEvent } from './BaseVMEvent';

/**
 * 基础视图模型抽象类
 * 实现MVVM架构中的ViewModel层
 * 管理状态和处理事件，连接View和Model
 * @template T 继承自BaseState的状态类型
 */
export abstract class BaseVM<T extends BaseState> {
  // 受保护的状态属性
  protected state: T;

  /**
   * 构造函数
   * 初始化视图模型的状态
   * @param initialState 初始状态对象
   */
  public constructor(initialState: T) {
    // 设置初始状态
    this.state = initialState;
  }

  /**
   * 获取状态方法
   * 返回当前的状态对象
   * @returns 当前状态
   */
  getState(): T {
    // 返回当前状态
    return this.state;
  }

  /**
   * 发送事件抽象方法
   * 处理来自视图的事件，由子类具体实现
   * @param baseVMEvent 基础视图模型事件
   */
  public abstract sendEvent(baseVMEvent: BaseVMEvent);
}
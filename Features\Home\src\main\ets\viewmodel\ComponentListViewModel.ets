// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入基础服务工具包中的业务错误类型定义
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型和响应数据类型定义
import type { GlobalInfoModel, ResponseData, } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举、通用常量、加载状态、日志记录器、页面上下文、请求错误代码、状态栏颜色类型和窗口工具
import {
  BreakpointType,
  BreakpointTypeEnum,
  CommonConstants,
  LoadingStatus,
  Logger,
  PageContext,
  RequestErrorCode,
  StatusBarColorType,
  WindowUtil,
} from '@ohos/common';
// 导入通用业务模块中的横幅数据类型定义
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的基础首页事件参数、基础首页事件类型、基础首页视图模型、组件详情参数、标签栏类型和标签内容状态
import {
  BaseHomeEventParam,
  BaseHomeEventType,
  BaseHomeViewModel,
  ComponentDetailParams,
  TabBarType,
  TAB_CONTENT_STATUSES,
} from '@ohos/commonbusiness';
// 导入组件数据相关类型定义
import type { ComponentContent, ComponentData } from '../model/ComponentData';
// 导入组件列表模型
import { ComponentListModel } from '../model/ComponentListModel';
// 导入组件列表状态
import { ComponentListState } from './ComponentListState';

// 定义标签常量
const TAG = '[ComponentListViewModel]';

// 定义组件列表视图模型类，继承基础首页视图模型
export class ComponentListViewModel extends BaseHomeViewModel<ComponentListState> {
  // 定义静态实例变量
  private static instance: ComponentListViewModel;
  // 定义私有全局信息模型，从应用存储获取
  private globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 定义私有组件列表模型实例
  private componentListModel: ComponentListModel = ComponentListModel.getInstance();
  // 定义私有横幅列分段选项
  private bannerColumnSection: SectionOptions = {
    itemsCount: 1,
    crossCount: 1,
  };
  // 定义私有组件列分段选项
  private componentColumnSection: SectionOptions = {
    itemsCount: 0,
    crossCount: new BreakpointType({
      sm: 1,
      md: 2,
      lg: 3,
    }).getValue(this.globalInfoModel.currentBreakpoint),
    margin: $r('sys.float.padding_level8'),
  };
  // 定义私有页脚分段选项
  private footerSection: SectionOptions = {
    itemsCount: 1,
    crossCount: 1,
  };

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数，传入新的组件列表状态
    super(new ComponentListState());
    // 设置状态顶部导航数据标题
    this.state.topNavigationData.title = $r('app.string.home_name');
  }

  // 定义获取实例的静态方法
  public static getInstance(): ComponentListViewModel {
    // 如果实例不存在则创建新实例
    if (!ComponentListViewModel.instance) {
      ComponentListViewModel.instance = new ComponentListViewModel();
    }
    // 返回实例
    return ComponentListViewModel.instance;
  }

  // 定义发送事件的公共方法
  public sendEvent<T>(eventParam: ComponentListEventParam<T>): void | boolean {
    // 获取事件类型
    const eventType: ComponentListEventType | BaseHomeEventType = eventParam.type;
    // 如果事件类型是加载组件页面
    if (eventType === ComponentListEventType.LOAD_COMPONENT_PAGE) {
      // 返回加载组件页面的结果
      return this.loadComponentPage();
    } else if (eventType === ComponentListEventType.JUMP_DETAIL_DETAIL) {
      // 如果事件类型是跳转详情，返回跳转组件详情的结果
      return this.jumpComponentDetail(eventParam.param as ComponentContent);
    } else if (eventType === ComponentListEventType.UPDATE_FLOW_SECTION) {
      // 如果事件类型是更新流分段，返回更新流分段的结果
      return this.updateFlowSection();
    } else {
      // 否则调用父类发送事件方法
      return super.sendEvent(eventParam as BaseHomeEventParam<T>);
    }
  }

  // 定义受保护的加载组件页面方法
  protected loadComponentPage(): void {
    // 判断是否为深色模式
    const isDark: boolean = AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    // 设置加载状态为正在加载
    this.state.loadingModel.loadingStatus = LoadingStatus.LOADING;
    // 根据深色模式设置标题颜色
    this.state.topNavigationData.titleColor = isDark ? StatusBarColorType.WHITE : StatusBarColorType.BLACK;
    // 设置导航不模糊
    this.state.topNavigationData.isBlur = false;
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(), isDark);
    // 获取组件页面数据
    this.componentListModel.getComponentPage(this.state.currentPage, this.pageSize)
      .then((result: ResponseData<ComponentData>) => {
        // 如果结果包含横幅信息
        if (result.data.bannerInfos) {
          // 遍历横幅信息设置标签视图类型
          result.data.bannerInfos.forEach((item: BannerData) => {
            item.tabViewType = TabBarType.HOME;
          });
          // 设置横幅状态数据数组
          this.state.bannerState.bannerResource.setDataArray([...result.data.bannerInfos]);
        }
        // 设置卡片数据源数组
        this.state.cardSource.setDataArray(result.data.cardData);
        // 更新流分段
        this.updateFlowSection();
        // 设置是否有下一页
        this.state.loadingModel.hasNextPage =
          result.data.cardData.length === this.pageSize && (result.totalSize > this.state.currentPage * this.pageSize);
        // 获取全局信息模型
        const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
        // 如果当前断点不是LG或XL
        if (globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.LG &&
          globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL) {
          // 设置标题颜色为白色
          this.state.topNavigationData.titleColor = StatusBarColorType.WHITE;
          // 更新状态栏颜色为深色
          WindowUtil.updateStatusBarColor(getContext(), true);
          // 设置标签内容状态
          TAB_CONTENT_STATUSES[TabBarType.HOME] = true;
        }
        // 设置加载状态为成功
        this.state.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
      })
      .catch((error: BusinessError) => {
        // 更新状态栏颜色
        WindowUtil.updateStatusBarColor(getContext(), isDark);
        // 设置标签内容状态
        TAB_CONTENT_STATUSES[TabBarType.HOME] = isDark;
        // 如果错误代码是网络连接失败
        if (error.code === RequestErrorCode.ERROR_NETWORK_CONNECT_FAILED) {
          // 设置加载状态为无网络
          this.state.loadingModel.loadingStatus = LoadingStatus.NO_NETWORK;
        } else {
          // 否则设置加载状态为失败
          this.state.loadingModel.loadingStatus = LoadingStatus.FAILED;
        }
      });
  }

  // 定义受保护的更新流分段方法
  protected updateFlowSection(): void {
    // 设置组件列分段项目数量
    this.componentColumnSection.itemsCount = this.state.cardSource.totalCount();
    // 根据断点类型获取交叉数量
    const crossCount: number = new BreakpointType({
      sm: 1,
      md: 2,
      lg: 3,
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 根据断点类型获取左边距
    const leftMargin: Length =
      new BreakpointType<Length>({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: CommonConstants.SPACE_32 + CommonConstants.TAB_BAR_WIDTH,
        xl: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint);
    // 根据断点类型获取右边距
    const rightMargin: Resource = new BreakpointType({
      sm: $r('sys.float.padding_level8'),
      md: $r('sys.float.padding_level12'),
      lg: $r('sys.float.padding_level16'),
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 根据断点类型获取列边距
    const columnMargin: Resource = new BreakpointType({
      sm: $r('sys.float.padding_level8'),
      md: $r('sys.float.padding_level10'),
      lg: $r('sys.float.padding_level12'),
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 定义边距对象
    const margin: Margin = {
      left: leftMargin,
      right: rightMargin,
      top: columnMargin,
      bottom: $r('sys.float.padding_level6'),
    };

    // 设置组件列分段交叉数量
    this.componentColumnSection.crossCount = crossCount;
    // 设置组件列分段边距
    this.componentColumnSection.margin = margin;
    // 设置页脚分段边距
    this.footerSection.margin = { left: leftMargin, right: rightMargin };
    // 替换状态分段数组
    this.state.sections.splice(0, this.state.sections.length(),
      [this.bannerColumnSection, this.componentColumnSection, this.footerSection]);
    // 记录调试日志
    Logger.debug(TAG, `updateFlowSection ${JSON.stringify(this.state.cardSource)}`);
  }

  // 定义受保护的跳转组件详情方法
  protected jumpComponentDetail(componentContent: ComponentContent) {
    // 根据断点类型获取页面上下文
    const pageContext: PageContext =
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('componentListPageContext')! :
        AppStorage.get('pageContext')!;
    // 定义组件详情参数
    const params: ComponentDetailParams = {
      componentName: componentContent.title,
      componentId: componentContent.id,
    };
    // 打开页面
    pageContext.openPage({
      routerName: 'ComponentDetailView',
      param: params,
    }, true);
  }
}

// 定义组件列表事件类型枚举
export enum ComponentListEventType {
  // 跳转详情视图
  JUMP_DETAIL_DETAIL = 'jumpDetailView',
  // 加载组件页面
  LOAD_COMPONENT_PAGE = 'loadComponentPage',
  // 更新流分段
  UPDATE_FLOW_SECTION = 'updateFlowSection',
}

// 定义组件列表事件参数接口
export interface ComponentListEventParam<T> {
  // 事件类型
  type: ComponentListEventType | BaseHomeEventType;
  // 参数
  param: T;
}
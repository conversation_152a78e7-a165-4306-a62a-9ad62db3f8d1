// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入文本选择器对话框相关的属性映射数据
import {
  canLoopMapData,
  itemHeightMapData,
  pickerDataCode,
} from '../entity/TextDialogAttributeMapping';

/**
 * 文本选择器对话框代码生成器类
 * 实现通用代码生成器接口，用于生成文本选择器对话框组件的代码
 */
export class TextPickerDialogCodeGenerator implements CommonCodeGenerator {
  // 私有循环属性，默认使用默认值
  private canLoop: string = canLoopMapData.get('Default')!.code;
  // 私有项目高度属性，默认使用默认值
  private itemHeight: string = itemHeightMapData.get('Default')!.code;

  /**
   * 生成文本选择器对话框组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的文本选择器对话框组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性进行处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        case 'canLoop':
          this.canLoop = attribute.currentValue;
          break;
        case 'itemHeight':
          this.itemHeight = attribute.currentValue;
          break;
        default:
          break;
      }
    });
    return `import { promptAction } from '@kit.ArkUI';

@Component
struct TextPickerComponent {
  build() {
    Column() {
      Button('文本选择器弹窗')
        .margin($r('sys.float.padding_level10'))
        .buttonStyle(ButtonStyleMode.NORMAL)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))
        .onClick(() => {
          TextPickerDialog.show({
            range: ${pickerDataCode},
            defaultPickerItemHeight: ${this.itemHeight},
            canLoop: ${this.canLoop},
            onAccept: (value: TextPickerResult) => {
              try {
                promptAction.showToast({
                  message: \`Select \${value.value}\`,
                  duration: 200
                });
              } catch (err) {
                const error: BusinessError = err as BusinessError;
                console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
              }
            },
            onCancel: () => {
              try {
                promptAction.showToast({
                  message: 'Canceled',
                  duration: 200
                });
              } catch (err) {
                const error: BusinessError = err as BusinessError;
                console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
              }
            }
          })
        })
    }
    .width('100%')
  }
}`;
  }
}
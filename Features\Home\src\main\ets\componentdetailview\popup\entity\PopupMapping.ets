/**
 * 弹窗位置类
 * 用于定义弹窗的位置信息，包含代码字符串和实际位置值
 */
class PlacementPosition {
  // 位置代码字符串，用于代码生成
  public code: string;
  // 位置枚举值，用于实际设置
  public value: Placement;

  /**
   * 构造函数
   * @param code 位置代码字符串
   * @param value 位置枚举值
   */
  constructor(code: string, value: Placement) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 弹窗位置映射数据
 * 将位置名称映射到具体的位置配置
 */
export const placementMapData: Map<string, PlacementPosition> = new Map([
  // 默认位置：底部
  ['Default', new PlacementPosition('Placement.Bottom', Placement.Bottom)],
  // 左侧位置
  ['Left', new PlacementPosition('Placement.Left', Placement.Left)],
  // 右侧位置
  ['Right', new PlacementPosition('Placement.Right', Placement.Right)],
  // 顶部位置
  ['Top', new PlacementPosition('Placement.Top', Placement.Top)],
  // 底部位置
  ['Bottom', new PlacementPosition('Placement.Bottom', Placement.Bottom)],
  // 左上角位置
  ['TopLeft', new PlacementPosition('Placement.TopLeft', Placement.TopLeft)],
  // 右上角位置
  ['TopRight', new PlacementPosition('Placement.TopRight', Placement.TopRight)],
  // 左下角位置
  ['BottomLeft', new PlacementPosition('Placement.BottomLeft', Placement.BottomLeft)],
  // 右下角位置
  ['BottomRight', new PlacementPosition('Placement.BottomRight', Placement.BottomRight)],
  // 左侧顶部位置
  ['LeftTop', new PlacementPosition('Placement.LeftTop', Placement.LeftTop)],
  // 左侧底部位置
  ['LeftBottom', new PlacementPosition('Placement.LeftBottom', Placement.LeftBottom)],
  // 右侧顶部位置
  ['RightTop', new PlacementPosition('Placement.RightTop', Placement.RightTop)],
  // 右侧底部位置
  ['RightBottom', new PlacementPosition('Placement.RightBottom', Placement.RightBottom)],
]);

/**
 * 弹窗样式枚举
 * 定义弹窗的不同样式类型
 */
export enum PopupStyle {
  // 带按钮的弹窗样式
  STYLE_BUTTON = 1,
  // 纯文本弹窗样式
  STYLE_TEXT = 2,
  // 带图标的弹窗样式
  STYLE_ICON = 3,
}

/**
 * 弹窗样式映射数据
 * 将样式名称映射到具体的弹窗样式枚举
 */
export const popupStyleMapData: Map<string, PopupStyle> = new Map([
  // 默认样式：带按钮的弹窗
  ['Default', PopupStyle.STYLE_BUTTON],
  // 按钮气泡样式
  ['按钮气泡', PopupStyle.STYLE_BUTTON],
  // 文字气泡样式
  ['文字气泡', PopupStyle.STYLE_TEXT],
  // 图文气泡样式
  ['图文气泡', PopupStyle.STYLE_ICON],
]);

/**
 * 弹窗样式代码映射数据
 * 将弹窗样式枚举映射到对应的代码字符串
 */
export const popupStyleCodeMapData: Map<PopupStyle, string> = new Map([
  // 带按钮弹窗的代码模板
  [PopupStyle.STYLE_BUTTON, `Popup({
        title: {
          text: '标题',
        },
        message: {
          text: '这是一个带按钮的气泡'
        },
        showClose: true,
        onClose: () => {
          this.handlePopup = false;
        },
        buttons: [
          {
            text: '知道了',
            action: () => {
              this.handlePopup = false;
            },
          },
        ]
      })`],
  // 纯文本弹窗的代码模板
  [PopupStyle.STYLE_TEXT, `Popup({
        message: {
          text: '这是一个文字气泡'
        },
        showClose: true,
        onClose: () => {
          this.handlePopup = false;
        }
      })`],
  // 带图标弹窗的代码模板
  [PopupStyle.STYLE_ICON, `Popup({
        icon: {
          // Replace the resource images under src/main/resources/base/media in your own project.
          image: $r('app.media.startIcon')
        },
        title: {
          text: '标题',
        },
        message: {
          text: '这是一个带图标的弹出气泡'
        },
        showClose: true,
        onClose: () => {
        this.handlePopup = false;
        }
      })`],
]);

/**
 * 弹窗按钮文本映射数据
 * 将弹窗样式枚举映射到对应的按钮文本资源
 */
export const popupBtnTextCodeMapData: Map<PopupStyle, Resource> = new Map([
  // 带按钮弹窗的按钮文本
  [PopupStyle.STYLE_BUTTON, $r('app.string.popup_button_button')],
  // 纯文本弹窗的按钮文本
  [PopupStyle.STYLE_TEXT, $r('app.string.popup_button_text')],
  // 带图标弹窗的按钮文本
  [PopupStyle.STYLE_ICON, $r('app.string.popup_button_icon')],
]);
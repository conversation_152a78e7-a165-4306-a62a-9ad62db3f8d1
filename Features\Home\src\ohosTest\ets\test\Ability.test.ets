// 导入性能分析工具包中的日志记录器
import { hilog } from '@kit.PerformanceAnalysisKit';
// 导入测试框架中的描述、前置、后置、测试用例和断言方法
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

// 定义默认导出的能力测试函数
export default function abilityTest() {
  // 描述能力测试套件
  describe('ActsAbilityTest', () => {
    // 在所有测试用例开始前执行一次的前置操作
    beforeAll(() => {
      // 预设操作，在测试套件的所有测试用例开始前只执行一次
      // 此API仅支持一个参数：预设操作函数
    })
    // 在每个单元测试用例开始前执行的前置操作
    beforeEach(() => {
      // 预设操作，在每个单元测试用例开始前执行
      // 执行次数与it定义的测试用例数量相同
      // 此API仅支持一个参数：预设操作函数
    })
    // 在每个单元测试用例结束后执行的清理操作
    afterEach(() => {
      // 预设清理操作，在每个单元测试用例结束后执行
      // 执行次数与it定义的测试用例数量相同
      // 此API仅支持一个参数：清理操作函数
    })
    // 在所有测试用例结束后执行的清理操作
    afterAll(() => {
      // 预设清理操作，在测试套件的所有测试用例结束后执行
      // 此API仅支持一个参数：清理操作函数
    })
    // 定义测试用例
    it('assertContain', 0, () => {
      // 定义测试用例，支持三个参数：测试用例名称、过滤参数和测试用例函数
      // 记录测试开始的日志信息
      hilog.info(0x0000, 'testTag', '%{public}s', 'it begin');
      // 定义变量a
      let a = 'abc';
      // 定义变量b
      let b = 'b';
      // 定义各种断言方法，用于声明预期的布尔条件
      expect(a).assertContain(b);
      // 断言a等于a
      expect(a).assertEqual(a);
    })
  })
}
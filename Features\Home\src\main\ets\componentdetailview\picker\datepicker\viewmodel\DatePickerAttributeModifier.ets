// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../../viewmodel/CommonAttributeModifier';
// 导入日期选择器描述器类型，用于描述组件配置
import type { DatePickerDescriptor } from './DatePickerDescriptor';

/**
 * 日期选择器属性修改器类
 * 继承通用属性修改器，专门用于修改日期选择器组件的属性
 * 支持农历显示等特定属性的动态修改
 */
@Observed
export class DatePickerAttributeModifier extends CommonAttributeModifier<DatePickerDescriptor, DatePickerAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性值应用到日期选择器实例上
   * @param instance 日期选择器属性实例
   */
  public applyNormalAttribute(instance: DatePickerAttribute): void {
    // 分配农历属性，从描述器获取农历配置并应用到实例
    this.assignAttribute((descriptor => descriptor.lunar), (val) => instance.lunar(val));
  }
}
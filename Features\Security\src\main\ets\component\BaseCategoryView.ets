// 导入通用模块中的空内容视图、加载失败视图、加载状态、加载视图和无网络视图
import { EmptyContentView, LoadingFailedView, LoadingStatus, LoadingView, NoNetworkView } from '@ohos/common';
// 导入通用模块中的全局信息模型和加载模型类型
import type { GlobalInfoModel, LoadingModel } from '@ohos/common';

// 使用Component装饰器定义基础分类视图组件
@Component
export struct BaseCategoryView {
  // 使用Prop和Require装饰器定义加载模型属性
  @Prop @Require loadingModel: LoadingModel;
  // 使用BuilderParam和Require装饰器定义内容视图构建参数
  @BuilderParam @Require contentView: () => void;
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 创建滚动控制器
  scroller: Scroller = new Scroller();
  // 定义重新加载数据函数
  reloadData?: Function;

  // 定义构建方法
  build() {
    // 创建滚动容器
    Scroll() {
      // 如果加载状态为成功
      if (this.loadingModel.loadingStatus === LoadingStatus.SUCCESS) {
        // 显示内容视图
        this.contentView()
      } else {
        // 创建列布局
        Column() {
          // 如果加载状态为失败
          if (this.loadingModel.loadingStatus === LoadingStatus.FAILED) {
            // 显示加载失败视图
            LoadingFailedView(this.globalInfoModel.currentBreakpoint, () => {
              // 调用重新加载数据函数
              this.reloadData?.();
            })
          } else if (this.loadingModel.loadingStatus === LoadingStatus.LOADING) {
            // 如果加载状态为加载中则显示加载视图
            LoadingView(this.globalInfoModel.currentBreakpoint)
          } else if (this.loadingModel.loadingStatus === LoadingStatus.NO_NETWORK) {
            // 如果加载状态为无网络则显示无网络视图
            NoNetworkView(this.globalInfoModel.currentBreakpoint, () => {
              // 调用重新加载数据函数
              this.reloadData?.();
            })
          } else {
            // 否则显示空内容视图
            EmptyContentView($r('app.media.ic_browse_no'), $r('app.string.no_content'))
          }
        }
        // 设置列宽度为100%
        .width('100%')
        // 设置列高度
        .height($r('app.float.loading_view_height'))
      }
    }
    // 设置滚动容器对齐方式为顶部对齐
    .align(Alignment.Top)
    // 设置滚动条为隐藏
    .scrollBar(BarState.Off)
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 设置高度为100%
    .height('100%')
    // 设置宽度为100%
    .width('100%')
    // 设置嵌套滚动模式
    .nestedScroll({
      scrollForward: NestedScrollMode.PARENT_FIRST,
      scrollBackward: NestedScrollMode.SELF_FIRST,
    })
  }
}
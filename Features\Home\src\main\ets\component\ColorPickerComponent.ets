// 导入通用模块中的颜色选择器工具类
import { ColorPickerUtil } from '@ohos/common';
// 导入颜色选择器属性类型定义
import type { ColorPickerAttribute } from '../viewmodel/ComponentDetailState';

// 颜色选择器组件装饰器
@Component
// 导出颜色选择器组件结构体
export struct ColorPickerComponent {
  // 颜色选择器属性对象，使用ObjectLink装饰器进行双向绑定
  @ObjectLink attribute: ColorPickerAttribute;
  // 颜色值变更回调函数，接收属性名和颜色值参数
  callback: (name: string, value: string) => void = (name: string, value: string) => {
    // 空实现，由外部传入具体的回调逻辑
  };

  // 构建方法，定义组件的UI结构
  build() {
    // 创建列布局容器
    Column() {
      // 创建文本组件显示属性名称
      Text(this.attribute.disPlayName)
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统中等副标题大小
        .fontSize($r('sys.float.Subtitle_M'))
        // 设置字体颜色为系统主要字体颜色
        .fontColor($r('sys.color.font_primary'))
        // 设置文本边距
        .margin({
          // 设置顶部边距
          top: $r('sys.float.padding_level8'),
          // 设置底部边距
          bottom: $r('sys.float.padding_level8'),
          // 设置左侧边距
          left: $r('sys.float.padding_level6')
        })

      // 创建滑块组件用于颜色选择
      Slider({
        // 根据当前RGB值计算滑块位置
        value: ColorPickerUtil.getColorFromRgb(this.attribute.currentValue),
        // 设置滑块样式为内嵌式
        style: SliderStyle.InSet,
      })
        // 设置滑块值变更事件处理器
        .onChange((value: number, mode: SliderChangeMode) => {
          // 根据滑块值获取对应的颜色值
          const curentValue: string = ColorPickerUtil.getBlockColor(value);
          // 检查颜色值是否发生变化
          if (this.attribute.currentValue !== String(curentValue)) {
            // 更新属性的当前颜色值
            this.attribute.currentValue = curentValue;
            // 触发颜色变更回调函数
            this.callback(this.attribute.name, curentValue);
          }
        })
        // 设置选中状态的颜色为透明
        .selectedColor(Color.Transparent)
        // 设置轨道颜色为线性渐变的彩虹色
        .trackColor(new LinearGradient([
          // 红色起点，偏移量为0
          { color: ColorPickerUtil.setRgba(255, 0, 0, 1.00), offset: 0 },
          // 黄色，偏移量为1/6
          { color: ColorPickerUtil.setRgba(255, 255, 0, 1.00), offset: 1 / 6 },
          // 绿色，偏移量为2/6
          { color: ColorPickerUtil.setRgba(0, 255, 0, 1.00), offset: 2 / 6 },
          // 青色，偏移量为3/6
          { color: ColorPickerUtil.setRgba(8, 255, 255, 1.00), offset: 3 / 6 },
          // 蓝色，偏移量为4/6
          { color: ColorPickerUtil.setRgba(0, 0, 255, 1.00), offset: 4 / 6 },
          // 品红色，偏移量为5/6
          { color: ColorPickerUtil.setRgba(255, 0, 255, 1.00), offset: 5 / 6 },
          // 红色终点，偏移量为1
          { color: ColorPickerUtil.setRgba(255, 0, 0, 1.00), offset: 1 },
        ]))
        // 设置滑块交互模式为滑动和点击抬起
        .sliderInteractionMode(SliderInteraction.SLIDE_AND_CLICK_UP)
        // 设置轨道厚度为大尺寸
        .trackThickness($r('app.float.slider_track_thick_large'))
        // 设置滑块边框颜色为白色
        .blockBorderColor(Color.White)
        // 设置滑块边框宽度
        .blockBorderWidth($r('app.float.slider_block_border_size'))
        // 设置滑块大小
        .blockSize({
          // 设置滑块宽度
          width: $r('app.float.slider_block_size_large'),
          // 设置滑块高度
          height: $r('app.float.slider_block_size_large'),
        })
        // 设置滑块颜色为透明
        .blockColor(Color.Transparent)
        // 设置滑块组件高度
        .height($r('app.float.common_component_height'))
        // 设置滑块组件宽度为100%
        .width('100%')
    }
    // 设置列容器宽度为100%
    .width('100%')
    // 设置列容器内子元素水平对齐方式为左对齐
    .alignItems(HorizontalAlign.Start)
    // 设置列容器内边距
    .padding({ left: $r('sys.float.padding_level4'), right: $r('sys.float.padding_level4') })
  }
}
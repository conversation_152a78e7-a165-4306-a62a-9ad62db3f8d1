// 导入ArkUI工具包中的弹窗组件，用于显示提示信息
import { Popup } from '@kit.ArkUI';
// 导入图像工具包，用于处理图像相关操作
import { image } from '@kit.ImageKit';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的断点类型、全局信息模型、日志记录器和偏好设置管理器
import { BreakpointTypeEnum, GlobalInfoModel, Logger, PreferenceManager } from '@ohos/common';
// 导入详情页面常量，用于获取AI图像组件的配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入通用存储键，用于偏好设置的键值管理
import { CommonStorageKey } from '../../common/entity/CommonStorageKey';

// 日志标签常量，用于标识AI图像组件的日志输出
const TAG: string = '[AIImageComponent]';

/**
 * AI图像组件
 * 实现图像AI分析功能，支持图像抠图和智能分析
 */
@Component
export struct AIImageComponent {
  // 全局信息模型，从应用存储中获取，用于响应式布局
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 图像像素图状态，存储当前显示的图像数据
  @State imagePixelMap?: image.PixelMap = undefined;
  // 弹窗显示状态，控制AI抠图提示弹窗的显示
  @State showPopup: boolean = true;
  // 图像宽度状态，记录图像的实际显示宽度
  @State imageWidth: number = 0;
  // 图像高度状态，记录图像的实际显示高度
  @State imageHeight: number = 0;

  /**
   * 组件即将出现时的生命周期方法
   * 初始化弹窗显示状态和图像像素图
   */
  async aboutToAppear() {
    // 从偏好设置中获取抠图提示的显示状态
    PreferenceManager.getInstance().getValue<boolean>(CommonStorageKey.KEY_MATTING_TIP).then((value) => {
      // 如果没有设置过，默认显示提示
      this.showPopup = value ?? true;
    });
    // 从媒体资源中获取AI图像的像素图
    this.imagePixelMap = await this.getPixmapFromMedia($r('app.media.image_ai'));
  }

  /**
   * 组件即将消失时的生命周期方法
   * 释放图像像素图资源，防止内存泄漏
   */
  aboutToDisappear(): void {
    // 释放像素图资源
    this.imagePixelMap?.release();
  }

  /**
   * 自定义弹窗构建器
   * 构建AI抠图功能的提示弹窗
   */
  @Builder
  MyPopup() {
    Row() {
      // 创建弹窗组件
      Popup({
        // 弹窗标题
        title: {
          text: $r('app.string.aiMatting'),
        },
        // 弹窗消息内容
        message: {
          text: $r('app.string.aiMatting_tip')
        },
        // 显示关闭按钮
        showClose: true,
        // 关闭按钮点击事件
        onClose: () => {
          this.showPopup = false;
        },
        // 弹窗按钮配置
        buttons: [
          {
            // 确认按钮
            text: $r('app.string.confirmTip'),
            action: () => {
              // 关闭弹窗
              this.showPopup = false;
            },
          },
          {
            // 取消按钮
            text: $r('app.string.cancelTip'),
            action: () => {
              // 设置不再显示提示，并关闭弹窗
              PreferenceManager.getInstance().setValue(CommonStorageKey.KEY_MATTING_TIP, false);
              this.showPopup = false;
            },
          }
        ],
      })
    }
    // 处理键盘预输入事件
    .onKeyPreIme((keyEvent: KeyEvent) => {
      // 处理方向键左右键事件
      if ((keyEvent?.keyText === 'KEYCODE_DPAD_RIGHT' || keyEvent?.keyText === 'KEYCODE_DPAD_LEFT') &&
        keyEvent.type === KeyType.Down) {
        return true;
      }
      return false;
    })
  }

  /**
   * 构建组件UI方法
   * 创建包含AI图像分析功能的界面布局
   */
  build() {
    // 使用Stack布局，内容居中对齐
    Stack({ alignContent: Alignment.Center }) {
      // 创建图像组件
      Image(this.imagePixelMap ?? $r('app.media.image_ai'))
        // 启用AI分析器功能
        .enableAnalyzer(true)
        // 根据断点类型设置宽度
        .width(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? '100%' : '90%')
        // 根据断点类型设置高度
        .height(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? '100%' : this.imageHeight)
        // 根据断点类型设置图像适配方式
        .objectFit(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? ImageFit.Fill : ImageFit.Contain)
        // 设置圆角边框
        .borderRadius($r('sys.float.corner_radius_level8'))
        // 禁用拖拽功能
        .draggable(false)
        // 监听区域变化事件，更新图像尺寸
        .onAreaChange((_oldValue: Area, newValue: Area) => {
          // 更新图像宽度
          this.imageWidth = newValue.width as number;
          // 按比例计算图像高度
          this.imageHeight = this.imageWidth * 0.56 as number;
        })
        // 绑定弹窗组件
        .bindPopup(this.showPopup, {
          // 弹窗构建器
          builder: this.MyPopup(),
          // 弹窗宽度
          width: $r('app.float.popup_width_large'),
          // 背景模糊样式
          backgroundBlurStyle: BlurStyle.COMPONENT_ULTRA_THICK,
          // 弹窗圆角半径
          radius: ($r('sys.float.corner_radius_level4')),
          // 弹窗偏移量
          offset: { y: DetailPageConstant.IMAGE_POPUP_OFFSET_Y },
          // 弹窗位置
          placement: Placement.Bottom,
          // 在子窗口中显示
          showInSubWindow: true,
          // 弹窗状态变化回调
          onStateChange: (event) => {
            // 当弹窗不可见时，更新状态
            if (!event.isVisible) {
              this.showPopup = false;
            }
          },
          // 弹窗可获取焦点
          focusable: true,
        })
    }
    // 设置Stack容器的宽度和高度
    .width('100%')
    .height('100%')
  }

  /**
   * 从媒体资源获取像素图的私有方法
   * 将资源文件转换为可用于图像分析的像素图对象
   * @param resource 媒体资源对象，包含图像文件信息
   * @returns 返回创建的像素图对象，失败时返回undefined
   */
  private async getPixmapFromMedia(resource: Resource) {
    // 声明像素图变量
    let createPixelMap: image.PixelMap;
    try {
      // 从资源管理器获取媒体内容的字节数组
      const unit8Array = await getContext(this)?.resourceManager?.getMediaContent({
        bundleName: resource.bundleName,
        moduleName: resource.moduleName,
        id: resource.id,
      });
      // 创建图像源对象
      const imageSource = image.createImageSource(unit8Array.buffer.slice(0, unit8Array.buffer.byteLength));
      // 创建像素图，指定RGBA_8888格式
      createPixelMap = await imageSource.createPixelMap({
        desiredPixelFormat: image.PixelMapFormat.RGBA_8888,
      });
      // 释放图像源资源
      await imageSource.release();
      // 释放之前的像素图资源
      this.imagePixelMap?.release();
    } catch (err) {
      // 捕获并处理错误
      const error: BusinessError = err as BusinessError;
      // 记录错误日志
      Logger.error(TAG, `Get pixmapFromMedia error, the code is ${error.code}, the message is ${error.message}`);
      return;
    }
    // 返回创建的像素图
    return createPixelMap;
  }
}
// 导入通用数字映射和字符串映射类
import { CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

/**
 * 行布局对齐方式映射类
 * 用于定义行布局垂直对齐的代码字符串和实际枚举值
 */
class RowAlignMapping {
  // 对齐方式代码字符串，用于代码生成
  public readonly code: string;
  // 对齐方式枚举值，用于实际设置
  public readonly value: VerticalAlign;

  /**
   * 构造函数
   * @param code 对齐方式代码字符串
   * @param value 对齐方式枚举值
   */
  constructor(code: string, value: VerticalAlign) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 行布局垂直对齐映射数据
 * 将对齐方式名称映射到具体的垂直对齐配置
 */
export const rowAlignMapData: Map<string, RowAlignMapping> = new Map([
  // 顶部对齐
  ['Top', new RowAlignMapping('VerticalAlign.Top', VerticalAlign.Top)],
  // 居中对齐
  ['Center', new RowAlignMapping('VerticalAlign.Center', VerticalAlign.Center)],
  // 底部对齐
  ['Bottom', new RowAlignMapping('VerticalAlign.Bottom', VerticalAlign.Bottom)],
  // 默认为居中对齐
  ['Default', new RowAlignMapping('VerticalAlign.Center', VerticalAlign.Center)],
]);

/**
 * 行布局主轴对齐方式映射类
 * 用于定义行布局主轴对齐的代码字符串和实际枚举值
 */
class RowJustifyContentMapping {
  // 主轴对齐方式代码字符串，用于代码生成
  public readonly code: string;
  // 主轴对齐方式枚举值，用于实际设置
  public readonly value: FlexAlign;

  /**
   * 构造函数
   * @param code 主轴对齐方式代码字符串
   * @param value 主轴对齐方式枚举值
   */
  constructor(code: string, value: FlexAlign) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 行布局主轴对齐映射数据
 * 将对齐方式名称映射到具体的主轴对齐配置
 */
export const rowJustifyContentMapData: Map<string, RowJustifyContentMapping> = new Map([
  // 起始对齐
  ['Start', new RowJustifyContentMapping('FlexAlign.Start', FlexAlign.Start)],
  // 居中对齐
  ['Center', new RowJustifyContentMapping('FlexAlign.Center', FlexAlign.Center)],
  // 末尾对齐
  ['End', new RowJustifyContentMapping('FlexAlign.End', FlexAlign.End)],
  // 两端对齐
  ['SpaceBetween', new RowJustifyContentMapping('FlexAlign.SpaceBetween', FlexAlign.SpaceBetween)],
  // 环绕对齐
  ['SpaceAround', new RowJustifyContentMapping('FlexAlign.SpaceAround', FlexAlign.SpaceAround)],
  // 均匀分布
  ['SpaceEvenly', new RowJustifyContentMapping('FlexAlign.SpaceEvenly', FlexAlign.SpaceEvenly)],
  // 默认为起始对齐
  ['Default', new RowJustifyContentMapping('FlexAlign.Start', FlexAlign.Start)],
]);

/**
 * 行布局间距映射数据
 * 定义行布局子组件间的默认间距配置
 */
export const rowSpaceMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认间距为3
  ['Default', new CommonNumberMapping('3', 3)],
]);

/**
 * 行布局内边距类型映射数据
 * 定义行布局的内边距类型配置
 */
export const rowPaddingMapData: Map<string, CommonStringMapping> = new Map([
  // 垂直内边距
  ['Vertical', new CommonStringMapping('Vertical', 'Vertical')],
  // 水平内边距
  ['Horizontal', new CommonStringMapping('Horizontal', 'Horizontal')],
  // 全方向内边距
  ['All', new CommonStringMapping('All', 'All')],
  // 默认为全方向内边距
  ['Default', new CommonStringMapping('All', 'All')],
]);

/**
 * 内边距数值映射数据
 * 定义行布局的默认内边距数值配置
 */
export const paddingNumMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认内边距数值为3
  ['Default', new CommonNumberMapping('3', 3)],
]);
// 导入断点类型枚举
import { BreakpointTypeEnum } from '../model/GlobalInfoModel';
// 导入断点类型工具类
import { BreakpointType } from '../util/BreakpointSystem';

/**
 * 加载视图构建函数
 * 显示加载状态页面，包含加载进度指示器和加载文本
 * 支持响应式设计，根据断点调整加载指示器大小
 * @param breakpoint 当前断点类型
 */
@Builder
export function LoadingView(breakpoint: BreakpointTypeEnum) {
  // 垂直布局容器
  Column() {
    // 水平布局容器，用于包装加载进度指示器
    Row() {
      // 加载进度指示器组件
      LoadingProgress()
    }
    // 根据断点设置不同的宽度
    .width(new BreakpointType({
      // 小屏幕断点使用小尺寸
      sm: $r('app.float.loading_size_sm'),
      // 中屏幕断点使用中等尺寸
      md: $r('app.float.loading_size_md'),
      // 大屏幕断点使用中等尺寸
      lg: $r('app.float.loading_size_md'),
    }).getValue(breakpoint))
    // 设置宽高比为1:1，保持正方形
    .aspectRatio(1)

    // 加载文本
    Text($r('app.string.loading'))
      // 设置字体大小为Body_M
      .fontSize($r('sys.float.Body_M'))
      // 设置字体颜色为系统次要字体颜色
      .fontColor($r('sys.color.font_secondary'))
      // 设置外边距为12级内边距
      .margin($r('sys.float.padding_level12'))
  }
  // 设置子组件水平居中对齐
  .alignItems(HorizontalAlign.Center)
  // 设置子组件垂直居中对齐
  .justifyContent(FlexAlign.Center)
  // 设置背景颜色为系统次要背景色
  .backgroundColor($r('sys.color.background_secondary'))
  // 设置高度为100%
  .height('100%')
  // 设置宽度为100%
  .width('100%')
}
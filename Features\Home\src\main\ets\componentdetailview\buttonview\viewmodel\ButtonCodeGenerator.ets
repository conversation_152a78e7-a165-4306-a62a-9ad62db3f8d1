// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入按钮相关的映射数据，包含各种属性的映射关系
import {
  buttonActionMap,
  buttonBgColorMap,
  buttonTypeMapData,
  sizeMapData,
  styleMapData,
} from '../entity/ButtonAttributeMapping';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 按钮代码生成器类
 * 实现通用代码生成器接口，用于生成按钮组件的代码
 */
export class ButtonCodeGenerator implements CommonCodeGenerator {
  // 按钮样式代码字符串，默认使用映射数据中的默认值
  private buttonStyle: string = styleMapData.get('Default')!.code;
  // 控件尺寸代码字符串，默认使用映射数据中的默认值
  private controlSize: string = sizeMapData.get('Default')!.code;
  // 按钮类型代码字符串，默认使用映射数据中的默认值
  private buttonType: string = buttonTypeMapData.get('Default')!.code;
  // 背景颜色代码字符串，默认使用映射数据中的默认值
  private backgroundColor: string = buttonBgColorMap.get('Default')!.code;
  // 操作类型代码字符串，默认使用映射数据中的默认值
  private operation: string = buttonActionMap.get('Default')!.code;

  /**
   * 生成按钮组件代码的方法
   * 根据传入的属性数组生成完整的按钮组件代码字符串
   * @param attributes 原始属性数组，包含组件的各种配置属性
   * @returns 生成的按钮组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理按钮样式属性
        case 'buttonStyle':
          // 从映射数据中获取对应的样式代码，如果不存在则使用默认值
          this.buttonStyle = styleMapData.get(attribute.currentValue)?.code ?? styleMapData.get('Default')!.code;
          break;
        // 处理控件尺寸属性
        case 'controlSize':
          // 从映射数据中获取对应的尺寸代码，如果不存在则使用默认值
          this.controlSize = sizeMapData.get(attribute.currentValue)?.code ?? sizeMapData.get('Default')!.code;
          break;
        // 处理按钮类型属性
        case 'buttonType':
          // 从映射数据中获取对应的类型代码，如果不存在则使用默认值
          this.buttonType =
            buttonTypeMapData.get(attribute.currentValue)?.code ?? buttonTypeMapData.get('Default')!.code;
          break;
        // 处理背景颜色属性
        case 'backgroundColor':
          // 先获取原始颜色值
          this.backgroundColor = attribute.currentValue;
          // 根据按钮样式决定背景颜色的处理方式
          if (this.buttonStyle === styleMapData.get('Emphasized')!.code) {
            // 强调样式使用自定义颜色
            this.backgroundColor = `'${attribute.currentValue}'`;
          } else if (this.buttonStyle === styleMapData.get('Normal')!.code) {
            // 普通样式使用系统颜色
            this.backgroundColor = `$r('sys.color.comp_background_tertiary')`;
          } else {
            // 文本样式使用透明背景
            this.backgroundColor = `Color.Transparent`;
          }
          break;
        // 处理操作类型属性
        case 'operation':
          // 直接使用属性的当前值
          this.operation = attribute.currentValue;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 初始化操作代码字符串
    let operationCode: string = '';
    // 根据操作类型生成对应的事件处理代码
    if (this.operation === 'LongGesture') {
      // 生成长按手势的代码
      operationCode = `.gesture(
        LongPressGesture({ repeat: true })
          .onActionEnd((event: GestureEvent) => {
            try {
              promptAction.showToast({
                message: '按钮被长按',
                duration: 2000
              });
            } catch (err) {
              const error: BusinessError = err as BusinessError;
              console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
            }
          })
      )`;
    } else if (this.operation === 'Click') {
      // 生成点击事件的代码
      operationCode = `.onClick(() => {
        try {
          promptAction.showToast({
            message: '按钮被点击',
            duration: 2000
          });
        } catch (err) {
          const error: BusinessError = err as BusinessError;
          console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
        }
      })`;
    } else {
      // 无操作时使用空字符串
      operationCode = ``;
    }
    // 返回生成的完整按钮组件代码字符串
    return `import { promptAction } from '@kit.ArkUI';

@Component
struct ButtonComponent {
  build() {
    Button('按钮示例', { type: ${this.buttonType} })
      .buttonStyle(${this.buttonStyle})
      .controlSize(${this.controlSize})
      .backgroundColor(${this.backgroundColor})
      ${operationCode}
  }
}`;
  }
}
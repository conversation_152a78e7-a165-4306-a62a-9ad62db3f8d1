/**
 * 全局上下文类
 * 使用单例模式管理全局对象存储
 * 提供键值对形式的对象存储和检索功能
 */
export class GlobalContext {
  // 单例实例
  private static instance: GlobalContext;
  // 对象存储映射表
  private _objects: Map<string, Object>;

  /**
   * 私有构造函数
   * 初始化对象存储映射表
   */
  private constructor() {
    // 创建对象存储映射表
    this._objects = new Map<string, Object>();
  }

  /**
   * 获取上下文实例方法
   * 单例模式获取全局上下文实例
   * @returns 全局上下文实例
   */
  public static getContext(): GlobalContext {
    // 如果实例不存在
    if (!GlobalContext.instance) {
      // 创建新实例
      GlobalContext.instance = new GlobalContext();
    }
    // 返回单例实例
    return GlobalContext.instance;
  }

  /**
   * 获取对象方法
   * 根据键名获取存储的对象
   * @param key 对象键名
   * @returns 存储的对象或undefined
   */
  public getObject(key: string): Object | undefined {
    // 从映射表中获取对象
    return this._objects.get(key);
  }

  /**
   * 设置对象方法
   * 将对象存储到映射表中
   * @param key 对象键名
   * @param objectClass 要存储的对象
   */
  public setObject(key: string, objectClass: Object): void {
    // 将对象存储到映射表
    this._objects.set(key, objectClass);
  }

  /**
   * 删除对象方法
   * 从映射表中删除指定键名的对象
   * @param key 对象键名
   */
  public deleteObject(key: string): void {
    // 从映射表中删除对象
    this._objects.delete(key);
  }
}
// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入文本区域相关的属性映射数据
import { textAlignTypeMapData, textOverflowTypeMapData } from '../entity/TextAreaAttributeMapping';

/**
 * 文本区域描述器类
 * 继承自通用描述器，用于描述文本区域组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TextAreaDescriptor extends CommonDescriptor {
  // 最大行数属性，默认为2行
  public maxLines: number = 2;
  // 行间距属性，默认为5像素
  public lineSpacing: number = 5;
  // 文本溢出类型数据，默认使用默认值
  public textOverflowTypeData: TextOverflow = textOverflowTypeMapData.get('Default')!.value;
  // 文本对齐属性，默认使用默认值
  public textAlign: TextAlign = textAlignTypeMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性转换为描述器属性
   * @param attributes 原始属性数组，包含需要转换的属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性进行转换
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的转换处理
      switch (attribute.name) {
        // 处理最大行数属性
        case 'maxLines':
          this.maxLines = Number(attribute.currentValue);
          break;
        case 'lineSpacing':
          this.lineSpacing = Number(attribute.currentValue);
          break;
        case 'textOverflowType':
          this.textOverflowTypeData =
            textOverflowTypeMapData.get(attribute.currentValue)?.value ?? textOverflowTypeMapData.get('Default')!.value;
          break;
        case 'textAlign':
          this.textAlign =
            textAlignTypeMapData.get(attribute.currentValue)?.value ?? textAlignTypeMapData.get('Default')!.value;
          break;
        default:
          break;
      }
    });
  }
}
import{H as Qt}from"../../../dist/common.js";import"../../../dist/common2.js";const St={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},te='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>',ht=typeof window<"u"&&typeof window.document<"u",M=ht?window:{},Y=ht&&M.document.documentElement?"ontouchstart"in M.document.documentElement:!1,bt=ht?"PointerEvent"in M:!1,f="viewer",ot="move",Kt="switch",K="zoom",et=`${f}-active`,ee=`${f}-close`,at=`${f}-fade`,mt=`${f}-fixed`,ie=`${f}-fullscreen`,It=`${f}-fullscreen-exit`,F=`${f}-hide`,se=`${f}-hide-md-down`,ne=`${f}-hide-sm-down`,oe=`${f}-hide-xs-down`,D=`${f}-in`,G=`${f}-invisible`,U=`${f}-loading`,ae=`${f}-move`,Nt=`${f}-open`,H=`${f}-show`,v=`${f}-transition`,Z="click",gt="dblclick",At="dragstart",Ct="focusin",xt="keydown",k="load",W="error",re=Y?"touchend touchcancel":"mouseup",le=Y?"touchmove":"mousemove",he=Y?"touchstart":"mousedown",Dt=bt?"pointerdown":he,kt=bt?"pointermove":le,Lt=bt?"pointerup pointercancel":re,zt="resize",z="transitionend",Ot="wheel",Rt="ready",_t="show",Mt="shown",Vt="hide",$t="hidden",Ft="view",J="viewed",Wt="move",Ht="moved",Pt="rotate",qt="rotated",Xt="scale",Bt="scaled",Yt="zoom",Ut="zoomed",Zt="play",jt="stop",lt=`${f}Action`,wt=/\s\s*/,it=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function Q(t){return typeof t=="string"}const ce=Number.isNaN||M.isNaN;function y(t){return typeof t=="number"&&!ce(t)}function q(t){return typeof t>"u"}function j(t){return typeof t=="object"&&t!==null}const{hasOwnProperty:de}=Object.prototype;function X(t){if(!j(t))return!1;try{const{constructor:e}=t,{prototype:i}=e;return e&&i&&de.call(i,"isPrototypeOf")}catch{return!1}}function p(t){return typeof t=="function"}function w(t,e){if(t&&p(e))if(Array.isArray(t)||y(t.length)){const{length:i}=t;let s;for(s=0;s<i&&e.call(t,t[s],s,t)!==!1;s+=1);}else j(t)&&Object.keys(t).forEach(i=>{e.call(t,t[i],i,t)});return t}const C=Object.assign||function(e,...i){return j(e)&&i.length>0&&i.forEach(s=>{j(s)&&Object.keys(s).forEach(n=>{e[n]=s[n]})}),e},ue=/^(?:width|height|left|top|marginLeft|marginTop)$/;function O(t,e){const{style:i}=t;w(e,(s,n)=>{ue.test(n)&&y(s)&&(s+="px"),i[n]=s})}function fe(t){return Q(t)?t.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):t}function P(t,e){return!t||!e?!1:t.classList?t.classList.contains(e):t.className.indexOf(e)>-1}function c(t,e){if(!t||!e)return;if(y(t.length)){w(t,s=>{c(s,e)});return}if(t.classList){t.classList.add(e);return}const i=t.className.trim();i?i.indexOf(e)<0&&(t.className=`${i} ${e}`):t.className=e}function g(t,e){if(!(!t||!e)){if(y(t.length)){w(t,i=>{g(i,e)});return}if(t.classList){t.classList.remove(e);return}t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,""))}}function tt(t,e,i){if(e){if(y(t.length)){w(t,s=>{tt(s,e,i)});return}i?c(t,e):g(t,e)}}const me=/([a-z\d])([A-Z])/g;function Et(t){return t.replace(me,"$1-$2").toLowerCase()}function B(t,e){return j(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute(`data-${Et(e)}`)}function pt(t,e,i){j(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute(`data-${Et(e)}`,i)}const Gt=(()=>{let t=!1;if(ht){let e=!1;const i=()=>{},s=Object.defineProperty({},"once",{get(){return t=!0,e},set(n){e=n}});M.addEventListener("test",i,s),M.removeEventListener("test",i,s)}return t})();function b(t,e,i,s={}){let n=i;e.trim().split(wt).forEach(o=>{if(!Gt){const{listeners:a}=t;a&&a[o]&&a[o][i]&&(n=a[o][i],delete a[o][i],Object.keys(a[o]).length===0&&delete a[o],Object.keys(a).length===0&&delete t.listeners)}t.removeEventListener(o,n,s)})}function d(t,e,i,s={}){let n=i;e.trim().split(wt).forEach(o=>{if(s.once&&!Gt){const{listeners:a={}}=t;n=(...r)=>{delete a[o][i],t.removeEventListener(o,n,s),i.apply(t,r)},a[o]||(a[o]={}),a[o][i]&&t.removeEventListener(o,a[o][i],s),a[o][i]=n,t.listeners=a}t.addEventListener(o,n,s)})}function T(t,e,i,s){let n;return p(Event)&&p(CustomEvent)?n=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:i,...s}):(n=document.createEvent("CustomEvent"),n.initCustomEvent(e,!0,!0,i)),t.dispatchEvent(n)}function ge(t){const e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}function rt({rotate:t,scaleX:e,scaleY:i,translateX:s,translateY:n}){const o=[];y(s)&&s!==0&&o.push(`translateX(${s}px)`),y(n)&&n!==0&&o.push(`translateY(${n}px)`),y(t)&&t!==0&&o.push(`rotate(${t}deg)`),y(e)&&e!==1&&o.push(`scaleX(${e})`),y(i)&&i!==1&&o.push(`scaleY(${i})`);const a=o.length?o.join(" "):"none";return{WebkitTransform:a,msTransform:a,transform:a}}function pe(t){return Q(t)?decodeURIComponent(t.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}const ft=M.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(M.navigator.userAgent);function Jt(t,e,i){const s=document.createElement("img");if(t.naturalWidth&&!ft)return i(t.naturalWidth,t.naturalHeight),s;const n=document.body||document.documentElement;return s.onload=()=>{i(s.width,s.height),ft||n.removeChild(s)},w(e.inheritedAttributes,o=>{const a=t.getAttribute(o);a!==null&&s.setAttribute(o,a)}),s.src=t.src,ft||(s.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(s)),s}function st(t){switch(t){case 2:return oe;case 3:return ne;case 4:return se;default:return""}}function be(t){const e={...t},i=[];return w(t,(s,n)=>{delete e[n],w(e,o=>{const a=Math.abs(s.startX-o.startX),r=Math.abs(s.startY-o.startY),l=Math.abs(s.endX-o.endX),h=Math.abs(s.endY-o.endY),m=Math.sqrt(a*a+r*r),E=(Math.sqrt(l*l+h*h)-m)/m;i.push(E)})}),i.sort((s,n)=>Math.abs(s)<Math.abs(n)),i[0]}function nt({pageX:t,pageY:e},i){const s={endX:t,endY:e};return i?s:{timeStamp:Date.now(),startX:t,startY:e,...s}}function we(t){let e=0,i=0,s=0;return w(t,({startX:n,startY:o})=>{e+=n,i+=o,s+=1}),e/=s,i/=s,{pageX:e,pageY:i}}const Ee={render(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody(){const{ownerDocument:t}=this.element,e=t.body||t.documentElement;this.body=e,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=e.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(e).paddingRight},initContainer(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer(){const{options:t,parent:e}=this;let i;t.inline&&(i={width:Math.max(e.offsetWidth,t.minWidth),height:Math.max(e.offsetHeight,t.minHeight)},this.parentData=i),(this.fulled||!i)&&(i=this.containerData),this.viewerData=C({},i)},renderViewer(){this.options.inline&&!this.fulled&&O(this.viewer,this.viewerData)},initList(){const{element:t,options:e,list:i}=this,s=[];i.innerHTML="",w(this.images,(n,o)=>{const{src:a}=n,r=n.alt||pe(a),l=this.getImageURL(n);if(a||l){const h=document.createElement("li"),m=document.createElement("img");w(e.inheritedAttributes,u=>{const E=n.getAttribute(u);E!==null&&m.setAttribute(u,E)}),e.navbar&&(m.src=a||l),m.alt=r,m.setAttribute("data-original-url",l||a),h.setAttribute("data-index",o),h.setAttribute("data-viewer-action","view"),h.setAttribute("role","button"),e.keyboard&&h.setAttribute("tabindex",0),h.appendChild(m),i.appendChild(h),s.push(h)}}),this.items=s,w(s,n=>{const o=n.firstElementChild;let a,r;pt(o,"filled",!0),e.loading&&c(n,U),d(o,k,a=l=>{b(o,W,r),e.loading&&g(n,U),this.loadImage(l)},{once:!0}),d(o,W,r=()=>{b(o,k,a),e.loading&&g(n,U)},{once:!0})}),e.transition&&d(t,J,()=>{c(i,v)},{once:!0})},renderList(){const{index:t}=this,e=this.items[t];if(!e)return;const i=e.nextElementSibling,s=parseInt(window.getComputedStyle(i||e).marginLeft,10),{offsetWidth:n}=e,o=n+s;O(this.list,C({width:o*this.length-s},rt({translateX:(this.viewerData.width-n)/2-o*t})))},resetList(){const{list:t}=this;t.innerHTML="",g(t,v),O(t,rt({translateX:0}))},initImage(t){const{options:e,image:i,viewerData:s}=this,n=this.footer.offsetHeight,o=s.width,a=Math.max(s.height-n,n),r=this.imageData||{};let l;this.imageInitializing={abort:()=>{l.onload=null}},l=Jt(i,e,(h,m)=>{const u=h/m;let E=Math.max(0,Math.min(1,e.initialCoverage)),N=o,I=a;this.imageInitializing=!1,a*u>o?I=o/u:N=a*u,E=y(E)?E:.9,N=Math.min(N*E,h),I=Math.min(I*E,m);const A=(o-N)/2,S=(a-I)/2,L={left:A,top:S,x:A,y:S,width:N,height:I,oldRatio:1,ratio:N/h,aspectRatio:u,naturalWidth:h,naturalHeight:m},x=C({},L);e.rotatable&&(L.rotate=r.rotate||0,x.rotate=0),e.scalable&&(L.scaleX=r.scaleX||1,L.scaleY=r.scaleY||1,x.scaleX=1,x.scaleY=1),this.imageData=L,this.initialImageData=x,t&&t()})},renderImage(t){const{image:e,imageData:i}=this;if(O(e,C({width:i.width,height:i.height,marginLeft:i.x,marginTop:i.y},rt(i))),t)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&P(e,v)){const s=()=>{this.imageRendering=!1,t()};this.imageRendering={abort:()=>{b(e,z,s)}},d(e,z,s,{once:!0})}else t()},resetImage(){if(this.viewing||this.viewed){const{image:t}=this;this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null}}},ye={bind(){const{options:t,viewer:e,canvas:i}=this,s=this.element.ownerDocument;d(e,Z,this.onClick=this.click.bind(this)),d(e,At,this.onDragStart=this.dragstart.bind(this)),d(i,Dt,this.onPointerDown=this.pointerdown.bind(this)),d(s,kt,this.onPointerMove=this.pointermove.bind(this)),d(s,Lt,this.onPointerUp=this.pointerup.bind(this)),d(s,xt,this.onKeyDown=this.keydown.bind(this)),d(window,zt,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&d(e,Ot,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&d(i,gt,this.onDblclick=this.dblclick.bind(this))},unbind(){const{options:t,viewer:e,canvas:i}=this,s=this.element.ownerDocument;b(e,Z,this.onClick),b(e,At,this.onDragStart),b(i,Dt,this.onPointerDown),b(s,kt,this.onPointerMove),b(s,Lt,this.onPointerUp),b(s,xt,this.onKeyDown),b(window,zt,this.onResize),t.zoomable&&t.zoomOnWheel&&b(e,Ot,this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&b(i,gt,this.onDblclick)}},ve={click(t){const{options:e,imageData:i}=this;let{target:s}=t,n=B(s,lt);switch(!n&&s.localName==="img"&&s.parentElement.localName==="li"&&(s=s.parentElement,n=B(s,lt)),Y&&t.isTrusted&&s===this.canvas&&clearTimeout(this.clickCanvasTimeout),n){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(B(s,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-i.scaleX||-1);break;case"flip-vertical":this.scaleY(-i.scaleY||-1);break;default:this.played&&this.stop()}},dblclick(t){t.preventDefault(),this.viewed&&t.target===this.image&&(Y&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(t.isTrusted?t:t.detail&&t.detail.originalEvent))},load(){this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);const{element:t,options:e,image:i,index:s,viewerData:n}=this;g(i,G),e.loading&&g(this.canvas,U),i.style.cssText=`height:0;margin-left:${n.width/2}px;margin-top:${n.height/2}px;max-width:none!important;position:relative;width:0;`,this.initImage(()=>{tt(i,ae,e.movable),tt(i,v,e.transition),this.renderImage(()=>{this.viewed=!0,this.viewing=!1,p(e.viewed)&&d(t,J,e.viewed,{once:!0}),T(t,J,{originalImage:this.images[s],index:s,image:i},{cancelable:!1})})})},loadImage(t){const e=t.target,i=e.parentNode,s=i.offsetWidth||30,n=i.offsetHeight||50,o=!!B(e,"filled");Jt(e,this.options,(a,r)=>{const l=a/r;let h=s,m=n;n*l>s?o?h=n*l:m=s/l:o?m=s/l:h=n*l,O(e,C({width:h,height:m},rt({translateX:(s-h)/2,translateY:(n-m)/2})))})},keydown(t){const{options:e}=this;if(!e.keyboard)return;const i=t.keyCode||t.which||t.charCode;switch(i){case 13:this.viewer.contains(t.target)&&this.click(t);break}if(this.fulled)switch(i){case 27:this.played?this.stop():e.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(e.loop);break;case 38:t.preventDefault(),this.zoom(e.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(e.loop);break;case 40:t.preventDefault(),this.zoom(-e.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle());break}},dragstart(t){t.target.localName==="img"&&t.preventDefault()},pointerdown(t){const{options:e,pointers:i}=this,{buttons:s,button:n}=t;if(this.pointerMoved=!1,!this.viewed||this.showing||this.viewing||this.hiding||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(y(s)&&s!==1||y(n)&&n!==0||t.ctrlKey))return;t.preventDefault(),t.changedTouches?w(t.changedTouches,a=>{i[a.identifier]=nt(a)}):i[t.pointerId||0]=nt(t);let o=e.movable?ot:!1;e.zoomOnTouch&&e.zoomable&&Object.keys(i).length>1?o=K:e.slideOnTouch&&(t.pointerType==="touch"||t.type==="touchstart")&&this.isSwitchable()&&(o=Kt),e.transition&&(o===ot||o===K)&&g(this.image,v),this.action=o},pointermove(t){const{pointers:e,action:i}=this;!this.viewed||!i||(t.preventDefault(),t.changedTouches?w(t.changedTouches,s=>{C(e[s.identifier]||{},nt(s,!0))}):C(e[t.pointerId||0]||{},nt(t,!0)),this.change(t))},pointerup(t){const{options:e,action:i,pointers:s}=this;let n;t.changedTouches?w(t.changedTouches,o=>{n=s[o.identifier],delete s[o.identifier]}):(n=s[t.pointerId||0],delete s[t.pointerId||0]),i&&(t.preventDefault(),e.transition&&(i===ot||i===K)&&c(this.image,v),this.action=!1,Y&&i!==K&&n&&Date.now()-n.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),e.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout(()=>{T(this.image,gt,{originalEvent:t})},50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout(()=>{this.imageClicked=!1},500)):(this.imageClicked=!1,e.backdrop&&e.backdrop!=="static"&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout(()=>{T(this.canvas,Z,{originalEvent:t})},50)))))},resize(){if(!(!this.isShown||this.hiding)&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(()=>{this.renderImage()}),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement)){this.stop();return}w(this.player.getElementsByTagName("img"),t=>{d(t,k,this.loadImage.bind(this),{once:!0}),T(t,k)})}},wheel(t){if(!this.viewed||(t.preventDefault(),this.wheeling))return;this.wheeling=!0,setTimeout(()=>{this.wheeling=!1},50);const e=Number(this.options.zoomRatio)||.1;let i=1;t.deltaY?i=t.deltaY>0?1:-1:t.wheelDelta?i=-t.wheelDelta/120:t.detail&&(i=t.detail>0?1:-1),this.zoom(-i*e,!0,null,t)}},Te={show(t=!1){const{element:e,options:i}=this;if(i.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(p(i.show)&&d(e,_t,i.show,{once:!0}),T(e,_t)===!1||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();const{viewer:s}=this;if(g(s,F),s.setAttribute("role","dialog"),s.setAttribute("aria-labelledby",this.title.id),s.setAttribute("aria-modal",!0),s.removeAttribute("aria-hidden"),i.transition&&!t){const n=this.shown.bind(this);this.transitioning={abort:()=>{b(s,z,n),g(s,D)}},c(s,v),s.initialOffsetWidth=s.offsetWidth,d(s,z,n,{once:!0}),c(s,D)}else c(s,D),this.shown();return this},hide(t=!1){const{element:e,options:i}=this;if(i.inline||this.hiding||!(this.isShown||this.showing))return this;if(p(i.hide)&&d(e,Vt,i.hide,{once:!0}),T(e,Vt)===!1)return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();const{viewer:s,image:n}=this,o=()=>{g(s,D),this.hidden()};if(i.transition&&!t){const a=l=>{l&&l.target===s&&(b(s,z,a),this.hidden())},r=()=>{P(s,v)?(d(s,z,a),g(s,D)):o()};this.transitioning={abort:()=>{this.viewed&&P(n,v)?b(n,z,r):P(s,v)&&b(s,z,a)}},this.viewed&&P(n,v)?(d(n,z,r,{once:!0}),this.zoomTo(0,!1,null,null,!0)):r()}else o();return this},view(t=this.options.initialViewIndex){if(t=Number(t)||0,this.hiding||this.played||t<0||t>=this.length||this.viewed&&t===this.index)return this;if(!this.isShown)return this.index=t,this.show();this.viewing&&this.viewing.abort();const{element:e,options:i,title:s,canvas:n}=this,o=this.items[t],a=o.querySelector("img"),r=B(a,"originalUrl"),l=a.getAttribute("alt"),h=document.createElement("img");if(w(i.inheritedAttributes,I=>{const A=a.getAttribute(I);A!==null&&h.setAttribute(I,A)}),h.src=r,h.alt=l,p(i.view)&&d(e,Ft,i.view,{once:!0}),T(e,Ft,{originalImage:this.images[t],index:t,image:h})===!1||!this.isShown||this.hiding||this.played)return this;const m=this.items[this.index];m&&(g(m,et),m.removeAttribute("aria-selected")),c(o,et),o.setAttribute("aria-selected",!0),i.focus&&o.focus(),this.image=h,this.viewed=!1,this.index=t,this.imageData={},c(h,G),i.loading&&c(n,U),n.innerHTML="",n.appendChild(h),this.renderList(),s.innerHTML="";const u=()=>{const{imageData:I}=this,A=Array.isArray(i.title)?i.title[1]:i.title;s.innerHTML=fe(p(A)?A.call(this,h,I):`${l} (${I.naturalWidth} × ${I.naturalHeight})`)};let E,N;return d(e,J,u,{once:!0}),this.viewing={abort:()=>{b(e,J,u),h.complete?this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort():(h.src="",b(h,k,E),this.timeout&&clearTimeout(this.timeout))}},h.complete?this.load():(d(h,k,E=()=>{b(h,W,N),this.load()},{once:!0}),d(h,W,N=()=>{b(h,k,E),this.timeout&&(clearTimeout(this.timeout),this.timeout=!1),g(h,G),i.loading&&g(this.canvas,U)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(()=>{g(h,G),this.timeout=!1},1e3)),this},prev(t=!1){let e=this.index-1;return e<0&&(e=t?this.length-1:0),this.view(e),this},next(t=!1){const e=this.length-1;let i=this.index+1;return i>e&&(i=t?0:e),this.view(i),this},move(t,e=t){const{imageData:i}=this;return this.moveTo(q(t)?t:i.x+Number(t),q(e)?e:i.y+Number(e)),this},moveTo(t,e=t,i=null){const{element:s,options:n,imageData:o}=this;if(t=Number(t),e=Number(e),this.viewed&&!this.played&&n.movable){const a=o.x,r=o.y;let l=!1;if(y(t)?l=!0:t=a,y(e)?l=!0:e=r,l){if(p(n.move)&&d(s,Wt,n.move,{once:!0}),T(s,Wt,{x:t,y:e,oldX:a,oldY:r,originalEvent:i})===!1)return this;o.x=t,o.y=e,o.left=t,o.top=e,this.moving=!0,this.renderImage(()=>{this.moving=!1,p(n.moved)&&d(s,Ht,n.moved,{once:!0}),T(s,Ht,{x:t,y:e,oldX:a,oldY:r,originalEvent:i},{cancelable:!1})})}}return this},rotate(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo(t){const{element:e,options:i,imageData:s}=this;if(t=Number(t),y(t)&&this.viewed&&!this.played&&i.rotatable){const n=s.rotate;if(p(i.rotate)&&d(e,Pt,i.rotate,{once:!0}),T(e,Pt,{degree:t,oldDegree:n})===!1)return this;s.rotate=t,this.rotating=!0,this.renderImage(()=>{this.rotating=!1,p(i.rotated)&&d(e,qt,i.rotated,{once:!0}),T(e,qt,{degree:t,oldDegree:n},{cancelable:!1})})}return this},scaleX(t){return this.scale(t,this.imageData.scaleY),this},scaleY(t){return this.scale(this.imageData.scaleX,t),this},scale(t,e=t){const{element:i,options:s,imageData:n}=this;if(t=Number(t),e=Number(e),this.viewed&&!this.played&&s.scalable){const o=n.scaleX,a=n.scaleY;let r=!1;if(y(t)?r=!0:t=o,y(e)?r=!0:e=a,r){if(p(s.scale)&&d(i,Xt,s.scale,{once:!0}),T(i,Xt,{scaleX:t,scaleY:e,oldScaleX:o,oldScaleY:a})===!1)return this;n.scaleX=t,n.scaleY=e,this.scaling=!0,this.renderImage(()=>{this.scaling=!1,p(s.scaled)&&d(i,Bt,s.scaled,{once:!0}),T(i,Bt,{scaleX:t,scaleY:e,oldScaleX:o,oldScaleY:a},{cancelable:!1})})}}return this},zoom(t,e=!1,i=null,s=null){const{imageData:n}=this;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(n.width*t/n.naturalWidth,e,i,s),this},zoomTo(t,e=!1,i=null,s=null,n=!1){const{element:o,options:a,pointers:r,imageData:l}=this,{x:h,y:m,width:u,height:E,naturalWidth:N,naturalHeight:I}=l;if(t=Math.max(0,t),y(t)&&this.viewed&&!this.played&&(n||a.zoomable)){if(!n){const V=Math.max(.01,a.minZoomRatio),$=Math.min(100,a.maxZoomRatio);t=Math.min(Math.max(t,V),$)}if(s)switch(s.type){case"wheel":a.zoomRatio>=.055&&t>.95&&t<1.05&&(t=1);break;case"pointermove":case"touchmove":case"mousemove":t>.99&&t<1.01&&(t=1);break}const A=N*t,S=I*t,L=A-u,x=S-E,R=l.ratio;if(p(a.zoom)&&d(o,Yt,a.zoom,{once:!0}),T(o,Yt,{ratio:t,oldRatio:R,originalEvent:s})===!1)return this;if(this.zooming=!0,s){const V=ge(this.viewer),$=r&&Object.keys(r).length>0?we(r):{pageX:s.pageX,pageY:s.pageY};l.x-=L*(($.pageX-V.left-h)/u),l.y-=x*(($.pageY-V.top-m)/E)}else X(i)&&y(i.x)&&y(i.y)?(l.x-=L*((i.x-h)/u),l.y-=x*((i.y-m)/E)):(l.x-=L/2,l.y-=x/2);l.left=l.x,l.top=l.y,l.width=A,l.height=S,l.oldRatio=R,l.ratio=t,this.renderImage(()=>{this.zooming=!1,p(a.zoomed)&&d(o,Ut,a.zoomed,{once:!0}),T(o,Ut,{ratio:t,oldRatio:R,originalEvent:s},{cancelable:!1})}),e&&this.tooltip()}return this},play(t=!1){if(!this.isShown||this.played)return this;const{element:e,options:i}=this;if(p(i.play)&&d(e,Zt,i.play,{once:!0}),T(e,Zt)===!1)return this;const{player:s}=this,n=this.loadImage.bind(this),o=[];let a=0,r=0;if(this.played=!0,this.onLoadWhenPlay=n,t&&this.requestFullscreen(t),c(s,H),w(this.items,(l,h)=>{const m=l.querySelector("img"),u=document.createElement("img");u.src=B(m,"originalUrl"),u.alt=m.getAttribute("alt"),u.referrerPolicy=m.referrerPolicy,a+=1,c(u,at),tt(u,v,i.transition),P(l,et)&&(c(u,D),r=h),o.push(u),d(u,k,n,{once:!0}),s.appendChild(u)}),y(i.interval)&&i.interval>0){const l=()=>{clearTimeout(this.playing.timeout),g(o[r],D),r-=1,r=r>=0?r:a-1,c(o[r],D),this.playing.timeout=setTimeout(l,i.interval)},h=()=>{clearTimeout(this.playing.timeout),g(o[r],D),r+=1,r=r<a?r:0,c(o[r],D),this.playing.timeout=setTimeout(h,i.interval)};a>1&&(this.playing={prev:l,next:h,timeout:setTimeout(h,i.interval)})}return this},stop(){if(!this.played)return this;const{element:t,options:e}=this;if(p(e.stop)&&d(t,jt,e.stop,{once:!0}),T(t,jt)===!1)return this;const{player:i}=this;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,w(i.getElementsByTagName("img"),s=>{b(s,k,this.onLoadWhenPlay)}),g(i,H),i.innerHTML="",this.exitFullscreen(),this},full(){const{options:t,viewer:e,image:i,list:s}=this;return!this.isShown||this.played||this.fulled||!t.inline?this:(this.fulled=!0,this.open(),c(this.button,It),t.transition&&(g(s,v),this.viewed&&g(i,v)),c(e,mt),e.setAttribute("role","dialog"),e.setAttribute("aria-labelledby",this.title.id),e.setAttribute("aria-modal",!0),e.removeAttribute("style"),O(e,{zIndex:t.zIndex}),t.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=C({},this.containerData),this.renderList(),this.viewed&&this.initImage(()=>{this.renderImage(()=>{t.transition&&setTimeout(()=>{c(i,v),c(s,v)},0)})}),this)},exit(){const{options:t,viewer:e,image:i,list:s}=this;return!this.isShown||this.played||!this.fulled||!t.inline?this:(this.fulled=!1,this.close(),g(this.button,It),t.transition&&(g(s,v),this.viewed&&g(i,v)),t.focus&&this.clearEnforceFocus(),e.removeAttribute("role"),e.removeAttribute("aria-labelledby"),e.removeAttribute("aria-modal"),g(e,mt),O(e,{zIndex:t.zIndexInline}),this.viewerData=C({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(()=>{this.renderImage(()=>{t.transition&&setTimeout(()=>{c(i,v),c(s,v)},0)})}),this)},tooltip(){const{options:t,tooltipBox:e,imageData:i}=this;return!this.viewed||this.played||!t.tooltip?this:(e.textContent=`${Math.round(i.ratio*100)}%`,this.tooltipping?clearTimeout(this.tooltipping):t.transition?(this.fading&&T(e,z),c(e,H),c(e,at),c(e,v),e.removeAttribute("aria-hidden"),e.initialOffsetWidth=e.offsetWidth,c(e,D)):(c(e,H),e.removeAttribute("aria-hidden")),this.tooltipping=setTimeout(()=>{t.transition?(d(e,z,()=>{g(e,H),g(e,at),g(e,v),e.setAttribute("aria-hidden",!0),this.fading=!1},{once:!0}),g(e,D),this.fading=!0):(g(e,H),e.setAttribute("aria-hidden",!0)),this.tooltipping=!1},1e3),this)},toggle(t=null){return this.imageData.ratio===1?this.zoomTo(this.imageData.oldRatio,!0,null,t):this.zoomTo(1,!0,null,t),this},reset(){return this.viewed&&!this.played&&(this.imageData=C({},this.initialImageData),this.renderImage()),this},update(){const{element:t,options:e,isImg:i}=this;if(i&&!t.parentNode)return this.destroy();const s=[];if(w(i?[t]:t.querySelectorAll("img"),n=>{p(e.filter)?e.filter.call(this,n)&&s.push(n):this.getImageURL(n)&&s.push(n)}),!s.length)return this;if(this.images=s,this.length=s.length,this.ready){const n=[];if(w(this.items,(o,a)=>{const r=o.querySelector("img"),l=s[a];l&&r?(l.src!==r.src||l.alt!==r.alt)&&n.push(a):n.push(a)}),O(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){const o=n.indexOf(this.index);if(o>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-o,this.length-1),0));else{const a=this.items[this.index];c(a,et),a.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy(){const{element:t,options:e}=this;return t[f]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),e.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):e.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),e.inline||b(t,Z,this.onStart),t[f]=void 0,this):this}},Se={getImageURL(t){let{url:e}=this.options;return Q(e)?e=t.getAttribute(e):p(e)?e=e.call(this,t):e="",e},enforceFocus(){this.clearEnforceFocus(),d(document,Ct,this.onFocusin=t=>{const{viewer:e}=this;let{target:i}=t;if(!(i===document||i===e||e.contains(i))){for(;i;){if(i.getAttribute("tabindex")!==null||i.getAttribute("aria-modal")==="true")return;i=i.parentElement}e.focus()}})},clearEnforceFocus(){this.onFocusin&&(b(document,Ct,this.onFocusin),this.onFocusin=null)},open(){const{body:t}=this;c(t,Nt),this.scrollbarWidth>0&&(t.style.paddingRight=`${this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0)}px`)},close(){const{body:t}=this;g(t,Nt),this.scrollbarWidth>0&&(t.style.paddingRight=this.initialBodyPaddingRight)},shown(){const{element:t,options:e,viewer:i}=this;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,e.focus&&(i.focus(),this.enforceFocus()),p(e.shown)&&d(t,Mt,e.shown,{once:!0}),T(t,Mt)!==!1&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden(){const{element:t,options:e,viewer:i}=this;e.fucus&&this.clearEnforceFocus(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),c(i,F),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),i.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(p(e.hidden)&&d(t,$t,e.hidden,{once:!0}),T(t,$t,null,{cancelable:!1}))},requestFullscreen(t){const e=this.element.ownerDocument;if(this.fulled&&!(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)){const{documentElement:i}=e;i.requestFullscreen?X(t)?i.requestFullscreen(t):i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):i.mozRequestFullScreen?i.mozRequestFullScreen():i.msRequestFullscreen&&i.msRequestFullscreen()}},exitFullscreen(){const t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change(t){const{options:e,pointers:i}=this,s=i[Object.keys(i)[0]];if(!s)return;const n=s.endX-s.startX,o=s.endY-s.startY;switch(this.action){case ot:(n!==0||o!==0)&&(this.pointerMoved=!0,this.move(n,o,t));break;case K:this.zoom(be(i),!1,null,t);break;case Kt:{this.action="switched";const a=Math.abs(n);a>1&&a>Math.abs(o)&&(this.pointers={},n>1?this.prev(e.loop):n<-1&&this.next(e.loop));break}}w(i,a=>{a.startX=a.endX,a.startY=a.endY})},isSwitchable(){const{imageData:t,viewerData:e}=this;return this.length>1&&t.x>=0&&t.y>=0&&t.width<=e.width&&t.height<=e.height}},Ie=M.Viewer,Ne=(t=>()=>(t+=1,t))(-1);class ct{constructor(e,i={}){if(!e||e.nodeType!==1)throw new Error("The first argument is required and must be an element.");this.element=e,this.options=C({},St,X(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=Ne(),this.init()}init(){const{element:e,options:i}=this;if(e[f])return;e[f]=this,i.focus&&!i.keyboard&&(i.focus=!1);const s=e.localName==="img",n=[];if(w(s?[e]:e.querySelectorAll("img"),o=>{p(i.filter)?i.filter.call(this,o)&&n.push(o):this.getImageURL(o)&&n.push(o)}),this.isImg=s,this.length=n.length,this.images=n,this.initBody(),q(document.createElement(f).style.transition)&&(i.transition=!1),i.inline){let o=0;const a=()=>{if(o+=1,o===this.length){let r;this.initializing=!1,this.delaying={abort:()=>{clearTimeout(r)}},r=setTimeout(()=>{this.delaying=!1,this.build()},0)}};this.initializing={abort:()=>{w(n,r=>{r.complete||(b(r,k,a),b(r,W,a))})}},w(n,r=>{if(r.complete)a();else{let l,h;d(r,k,l=()=>{b(r,W,h),a()},{once:!0}),d(r,W,h=()=>{b(r,k,l),a()},{once:!0})}})}else d(e,Z,this.onStart=({target:o})=>{o.localName==="img"&&(!p(i.filter)||i.filter.call(this,o))&&this.view(this.images.indexOf(o))})}build(){if(this.ready)return;const{element:e,options:i}=this,s=e.parentNode,n=document.createElement("div");n.innerHTML=te;const o=n.querySelector(`.${f}-container`),a=o.querySelector(`.${f}-title`),r=o.querySelector(`.${f}-toolbar`),l=o.querySelector(`.${f}-navbar`),h=o.querySelector(`.${f}-button`),m=o.querySelector(`.${f}-canvas`);if(this.parent=s,this.viewer=o,this.title=a,this.toolbar=r,this.navbar=l,this.button=h,this.canvas=m,this.footer=o.querySelector(`.${f}-footer`),this.tooltipBox=o.querySelector(`.${f}-tooltip`),this.player=o.querySelector(`.${f}-player`),this.list=o.querySelector(`.${f}-list`),o.id=`${f}${this.id}`,a.id=`${f}Title${this.id}`,c(a,i.title?st(Array.isArray(i.title)?i.title[0]:i.title):F),c(l,i.navbar?st(i.navbar):F),tt(h,F,!i.button),i.keyboard&&h.setAttribute("tabindex",0),i.backdrop&&(c(o,`${f}-backdrop`),!i.inline&&i.backdrop!=="static"&&pt(m,lt,"hide")),Q(i.className)&&i.className&&i.className.split(wt).forEach(u=>{c(o,u)}),i.toolbar){const u=document.createElement("ul"),E=X(i.toolbar),N=it.slice(0,3),I=it.slice(7,9),A=it.slice(9);E||c(r,st(i.toolbar)),w(E?i.toolbar:it,(S,L)=>{const x=E&&X(S),R=E?Et(L):S,V=x&&!q(S.show)?S.show:S;if(!V||!i.zoomable&&N.indexOf(R)!==-1||!i.rotatable&&I.indexOf(R)!==-1||!i.scalable&&A.indexOf(R)!==-1)return;const $=x&&!q(S.size)?S.size:S,ut=x&&!q(S.click)?S.click:S,_=document.createElement("li");i.keyboard&&_.setAttribute("tabindex",0),_.setAttribute("role","button"),c(_,`${f}-${R}`),p(ut)||pt(_,lt,R),y(V)&&c(_,st(V)),["small","large"].indexOf($)!==-1?c(_,`${f}-${$}`):R==="play"&&c(_,`${f}-large`),p(ut)&&d(_,Z,ut),u.appendChild(_)}),r.appendChild(u)}else c(r,F);if(!i.rotatable){const u=r.querySelectorAll('li[class*="rotate"]');c(u,G),w(u,E=>{r.appendChild(E)})}if(i.inline)c(h,ie),O(o,{zIndex:i.zIndexInline}),window.getComputedStyle(s).position==="static"&&O(s,{position:"relative"}),s.insertBefore(o,e.nextSibling);else{c(h,ee),c(o,mt),c(o,at),c(o,F),O(o,{zIndex:i.zIndex});let{container:u}=i;Q(u)&&(u=e.ownerDocument.querySelector(u)),u||(u=this.body),u.appendChild(o)}if(i.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,p(i.ready)&&d(e,Rt,i.ready,{once:!0}),T(e,Rt)===!1){this.ready=!1;return}this.ready&&i.inline&&this.view(this.index)}static noConflict(){return window.Viewer=Ie,ct}static setDefaults(e){C(St,X(e)&&e)}}C(ct.prototype,Ee,ye,ve,Te,Se);function Ae(){document.querySelectorAll(".screen").forEach(e=>{const i=document.createElement("code");for(i.className="language-typescript";e.firstChild;)i.appendChild(e.firstChild);if(e.appendChild(i),i.offsetHeight>window.innerHeight*32/100){e.classList.add("fold");const s=document.createElement("div"),n=document.createElement("div");s.className="foldButton",n.className="divider",s.innerText="查看更多",e.appendChild(s),e.appendChild(n),s.addEventListener("click",()=>{e.classList.replace("fold","expand"),e.removeChild(s),e.removeChild(n)})}})}Ae();Qt.highlightAll();window.addEventListener("DOMContentLoaded",function(){Array.from(document.getElementsByTagName("img")).forEach((s,n)=>{s.dataset.original=s.src});const e=document.getElementsByClassName("nested0")[0],i=new ct(e,{filter(s){return s.id.length>0},fullscreen:!1,movable:!1,zoomable:!0,toggleOnDblclick:!0,navbar:!1,toolbar:!1,button:!1,title:function(s){return`${this.index+1}/${this.length}`},keyboard:!1,rotatable:!1,scalable:!1,initialCoverage:.75,minZoomRatio:.15,maxZoomRatio:1.2});window.checkPreview=()=>document.body.className==="viewer-open",window.closePreview=()=>{i.hide()}});function Ce(){const t=document.querySelectorAll(".figcap");t.forEach((e,i)=>{var n;const s=(n=e.nextElementSibling)==null?void 0:n.nextElementSibling;s&&s.insertAdjacentElement("afterend",e)}),t.forEach(e=>{e.classList.add("figcap-show")}),t.forEach(e=>{e.style.display="block"})}Ce();const xe=document.getElementsByTagName("object"),De=Array.from(xe);De.forEach(t=>{const e=document.createElement("video");e.controls=!0;const i=t.getAttribute("data");i&&(e.src=i);const s=t.getElementsByTagName("param");for(let o=0;o<s.length;o++){const a=s[o],r=a.getAttribute("name"),l=a.getAttribute("value");switch(r){case"play":l==="true"&&(e.autoplay=!0);break;case"loop":l==="true"&&(e.loop=!0);break;case"wmode":l==="transparent"&&(e.style.background="transparent");break}}const n=t.getAttribute("id");n&&(e.id=n),t.parentNode.replaceChild(e,t)});const yt=document.createElement("div");yt.className="footer";const dt=document.createElement("img");dt.className="footerImg";dt.src="../../common/image/f_icon.png";yt.appendChild(dt);document.body.appendChild(yt);const vt=window.matchMedia("(prefers-color-scheme: dark)"),Tt=t=>{dt.src=t.matches?"../../common/image/f_icon_dark.png":"../../common/image/f_icon.png"};Tt(vt);vt.addEventListener("change",Tt);window.addEventListener("beforeunload",()=>{vt.removeEventListener("change",Tt)});

// 导入Hypium测试框架的核心方法
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

/**
 * 本地单元测试函数
 * 定义和执行本地单元测试用例
 * 使用Hypium测试框架进行测试
 */
export default function localUnitTest() {
  // 定义测试套件，包含测试套件名称和测试套件函数
  describe('localUnitTest', () => {
    // 在所有测试用例开始前执行一次的预设操作
    beforeAll(() => {
      // 在测试套件的所有测试用例开始前执行的预设操作
      // 此API仅支持一个参数：预设操作函数
    });
    // 在每个单元测试用例开始前执行的预设操作
    beforeEach(() => {
      // 在每个单元测试用例开始前执行的预设操作
      // 执行次数与it定义的测试用例数量相同
      // 此API仅支持一个参数：预设操作函数
    });
    // 在每个单元测试用例结束后执行的清理操作
    afterEach(() => {
      // 在每个单元测试用例结束后执行的清理操作
      // 执行次数与it定义的测试用例数量相同
      // 此API仅支持一个参数：清理操作函数
    });
    // 在测试套件的所有测试用例结束后执行的清理操作
    afterAll(() => {
      // 在测试套件的所有测试用例结束后执行的清理操作
      // 此API仅支持一个参数：清理操作函数
    });
    // 定义测试用例，测试字符串包含和相等断言
    it('assertContain', 0, () => {
      // 定义测试用例，支持三个参数：测试用例名称、过滤参数、测试用例函数
      let a = 'abc';
      let b = 'b';
      // 定义各种断言方法，用于声明预期的布尔条件
      expect(a).assertContain(b);
      expect(a).assertEqual(a);
    });
  });
}
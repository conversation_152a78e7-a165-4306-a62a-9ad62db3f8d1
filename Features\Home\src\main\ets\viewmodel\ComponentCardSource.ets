// 导入组件卡片数据类
import { ComponentCardData } from '../model/ComponentData';

// 定义组件卡片数据源类，实现IDataSource接口
export class ComponentCardSource implements IDataSource {
  // 定义私有卡片列表数组
  private cardList: ComponentCardData[] = [];
  // 定义私有监听器数组
  private listeners: DataChangeListener[] = [];

  // 定义设置数据数组的公共方法
  public setDataArray(dataArray: ComponentCardData[]): void {
    // 设置卡片列表
    this.cardList = dataArray;
  }

  // 定义获取总数的公共方法
  public totalCount(): number {
    // 返回卡片列表长度
    return this.cardList.length;
  }

  // 定义获取数据的公共方法
  public getData(index: number): ComponentCardData {
    // 返回指定索引的卡片数据
    return this.cardList[index];
  }

  // 定义注册数据变更监听器的公共方法
  public registerDataChangeListener(listener: DataChangeListener): void {
    // 如果监听器不存在则添加
    if (this.listeners.indexOf(listener) < 0) {
      this.listeners.push(listener);
    }
  }

  // 定义注销数据变更监听器的公共方法
  public unregisterDataChangeListener(listener: DataChangeListener): void {
    // 获取监听器位置
    const pos: number = this.listeners.indexOf(listener);
    // 如果位置有效则移除监听器
    if (pos >= 0) {
      this.listeners.splice(pos, 1);
    }
  }
}
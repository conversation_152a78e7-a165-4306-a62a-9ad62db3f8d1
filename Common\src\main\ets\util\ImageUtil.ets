// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入偏好设置管理器
import { PreferenceManager } from '../storagemanager/PreferenceManager';
// 导入颜色工具类
import { ColorUtil } from './ColorUtil';
// 导入日志工具
import Logger from './Logger';
// 导入资源工具类
import { ResourceUtil } from './ResourceUtil';

// 横幅图片颜色存储键名常量
const BANNER_IMAGE_COLOR = 'banner_image_color';
// 日志标签常量
const TAG = '[ImageUtil]';

/**
 * 图片工具类
 * 提供图片颜色提取和缓存功能
 * 支持从偏好设置中获取缓存的颜色数据，提高性能
 */
export class ImageUtil {
  /**
   * 从图片URL获取颜色方法
   * 优先从偏好设置缓存中获取，如果没有则从图片路径提取
   * @param imgUrl 图片URL
   * @param isDeepColor 是否需要深色处理，可选参数
   * @returns Promise<颜色数组>
   */
  public static getColorFromImgUrl(imgUrl: string, isDeepColor?: boolean): Promise<number[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: number[]) => void, reject: (reason?: Object) => void) => {
      // 首先尝试从偏好设置中获取颜色数据
      ImageUtil.getColorDataByPreference(imgUrl).then((data: number[]) => {
        // 如果获取成功，直接返回缓存的颜色数据
        resolve(data);
      }).catch(() => {
        // 如果缓存中没有数据，则从图片路径提取颜色
        ImageUtil.getColorByPath(imgUrl, isDeepColor).then((colorData: number[]) => {
          // 提取成功后返回颜色数据
          resolve(colorData);
        }).catch((err: BusinessError) => {
          // 记录保存图片数据失败错误日志
          Logger.error(TAG, `Failed to Save ImageData. error ${err.code}  ${err.message}`);
          // 拒绝Promise
          reject();
        });
      });
    });
  }

  /**
   * 通过路径获取颜色私有方法
   * 从图片路径提取颜色数据并可选择进行深色处理
   * @param imageUrl 图片URL
   * @param isDeepColor 是否需要深色处理，可选参数
   * @returns Promise<颜色数组>
   */
  private static getColorByPath(imageUrl: string, isDeepColor?: boolean): Promise<number[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: number[]) => void, reject: (reason?: Object) => void) => {
      // 通过资源工具类获取图片的颜色数据
      ResourceUtil.getColorDataByPath(imageUrl).then((colors: number[]) => {
        // 初始化颜色数组
        let colorArr: number[] = colors;
        // 如果需要深色处理
        if (isDeepColor) {
          // 使用颜色工具类生成加深的沉浸式颜色
          colorArr = ColorUtil.getDeepenImmersionColor(colors[0], colors[1], colors[2]);
        }
        // 将颜色数据保存到偏好设置中进行缓存
        ImageUtil.setColorDataToPreference(imageUrl, colorArr);
        // 返回颜色数组
        resolve(colorArr);
      }).catch((err: BusinessError) => {
        // 记录通过路径获取颜色数据失败错误日志
        Logger.error(TAG, `Failed to getColorDataByPath. error ${err.code}  ${err.message}`);
        // 拒绝Promise
        reject();
      })
    });
  }

  /**
   * 从偏好设置获取颜色数据私有方法
   * 从偏好设置缓存中获取指定图片URL的颜色数据
   * @param imgUrl 图片URL
   * @returns Promise<颜色数组>
   */
  private static getColorDataByPreference(imgUrl: string): Promise<number[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: number[]) => void, reject: (reason?: Object) => void) => {
      // 从偏好设置管理器获取横幅图片颜色数据
      PreferenceManager.getInstance()
        .getValue<Record<string, number[]>>(BANNER_IMAGE_COLOR)
        .then((resp) => {
          // 如果响应数据不存在
          if (!resp) {
            // 拒绝Promise并提示偏好设置中没有数据
            reject('There is no data in the Preference');
          }
          // 将响应数据转换为记录类型
          resp = (resp as Record<string, number[]>);
          // 根据图片URL获取对应的颜色数据
          const ret = resp[imgUrl];
          // 如果该URL对应的颜色数据不存在
          if (!ret) {
            // 拒绝Promise并提示偏好设置中没有数据
            reject('There is no data in the Preference');
          }
          // 返回颜色数据
          resolve(ret);
        });
    });
  }

  /**
   * 设置颜色数据到偏好设置私有方法
   * 将图片URL和对应的颜色数据保存到偏好设置缓存中
   * @param imgUrl 图片URL
   * @param data 颜色数据数组
   * @returns Promise<void>
   */
  private static setColorDataToPreference(imgUrl: string, data: number[]): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置中是否已存在横幅图片颜色数据
      PreferenceManager.getInstance().hasValue(BANNER_IMAGE_COLOR)
        .then((result) => {
          // 如果已存在数据
          if (result) {
            // 获取现有的颜色数据记录
            PreferenceManager.getInstance()
              .getValue<Record<string, number[]>>(BANNER_IMAGE_COLOR)
              .then((resp) => {
                // 将响应数据转换为记录类型
                resp = (resp as Record<string, number[]>);
                // 添加或更新当前图片URL的颜色数据
                resp[imgUrl] = data;
                // 将更新后的数据保存到偏好设置
                PreferenceManager.getInstance().setValue(BANNER_IMAGE_COLOR, resp);
                // 解析Promise
                resolve();
              })
          } else {
            // 如果不存在数据，创建新的记录对象
            const record: Record<string, number[]> = {};
            // 设置当前图片URL的颜色数据
            record[imgUrl] = data;
            // 将新记录保存到偏好设置
            PreferenceManager.getInstance().setValue(BANNER_IMAGE_COLOR, record);
          }
        });
    });
  }
}
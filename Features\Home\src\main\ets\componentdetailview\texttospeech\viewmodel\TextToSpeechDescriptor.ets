// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';

/**
 * 文本转语音描述器类
 * 继承自通用描述器，用于描述文本转语音组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TextToSpeechDescriptor extends CommonDescriptor {
  // 语音播放速度，默认为1倍速
  public speed: number = 1;
  // 速度类型索引，用于选择速度选项
  public speedType: number = 1;
  // 速度选项数组，包含不同的播放速度选项
  public speedArray: string[] = ['0.5倍', '1倍', '1.5倍', '2倍'];

  /**
   * 转换原始属性到描述器属性
   * 将原始属性数组转换为描述器的具体属性值
   * @param attributes 原始属性数组
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性进行转换
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理速度属性
        case 'speed':
          // 在速度数组中查找当前值的索引
          this.speedType = this.speedArray.indexOf(attribute.currentValue);
          // 如果未找到匹配项，设置为默认值
          if (this.speedType === -1) {
            this.speedType = 1;
          }
          this.speed = (this.speedType + 1) * 0.5;
          break;
        default:
          break;
      }
    });
  }
}
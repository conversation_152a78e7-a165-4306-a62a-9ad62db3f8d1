// 导入通用模块中的Web工具、Web表单构建器、Web URL类型、全局信息模型、断点类型枚举和通用常量
import {
  WebUtil,
  WebSheetBuilder,
  WebUrlType,
  GlobalInfoModel,
  BreakpointTypeEnum,
  CommonConstants,
} from '@ohos/common';
// 导入详情页面常量
import { DetailPageConstant } from '../constant/DetailPageConstant';
// 导入推荐数据类型定义
import type { RecommendData } from '../model/ComponentDetailData';

// 使用Component装饰器定义推荐列表项组件
@Component
export struct RecommendListItem {
  // 使用StorageProp装饰器定义全局信息模型存储属性
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop装饰器定义项目数据属性
  @Prop itemData: RecommendData;
  // 使用State装饰器定义是否显示表单状态
  @State showSheet: boolean = false;

  // 定义组件构建方法
  build() {
    // 创建水平排列的行容器
    Row() {
      // 创建符号字形组件，根据文章类型显示不同图标
      SymbolGlyph(this.itemData.articleType === 1 ? $r('sys.symbol.doc_text') : $r('sys.symbol.paintpalette'))
        // 设置字体大小
        .fontSize($r('app.float.symbol_size_large'))
        // 设置右边距
        .margin({ right: $r('sys.float.padding_level8') })
        // 设置字体颜色
        .fontColor([$r('sys.color.icon_emphasize')])
      // 创建垂直排列的列容器用于文本内容
      Column() {
        // 创建文本组件显示标题
        Text(this.itemData.title).fontSize($r('sys.float.Body_L')).fontColor($r('sys.color.font_primary'))
        // 创建文本组件显示副标题
        Text(this.itemData.subTitle).fontSize($r('sys.float.Body_M')).fontColor($r('sys.color.font_tertiary'))
      }
      // 设置宽度为70%
      .width('70%')
      // 设置水平对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)

      // 创建空白填充组件
      Blank()
      // 创建右箭头符号字形组件
      SymbolGlyph($r('sys.symbol.chevron_right'))
        // 设置字体颜色
        .fontColor([$r('sys.color.icon_secondary')])
        // 设置字体大小
        .fontSize($r('app.float.symbol_size_large'))
        // 绑定表单显示
        .bindSheet(this.showSheet, WebSheetBuilder(this.itemData.url, WebUrlType.HARMONYOS), {
          // 显示关闭按钮
          showClose: true,
          // 表单消失时的回调
          onDisappear: () => {
            // 隐藏表单
            this.showSheet = false;
            // 移除Web节点
            WebUtil.getWebNode(this.itemData.url)?.remove();
          },
          // 设置表单类型为居中
          preferType: SheetType.CENTER,
          // 根据断点类型设置高度
          height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
            ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
            CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,
          // 根据断点类型设置宽度
          width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SHEET_WIDTH_XL :
            undefined,
          // 设置表单标题
          title: { title: this.itemData.title },
        })
    }
    // 设置点击事件处理
    .onClick(() => {
      // 创建Web节点
      WebUtil.createWebNode(this.itemData.url, undefined, NestedScrollMode.SELF_ONLY);
      // 添加Web节点
      WebUtil.addNode(this.itemData.url);
      // 显示表单
      this.showSheet = true;
    })
    // 设置垂直对齐方式为居中
    .alignItems(VerticalAlign.Center)
    // 设置宽度为100%
    .width('100%')
    // 设置高度
    .height(DetailPageConstant.ATTRIBUTE_ITEM_HEIGHT)
    // 设置内边距
    .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
  }
}
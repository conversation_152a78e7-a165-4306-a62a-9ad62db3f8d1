// 导入通用能力类型，用于获取应用上下文
import type { common } from '@kit.AbilityKit';
// 导入提示操作模块，用于显示Toast消息
import { promptAction } from '@kit.ArkUI';
// 导入业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入相机和相机选择器模块，用于实现相机功能
import { camera, cameraPicker } from '@kit.CameraKit';
// 导入日志工具，用于记录操作信息
import { Logger } from '@ohos/common';
// 导入相机选择器描述器类型，用于描述组件配置
import type { CameraPickerDescriptor } from '../viewmodel/CameraPickerDescriptor';

// 日志标签，用于标识相机选择器构建器的日志
const TAG: string = '[CameraPickerBuilder]';
// 成功代码常量
const CODE_SUCC: number = 0;

/**
 * 相机选择器组件结构体
 * 提供相机拍照和录像功能，支持预览和选择媒体类型
 */
@Component
export struct CameraPickerComponent {
  // 相机选择器描述器属性，包含组件配置信息
  @Prop cameraPickerDescriptor: CameraPickerDescriptor;
  // 媒体资源URI状态，存储拍摄的照片或视频路径
  @State uri?: ResourceStr = undefined;
  // 媒体类型状态，默认为照片类型
  @State mediaType: cameraPicker.PickerMediaType = cameraPicker.PickerMediaType.PHOTO;
  // 是否已填充内容状态，控制UI显示效果
  @State isFilled: boolean = false;
  // 相机数量状态，用于检查设备是否有相机
  @State cameraNum: number = 0;
  // 视频控制器，用于控制视频播放
  private controller: VideoController = new VideoController();

  /**
   * 组件即将出现时的生命周期方法
   * 初始化相机管理器并获取支持的相机数量
   */
  aboutToAppear() {
    // 获取应用上下文
    const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
    let cameraManager: camera.CameraManager;
    try {
      // 获取相机管理器实例
      cameraManager = camera.getCameraManager(context);
    } catch (err) {
      // 捕获并记录获取相机管理器失败的错误
      const error: BusinessError = err as BusinessError;
      Logger.error(TAG, `GetCameraManager error, the code is ${error.code}, the message is ${error.message}`);
      return;
    }
    // 获取支持的相机设备数组
    const cameraArray: camera.CameraDevice[] = this.getSupportedCameras(cameraManager);
    // 设置相机数量
    this.cameraNum = cameraArray.length;
  }

  /**
   * 构建组件UI方法
   * 创建包含媒体预览和拍摄按钮的界面
   */
  build() {
    // 创建堆叠布局，内容居中显示
    Stack({ alignContent: Alignment.Center }) {
      // 根据媒体类型显示不同的预览组件
      if (this.mediaType === cameraPicker.PickerMediaType.VIDEO) {
        // 显示视频预览组件
        Video({ src: this.uri, controller: this.controller })
          // 设置视频尺寸
          .width('100%')
          .height('100%')
          // 设置视频适应方式为包含
          .objectFit(ImageFit.Contain)
          // 设置圆角边框
          .borderRadius($r('sys.float.corner_radius_level8'))
          // 视频准备完成时的回调，设置播放时间点
          .onPrepared(() => {
            this.controller.setCurrentTime(0.5, SeekMode.Accurate)
          })
      } else {
        // 显示图片预览组件
        Image(this.uri)
          // 设置图片尺寸
          .width('100%')
          .height('100%')
          // 设置图片适应方式为包含
          .objectFit(ImageFit.Contain)
          // 设置圆角边框
          .borderRadius($r('sys.float.corner_radius_level8'))
      }
      // 创建相机拍摄按钮
      Button($r('app.string.camera_picker_button'), { buttonStyle: ButtonStyleMode.NORMAL })
        // 根据是否已填充内容设置背景模糊样式
        .backgroundBlurStyle(this.isFilled ? BlurStyle.COMPONENT_REGULAR : BlurStyle.NONE,
          { adaptiveColor: AdaptiveColor.AVERAGE })
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 根据是否已填充内容设置字体颜色
        .fontColor(this.isFilled ? $r('sys.color.font_on_primary') : $r('sys.color.font_emphasize'))
        // 设置按钮底部边距
        .margin({ bottom: $r('sys.float.padding_level10') })
        // 设置点击事件处理，启动相机选择器
        .onClick(async () => {
          // 检查设备是否有相机
          if (this.cameraNum === 0) {
            try {
              // 显示设备无相机的提示消息
              promptAction.showToast({ message: $r('app.string.device_camera') });
            } catch (err) {
              // 捕获并记录显示Toast失败的错误
              const error: BusinessError = err as BusinessError;
              Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
            }
            return;
          }
          try {
            // 创建相机选择器配置，使用后置摄像头
            const pickerProfile: cameraPicker.PickerProfile = {
              cameraPosition: camera.CameraPosition.CAMERA_POSITION_BACK,
            };
            // 调用相机选择器进行拍摄
            const pickerResult: cameraPicker.PickerResult =
              await cameraPicker.pick(getContext(), this.cameraPickerDescriptor.mediaTypes, pickerProfile);
            // 检查拍摄结果
            if (pickerResult.resultCode === CODE_SUCC) {
              // 拍摄成功，更新状态
              this.isFilled = true;
              this.uri = pickerResult.resultUri;
              this.mediaType = pickerResult.mediaType;
            } else {
              // 拍摄失败，记录错误日志
              Logger.error(TAG, 'the pick pickerResult : error');
            }
          } catch (error) {
            // 捕获并记录拍摄过程中的错误
            const err = error as BusinessError;
            Logger.error(TAG, `${err.code},${err.message}`);
          }
        })
    }
  }

  /**
   * 获取支持的相机设备方法
   * 通过相机管理器获取设备上所有可用的相机
   * @param cameraManager 相机管理器实例
   * @returns 相机设备数组
   */
  getSupportedCameras(cameraManager: camera.CameraManager): camera.CameraDevice[] {
    // 初始化相机设备数组
    let cameras: camera.CameraDevice[] = [];
    try {
      // 获取支持的相机设备列表
      cameras = cameraManager.getSupportedCameras();
    } catch (error) {
      // 捕获并记录获取相机设备失败的错误
      const err = error as BusinessError;
      Logger.error(TAG, `The getSupportedCameras call failed. error code: ${err.code}`);
    }
    return cameras;
  }
}
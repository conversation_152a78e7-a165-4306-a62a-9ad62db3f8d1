// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入评分组件描述器类型，用于描述组件配置
import type { RatingDescriptor } from './RatingDescriptor';

/**
 * 评分组件属性修改器类
 * 继承通用属性修改器，专门用于修改评分组件的属性
 * 支持星星数量和星星样式等属性的动态修改
 */
@Observed
export class RatingAttributeModifier extends CommonAttributeModifier<RatingDescriptor, RatingAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性值应用到评分组件实例上
   * @param instance 评分组件属性实例
   */
  public applyNormalAttribute(instance: RatingAttribute): void {
    // 分配星星数量属性，从描述器获取星星数量并应用到实例
    this.assignAttribute((descriptor => descriptor.stars), (val) => instance.stars(Number(val)));
    // 分配星星样式属性，根据布尔值设置自定义图片或默认样式
    this.assignAttribute((descriptor => descriptor.starStyle), (val) => instance.starStyle(Boolean(val) ?
      {
        // 启用自定义样式时，设置背景和前景图片
        backgroundUri: '/resources/base/media/rating_background.png',
        foregroundUri: '/resources/base/media/rating_foreground.png',
      } : {
        // 使用默认样式时，清除自定义图片
        backgroundUri: undefined,
        foregroundUri: undefined,
      }));
  }
}
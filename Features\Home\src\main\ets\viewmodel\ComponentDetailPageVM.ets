// 导入ArkUI工具包中的分段按钮文本项
import { SegmentButtonTextItem } from '@kit.ArkUI';
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的基础视图模型、基础视图模型事件、断点类型枚举、全局信息模型、加载状态、可观察数组、页面上下文和顶部导航数据
import {
  BaseVM,
  BaseVMEvent,
  BreakpointTypeEnum,
  GlobalInfoModel,
  LoadingStatus,
  ObservedArray,
  PageContext,
  TopNavigationData,
} from '@ohos/common';
// 导入组件详情状态相关类和方法
import {
  ColorPickerAttribute,
  ComponentDetailState,
  getEnumType,
  OpacityPickerAttribute,
  SelectComAttribute,
  SliderComAttribute,
  ToggleButtonAttribute,
  ToggleComAttribute,
} from './ComponentDetailState';
// 导入组件详情数据相关类型定义
import type { AttributeData, ComponentDetailData, RecommendData } from '../model/ComponentDetailData';
// 导入组件详情模型
import { ComponentDetailModel } from '../model/ComponentDetailModel';
// 导入属性相关类
import { Attribute, OriginAttribute } from './Attribute';
// 导入属性类型枚举
import { AttributeTypeEnum } from './AttributeTypeEnum';
// 导入通用代码生成器
import { CommonCodeGenerator } from './CommonCodeGenerator';
// 导入通用属性过滤器
import { CommonAttributeFilter } from './CommonAttributeFilter';
// 导入通用描述符
import { CommonDescriptor } from './CommonDescriptor';
// 导入配置接口类型定义
import type { ConfigInterface } from '../componentdetailview/ComponentDetailConfig';
// 导入组件详情管理器
import { ComponentDetailManager } from './ComponentDetailManager';

// 定义组件详情页面视图模型类，继承基础视图模型
export class ComponentDetailPageVM extends BaseVM<ComponentDetailState> {
  // 定义公共组件名称
  public componentName: string;
  // 定义公共代码生成器
  public codeGenerator: CommonCodeGenerator;
  // 定义公共属性过滤器（可选）
  public attributeFilter?: CommonAttributeFilter;
  // 定义公共描述符
  public descriptor: CommonDescriptor;
  // 定义公共原始属性数组
  public originAttributes: OriginAttribute[] = [];
  // 定义公共完整属性可观察数组
  public fullAttributes: ObservedArray<Attribute> = [];
  // 定义私有详情页面模型实例
  private detailPageModel = ComponentDetailModel.getInstance();
  // 定义私有全局信息模型，从应用存储获取
  private globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 定义私有详情数据（可选）
  private detailData?: ComponentDetailData;

  // 定义构造函数
  constructor(componentName: string) {
    // 从应用存储获取组件详情配置
    const componentDetailConfig: Record<string, ConfigInterface> = AppStorage.get('componentDetailConfig')!;
    // 调用父类构造函数，传入新的组件详情状态
    super(new ComponentDetailState(componentDetailConfig[componentName].descriptor(), [], '', [], LoadingStatus.OFF,
      new TopNavigationData()));
    // 初始化组件名称
    this.componentName = componentName;
    // 初始化代码生成器
    this.codeGenerator = componentDetailConfig[componentName].codeGenerate;
    // 初始化属性过滤器
    this.attributeFilter = componentDetailConfig[componentName].attributeFilter;
    // 初始化描述符
    this.descriptor = componentDetailConfig[componentName].descriptor();
  }

  // 定义初始化方法
  init(event: InitComponentEvent): Promise<void> {
    // 如果加载状态不是正在加载
    if (this.state.loadingStatus !== LoadingStatus.LOADING) {
      // 设置加载状态为正在加载
      this.state.loadingStatus = LoadingStatus.LOADING;
      // 初始化顶部导航数据
      this.initTopNavigationData();
      // 清空代码
      this.state.code = '';
      // 清空推荐列表
      this.state.recommends = [];
      // 清空属性列表
      this.state.attributes = [];
      // 如果详情数据存在
      if (this.detailData) {
        // 处理属性
        this.processAttribute(this.detailData.props);
        // 设置加载状态为成功
        this.state.loadingStatus = LoadingStatus.SUCCESS;
        // 返回已解决的Promise
        return Promise.resolve();
      }
      // 调用详情页面模型初始化方法
      return this.detailPageModel.init(event.id).then((data: ComponentDetailData) => {
        // 设置详情数据
        this.detailData = data;
        // 处理属性
        this.processAttribute(data.props);
        // 设置加载状态为成功
        this.state.loadingStatus = LoadingStatus.SUCCESS;
        // 更新详情视图模型映射表
        ComponentDetailManager.getInstance().updateDetailViewModelMap(this.componentName, this);
      }).catch((_err: string) => {
        // 设置加载状态为失败
        this.state.loadingStatus = LoadingStatus.FAILED;
      });
    }
    // 返回已解决的Promise
    return Promise.resolve();
  }

  // 定义发送事件的方法
  sendEvent(event: DetailPageEvent): Promise<string | void> | null {
    // 如果事件是初始化组件事件
    if (event instanceof InitComponentEvent) {
      // 返回初始化方法的结果
      return this.init(event);
    } else if ((event instanceof ChangeAttributeEvent) || (event instanceof ComPreviewChangeEvent)) {
      // 如果事件是改变属性事件或组件预览改变事件，调用改变属性方法
      this.changeAttribute(event);
    } else if (event instanceof ComponentDetailEvent) {
      // 如果事件是组件详情事件
      if (event.type === ComponentDetailEventType.INIT_RECOMMEND) {
        // 如果事件类型是初始化推荐，初始化推荐列表
        this.initRecommendList(this.detailData?.recommendList || []);
      } else if (event.type === ComponentDetailEventType.INIT_DESCRIPTOR) {
        // 如果事件类型是初始化描述符，初始化描述符
        this.initDescriptor();
      } else {
        // 否则返回初始化Web代码的结果
        return this.initWebCode();
      }
    } else if (event instanceof TopNavigationChangeEvent) {
      // 如果事件是顶部导航改变事件，改变顶部导航状态
      this.changeTopNavigationState(event);
    } else {
      // 否则改变属性启用状态
      this.changeAttributeEnable(event);
    }
    // 返回null
    return null;
  }

  // 定义弹出页面的公共方法
  public pop(animated?: boolean) {
    // 根据断点类型获取页面上下文
    const pageContext: PageContext =
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('componentListPageContext')! :
        AppStorage.get('pageContext')!;
    // 弹出页面
    pageContext.popPage(animated);
  }

  // 定义跳转到代码预览的公共方法
  public jumpToCodePreview(code: string, preRebuild: () => void) {
    // 根据断点类型获取页面上下文
    const pageContext: PageContext = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
      AppStorage.get('componentListPageContext')! : AppStorage.get('pageContext')!;
    // 打开代码预览页面
    pageContext.openPage({
      routerName: 'CodePreviewView',
      param: {
        code: code,
        componentName: this.componentName,
        preFinishAnimation: preRebuild,
      } as CodeViewParams,
    }, false);
  }

  // 定义跳转到PenKit视图的公共方法
  public jumpToPenKitView() {
    // 根据断点类型获取页面上下文
    const pageContext: PageContext = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
      AppStorage.get('componentListPageContext')! : AppStorage.get('pageContext')!;
    // 打开PenKit视图页面
    pageContext.openPage({
      routerName: 'PenKitView',
    });
  }

  // 定义私有改变属性的方法
  private changeAttribute(event: ChangeAttributeEvent | ComPreviewChangeEvent) {
    // 查找原始属性中匹配的索引
    const index: number = this.originAttributes.findIndex((item) => item.name === event.attributeName);
    // 更新原始属性的当前值
    this.originAttributes[index].currentValue = event.attributeValue;
    // 更新完整属性的当前值
    this.fullAttributes[index].currentValue = event.attributeValue;
    // 生成新的代码
    this.state.code = this.codeGenerator.generate(this.originAttributes);
    // 如果属性过滤器存在则进行过滤
    if (this.attributeFilter) {
      this.attributeFilter.filter(this.fullAttributes);
    }
    // 过滤启用的属性
    this.state.attributes = this.fullAttributes.filter((attribute) => attribute.enable);
    // 创建新实例
    this.descriptor.convert(this.originAttributes);
    // 更新状态描述符
    this.state.descriptor = this.descriptor;
  }

  // 定义私有初始化Web代码的方法
  private initWebCode(): Promise<void> {
    // 生成代码
    this.state.code = this.codeGenerator.generate(this.originAttributes);
    // 如果推荐列表为空则初始化推荐列表
    if (this.state.recommends.length === 0) {
      this.initRecommendList(this.detailData?.recommendList || []);
    }
    // 返回已解决的Promise
    return Promise.resolve();
  }

  // 定义私有初始化描述符的方法
  private initDescriptor(): void {
    // 转换状态描述符
    this.state.descriptor.convert(this.originAttributes);
    // 如果属性过滤器存在则进行过滤
    if (this.attributeFilter) {
      this.attributeFilter.filter(this.fullAttributes);
    }
    // 过滤启用的属性
    this.state.attributes = this.fullAttributes.filter((attribute) => attribute.enable);
  }

  // 定义私有改变属性启用状态的方法
  private changeAttributeEnable(event: AttributeChangeEnable): void {
    // 获取属性名称
    const name: string = event.attributeName;
    // 获取启用状态
    const enable: boolean = event.enable;
    // 查找完整属性中匹配的索引
    const index = this.fullAttributes.findIndex(item => item.name === name);
    // 如果索引有效
    if (index !== -1) {
      // 更新属性启用状态
      this.fullAttributes[index].enable = enable;
      // 过滤启用的属性
      this.state.attributes = this.fullAttributes.filter((attribute) => attribute.enable);
    }
  }

  // 定义私有初始化推荐列表的方法
  private initRecommendList(recommendList: RecommendData[]): void {
    // 创建推荐数组
    const recommends: RecommendData[] = [];
    // 遍历推荐列表
    recommendList?.forEach((item) => {
      // 添加推荐项
      recommends.push({
        title: item.articleType === 1 ? $r('app.string.develop_practice') : $r('app.string.design_practice'),
        articleType: item.articleType,
        url: item.url,
      });
    });
    // 设置状态推荐列表
    this.state.recommends = recommends;
  }

  // 定义私有构造属性组件的方法
  private constructAttributeCom(originAttribute: OriginAttribute): Attribute {
    // 根据原始属性类型进行切换
    switch (originAttribute.type) {
      case AttributeTypeEnum.TOGGLE_BUTTON: {
        // 创建切换按钮属性
        const attribute =
          new ToggleButtonAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 映射原始属性值到选择选项
        originAttribute.values.map((item, index) => {
          attribute.selectOption[index] = { text: item } as SegmentButtonTextItem;
        });
        // 返回属性
        return attribute;
      }
      case AttributeTypeEnum.TOGGLE: {
        // 创建切换组件属性
        const attribute =
          new ToggleComAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 返回属性
        return attribute;
      }
      case AttributeTypeEnum.SELECT: {
        // 创建选择组件属性
        const attribute =
          new SelectComAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 映射原始属性值到选择选项
        attribute.selectOption = originAttribute.values.map((item) => {
          return { value: item } as SelectOption;
        });
        // 返回属性
        return attribute;
      }
      case AttributeTypeEnum.SLIDER: {
        // 创建滑块组件属性
        const attribute =
          new SliderComAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 设置左范围
        attribute.leftRange = originAttribute.leftRange;
        // 设置右范围
        attribute.rightRange = originAttribute.rightRange;
        // 设置步长
        attribute.step = originAttribute.step;
        // 返回属性
        return attribute;
      }
      case AttributeTypeEnum.COLOR: {
        // 创建颜色选择器属性
        const attribute =
          new ColorPickerAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 返回属性
        return attribute;
      }
      default: {
        // 创建透明度选择器属性
        const attribute =
          new OpacityPickerAttribute(originAttribute.name, originAttribute.displayName, originAttribute.currentValue,
            originAttribute.type);
        // 设置左范围
        attribute.leftRange = originAttribute.leftRange;
        // 设置右范围
        attribute.rightRange = originAttribute.rightRange;
        // 设置步长
        attribute.step = originAttribute.step;
        // 返回属性
        return attribute;
      }
    }
  }

  // 定义私有处理属性的方法
  private processAttribute(attributes: AttributeData[]) {
    // 定义当前值数组
    let curValueArray: string[] = [];
    // 从应用存储获取系统颜色模式
    const systemColorMode: ConfigurationConstant.ColorMode = AppStorage.get('systemColorMode')!;
    // 创建原始属性列表
    const originAttributeList: OriginAttribute[] = [];
    // 创建属性列表
    const attributeList: ObservedArray<Attribute> = [];
    // 遍历属性数组
    attributes.forEach((attribute: AttributeData) => {
      // 创建原始属性实例
      const originAttribute: OriginAttribute = new OriginAttribute();
      // 设置属性名称
      originAttribute.name = attribute.propertyName;
      // 设置显示名称
      originAttribute.displayName = attribute.propertyDesc;
      // 获取枚举类型
      originAttribute.type = getEnumType(attribute);
      // 如果显示类型是颜色
      if (attribute.displayType === 'color') {
        // 分割默认属性值
        curValueArray = attribute.defaultProperty.split(';');
        // 如果数组长度为1
        if (curValueArray.length === 1) {
          // 设置当前值
          originAttribute.currentValue = curValueArray[0];
        } else {
          // 根据系统颜色模式设置当前值
          originAttribute.currentValue =
            systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT ? curValueArray[0] : curValueArray[1];
        }
      } else {
        // 设置当前值为默认属性
        originAttribute.currentValue = attribute.defaultProperty;
      }

      // 如果显示类型是枚举
      if (attribute.displayType === 'enum') {
        // 解析属性值
        originAttribute.values = JSON.parse(attribute.propertyValues);
      } else if (attribute.displayType === 'number' || attribute.displayType === 'opacity') {
        // 解析属性值为数字记录
        const propertyValues: Record<string, number> = JSON.parse(attribute.propertyValues);
        // 设置左范围
        originAttribute.leftRange = propertyValues.left;
        // 设置右范围
        originAttribute.rightRange = propertyValues.right;
        // 设置步长
        originAttribute.step = propertyValues.step;
      }
      // 添加到原始属性列表
      originAttributeList.push(originAttribute);
      // 添加到属性列表
      attributeList.push(this.constructAttributeCom(originAttribute));
    });
    // 设置完整属性
    this.fullAttributes = attributeList;
    // 设置状态属性
    this.state.attributes = attributeList;
    // 设置原始属性
    this.originAttributes = originAttributeList;
  }

  // 定义私有初始化顶部导航数据的方法
  private initTopNavigationData() {
    // 设置状态顶部导航数据
    this.state.topNavigationData = {
      title: `${this.componentName}`,
      titleColor: $r('sys.color.font_primary'),
      isFullScreen: true,
      isBlur: false,
      onBackClick: () => {
        this.pop();
      },
    };
  }

  // 定义私有改变顶部导航状态的方法
  private changeTopNavigationState(event: TopNavigationChangeEvent): void {
    // 更新状态顶部导航数据
    this.state.topNavigationData = {
      title: this.state.topNavigationData.title,
      titleColor: this.state.topNavigationData.titleColor,
      isFullScreen: this.state.topNavigationData.isFullScreen,
      isBlur: event.isBlur,
      onBackClick: this.state.topNavigationData.onBackClick,
    };
  }
}

// 定义组件详情事件类型枚举
export enum ComponentDetailEventType {
  // 初始化推荐事件
  INIT_RECOMMEND = 'initRecommendEvent',
  // 初始化描述符事件
  INIT_DESCRIPTOR = 'initDescriptor',
  // Web代码事件
  WEB_CODE_EVENT = 'webCodeEvent',
}

// 定义组件详情事件类，实现基础视图模型事件接口
export class ComponentDetailEvent implements BaseVMEvent {
  // 定义只读事件类型
  public readonly type: ComponentDetailEventType;

  // 定义构造函数
  constructor(type: ComponentDetailEventType) {
    // 初始化事件类型
    this.type = type;
  }
}

// 定义初始化组件事件类，实现基础视图模型事件接口
export class InitComponentEvent implements BaseVMEvent {
  // 定义只读ID
  public readonly id: number;

  // 定义构造函数
  constructor(id: number) {
    // 初始化ID
    this.id = id;
  }
}

// 定义改变属性事件类，实现基础视图模型事件接口
export class ChangeAttributeEvent implements BaseVMEvent {
  // 定义只读属性名称
  public readonly attributeName: string;
  // 定义只读属性值
  public readonly attributeValue: string;

  // 定义构造函数
  constructor(attributeName: string, attributeValue: string) {
    // 初始化属性名称
    this.attributeName = attributeName;
    // 初始化属性值
    this.attributeValue = attributeValue;
  }
}

// 定义组件预览改变事件类，实现基础视图模型事件接口
export class ComPreviewChangeEvent implements BaseVMEvent {
  // 定义只读属性名称
  public readonly attributeName: string;
  // 定义只读属性值
  public readonly attributeValue: string;

  // 定义构造函数
  constructor(attributeName: string, attributeValue: string) {
    // 初始化属性名称
    this.attributeName = attributeName;
    // 初始化属性值
    this.attributeValue = attributeValue;
  }
}

// 定义顶部导航改变事件类，实现基础视图模型事件接口
export class TopNavigationChangeEvent implements BaseVMEvent {
  // 定义只读模糊状态
  public readonly isBlur: boolean;

  // 定义构造函数
  constructor(isBlur: boolean) {
    // 初始化模糊状态
    this.isBlur = isBlur;
  }
}

// 定义属性改变启用类
export class AttributeChangeEnable {
  // 定义只读属性名称
  public readonly attributeName: string;
  // 定义只读启用状态
  public readonly enable: boolean;

  // 定义构造函数
  constructor(attributeName: string, enable: boolean) {
    // 初始化属性名称
    this.attributeName = attributeName;
    // 初始化启用状态
    this.enable = enable;
  }
}

// 定义代码视图参数接口
export interface CodeViewParams {
  // 代码字符串
  code: string;
  // 组件名称
  componentName: string;
  // 预完成动画回调函数
  preFinishAnimation: () => void;
}

// 定义详情页面事件类型联合
export type DetailPageEvent = ComponentDetailEvent | InitComponentEvent | ChangeAttributeEvent | ComPreviewChangeEvent
  | AttributeChangeEnable | TopNavigationChangeEvent;
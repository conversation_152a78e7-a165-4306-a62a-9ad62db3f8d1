// 导入加载状态枚举
import { LoadingStatus } from '../constant/CommonEnums';

/**
 * 加载模型类
 * 用于管理页面和数据的加载状态
 * 包含主要加载状态、加载更多状态和分页信息
 */
@Observed
export class LoadingModel {
  // 主要加载状态，默认为关闭状态
  public loadingStatus: LoadingStatus = LoadingStatus.OFF;
  // 加载更多数据的状态，默认为关闭状态
  public loadingMoreStatus: LoadingStatus = LoadingStatus.OFF;
  // 是否还有下一页数据，默认为false
  public hasNextPage: boolean = false;

  /**
   * 构造函数
   * 初始化加载模型
   * @param loadingStatus 初始加载状态，可选参数，默认为OFF
   */
  public constructor(loadingStatus?: LoadingStatus) {
    // 设置加载状态，如果未提供则使用默认值OFF
    this.loadingStatus = loadingStatus || LoadingStatus.OFF;
  }
}
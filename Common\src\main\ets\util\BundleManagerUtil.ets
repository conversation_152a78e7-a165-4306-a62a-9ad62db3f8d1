// 导入AbilityKit包中的包管理器
import { bundleManager } from '@kit.AbilityKit';
// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from './Logger';
// 导入包信息数据模型
import { BundleInfoData } from '../model/BundleInfoData';

// 日志标签常量
const TAG = '[BundleManagerUtil]';

/**
 * 包管理器工具类
 * 提供应用包信息获取功能
 * 用于获取当前应用的版本信息和基本信息
 */
export class BundleManagerUtil {
  /**
   * 获取包信息方法
   * 获取当前应用的包信息并存储到AppStorage
   * 包括版本名称、版本代码和应用名称
   */
  public static getBundleInfo(): void {
    try {
      // 获取当前应用的包信息，使用默认标志
      bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_DEFAULT)
        .then((bundleInfo: bundleManager.BundleInfo) => {
          // 记录获取包信息成功调试日志
          Logger.debug(TAG, `getBundleInfoForSelf successed. ${bundleInfo}`);
          // 创建包信息数据对象并存储到AppStorage
          AppStorage.setOrCreate('BundleInfoData',
            new BundleInfoData(bundleInfo.versionName, bundleInfo.versionCode, bundleInfo.name));
        });
    } catch (err) {
      // 捕获异常并转换为业务错误类型
      const error = err as BusinessError;
      // 记录获取包信息失败错误日志
      Logger.error(TAG, `getBundleInfoForSelf failed: code ${error.code}, message ${error.message}`);
    }
  }
}
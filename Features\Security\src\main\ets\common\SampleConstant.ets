// 定义示例常量类
export class SampleConstant {
  // 定义小屏幕滚动边距
  public static SCROLL_MARGIN_SM: number = -16;
  // 定义中等屏幕滚动边距
  public static SCROLL_MARGIN_MD: number = -24;
  // 定义大屏幕滚动边距
  public static SCROLL_MARGIN_LG: number = -32;
}

// 定义示例类型枚举
export enum SampleTypeEnum {
  // 通用客户端类型
  COMMON_CLIENT = 'commonClient',
  // 可穿戴设备客户端类型
  WEARABLE_CLIENT = 'wearableClient',
  // 通用示例类型
  COMMON_SAMPLE = 'commonSample',
  // 可穿戴设备示例类型
  WEARABLE_SAMPLE = 'wearableSample',
}
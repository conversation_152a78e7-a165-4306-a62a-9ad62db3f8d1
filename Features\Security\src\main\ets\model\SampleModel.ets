// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的响应数据类型
import type { ResponseData } from '@ohos/common';
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入示例服务
import { SampleService } from '../service/SampleService';
// 导入示例数据类型
import type { SampleCardData, SampleData } from './SampleData';
// 导入示例卡片详情类型
import type { SampleCardDetail } from './SampleDetailData';

// 定义日志标签常量
const TAG = '[SampleModel]';

// 导出示例模型类
export class SampleModel {
  // 定义示例服务私有属性
  private service: SampleService = new SampleService();
  // 定义静态实例私有属性
  private static instance: SampleModel;

  // 定义私有构造函数
  private constructor() {
  }

  // 定义获取实例的静态方法
  public static getInstance(): SampleModel {
    // 如果实例不存在则创建新实例
    if (!SampleModel.instance) {
      SampleModel.instance = new SampleModel();
    }
    // 返回实例
    return SampleModel.instance;
  }

  // 定义获取示例页面的方法
  public getSamplePage(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>> {
    // 首先尝试从偏好设置获取示例页面数据
    return this.service.getSamplePageByPreference(currentPage, pageSize)
      .then((data: ResponseData<SampleData>) => {
        // 返回获取到的数据
        return data;
      }).catch((err: string) => {
        // 记录错误日志
        Logger.error(TAG,
          `getSamplePage data from network failed! try to get data form preference. ${err}`);
        // 如果偏好设置获取失败则从服务获取示例页面数据
        return this.service.getSamplePage(currentPage, pageSize)
          .then((data: ResponseData<SampleData>) => {
            // 将获取到的数据设置到偏好设置
            this.service.setSamplePageToPreference(data);
            // 返回获取到的数据
            return data;
          });
      });
  }

  // 定义获取示例列表的方法
  public getSampleList(categoryType: number, currentPage: number,
    pageSize: number): Promise<ResponseData<SampleCardData[]>> {
    // 首先尝试从偏好设置获取示例列表数据
    return this.service.getSampleListByPreference(categoryType, currentPage, pageSize)
      .then((data: ResponseData<SampleCardData[]>) => {
        // 返回获取到的数据
        return data;
      }).catch((err: string) => {
        // 记录错误日志
        Logger.error(TAG,
          `getSamplePage data from network failed! try to get data form preference. ${err}`);
        // 如果偏好设置获取失败则从服务获取示例列表数据
        return this.service.getSampleList(categoryType, currentPage, pageSize)
          .then((data: ResponseData<SampleCardData[]>) => {
            // 将获取到的数据设置到偏好设置
            this.service.setSampleListToPreference(categoryType, currentPage, pageSize, data);
            // 返回获取到的数据
            return data;
          });
      });
  }

  // 定义预加载示例页面数据的方法
  public preloadSamplePageData(): Promise<void> {
    // 通过模拟获取示例详情列表
    this.service.getSampleDetailsByMock().then((sampleDetailList: SampleCardDetail[]) => {
      // 将示例详情列表设置到偏好设置
      this.service.setSampleDetailsToPreference(sampleDetailList);
    });
    // 获取示例页面数据
    return this.service.getSamplePage()
      .then((result: ResponseData<SampleData>) => {
        // 将获取到的数据设置到偏好设置
        this.service.setSamplePageToPreference(result);
      }).catch((err: BusinessError) => {
        // 记录错误日志
        Logger.error(TAG,
          `preloadDiscoveryData data from network failed. ${err.code}, ${err.message}`);
      });
  }
}
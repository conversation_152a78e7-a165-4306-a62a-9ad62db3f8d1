// 导入ArkUI工具包中的提示操作模块，用于显示Toast提示
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器，用于记录日志信息
import { Logger } from '@ohos/common';
// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入按钮属性修改器，用于动态修改按钮属性
import { ButtonAttributeModifier } from '../viewmodel/ButtonAttributeModifier';
// 导入按钮描述器类型，用于获取按钮配置信息
import type { ButtonDescriptor } from '../viewmodel/ButtonDescriptor';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[ButtonBuilder]';

/**
 * 按钮构建器函数
 * 用于构建按钮组件的UI界面，支持点击和长按手势操作
 * @param $$ 描述器包装对象，包含按钮的配置信息
 */
@Builder
export function ButtonBuilder($$: DescriptorWrapper) {
  // 创建按钮组件，设置按钮文本
  Button($r('app.string.button_text'))
    // 设置按钮点击事件处理函数
    .onClick(() => {
      // 检查按钮描述器中的操作类型是否为点击操作
      if (($$.descriptor as ButtonDescriptor).operation === 'Click') {
        // 使用try-catch处理可能的异常
        try {
          // 显示点击操作的提示信息
          promptAction.showToast({
            // 设置提示消息内容
            message: $r('app.string.btn_click'),
            // 设置提示显示时长
            duration: DetailPageConstant.LONG_DURATION,
          });
        } catch (err) {
          // 捕获异常并转换为业务错误类型
          const error: BusinessError = err as BusinessError;
          // 记录错误日志，包含错误代码和错误消息
          Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
        }
      }
    })
    // 设置按钮手势处理，支持长按手势
    .gesture(
      // 创建长按手势，设置为可重复触发
      LongPressGesture({ repeat: true })
        // 设置手势结束时的回调函数
        .onActionEnd((_event: GestureEvent) => {
          // 检查按钮描述器中的操作类型是否为长按手势操作
          if (($$.descriptor as ButtonDescriptor).operation === 'LongGesture') {
            // 使用try-catch处理可能的异常
            try {
              // 显示长按操作的提示信息
              promptAction.showToast({
                // 设置提示消息内容
                message: $r('app.string.button_text2'),
                // 设置提示显示时长
                duration: DetailPageConstant.LONG_DURATION
              });
            } catch (err) {
              // 捕获异常并转换为业务错误类型
              const error: BusinessError = err as BusinessError;
              // 记录错误日志，包含错误代码和错误消息
              Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
            }
          }
        })
    )
    // 设置按钮属性修改器，用于动态修改按钮的样式和属性
    .attributeModifier(new ButtonAttributeModifier($$.descriptor as ButtonDescriptor))
}
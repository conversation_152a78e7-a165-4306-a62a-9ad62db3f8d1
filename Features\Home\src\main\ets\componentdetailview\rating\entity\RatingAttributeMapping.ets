// 导入通用布尔映射和数字映射类
import { CommonBoolMapping, CommonNumberMapping } from '../../common/entity/CommonMapData';

/**
 * 评分值映射数据
 * 定义评分组件的默认评分值配置
 */
export const ratingValueMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认评分值为0
  ['Default', new CommonNumberMapping('0', 0)],
]);

/**
 * 星星数量映射数据
 * 定义评分组件的默认星星总数配置
 */
export const starsMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认星星数量为5个
  ['Default', new CommonNumberMapping('5', 5)]
]);

/**
 * 指示器模式映射数据
 * 定义评分组件是否为只读指示器模式
 */
export const indicatorMapData: Map<string, CommonBoolMapping> = new Map([
  // 默认为非指示器模式（可交互）
  ['Default', new CommonBoolMapping('false', false)],
]);

/**
 * 星星样式映射数据
 * 定义评分组件的星星样式配置
 */
export const starStyleMapData: Map<string, CommonBoolMapping> = new Map([
  // 默认使用标准星星样式
  ['Default', new CommonBoolMapping('false', false)],
]);
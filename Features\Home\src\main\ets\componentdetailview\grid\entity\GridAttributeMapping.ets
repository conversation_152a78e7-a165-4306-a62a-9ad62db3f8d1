// 导入通用映射数据类，用于Grid属性的映射配置
import { CommonBoolMapping, CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

// Grid数据项数量常量，定义Grid中显示的项目总数
const DATA_SIZE = 30;

/**
 * 项目数据类
 * 用于存储Grid项目的显示信息和拖拽时的尺寸数据
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class ItemData {
  // 项目显示的文本内容
  public text: ResourceStr;
  // 项目的宽度，可以是资源引用或数值
  public width: Resource | number;
  // 项目的高度，可以是资源引用或数值
  public height: Resource | number;

  /**
   * 构造函数
   * @param text 文本内容，默认为空字符串
   * @param width 宽度，默认为0
   * @param height 高度，默认为0
   */
  constructor(text: ResourceStr = '', width: Resource | number = 0, height: Resource | number = 0) {
    this.text = text;
    this.width = width;
    this.height = height;
  }
}

/**
 * 获取Grid数据函数
 * 生成指定数量的Grid项目数据，用于填充Grid组件
 * @returns 包含所有项目标识符的字符串数组
 */
export function getData() {
  // 创建空的字符串数组
  const numbers: string[] = [];
  // 循环生成指定数量的项目数据
  for (let i = 1; i <= DATA_SIZE; i++) {
    // 添加格式化的项目标识符
    numbers.push(`item${i}`);
  }
  // 返回生成的数据数组
  return numbers;
}

/**
 * 滚动器映射类
 * 用于存储滚动器的代码字符串和实际对象的映射关系
 */
class ScrollMapping {
  // 滚动器的代码字符串表示
  public readonly code: string;
  // 滚动器的实际对象
  public readonly value: Scroller;

  /**
   * 构造函数
   * @param code 滚动器的代码字符串
   * @param value 滚动器对象实例
   */
  constructor(code: string, value: Scroller) {
    this.code = code;
    this.value = value;
  }
}

// 滚动器映射数据，用于Grid的滚动控制配置
export const scrollerMapData: Map<string, ScrollMapping> = new Map([
  ['default', new ScrollMapping('new Scroller()', new Scroller())],
]);

// 列间距映射数据，用于设置Grid列之间的间距
export const columnsGapMapData: Map<string, CommonNumberMapping> = new Map([
  ['default', new CommonNumberMapping('10', 10)],
]);

// 行间距映射数据，用于设置Grid行之间的间距
export const rowsGapMapData: Map<string, CommonNumberMapping> = new Map([
  ['default', new CommonNumberMapping('10', 10)],
]);

// 列模板映射数据，用于定义Grid的列布局模板
export const columnsTemplateMapData: Map<string, CommonStringMapping> = new Map([
  ['default', new CommonStringMapping('1fr 1fr 1fr', '1fr 1fr 1fr')],
]);

// 行模板映射数据，用于定义Grid的行布局模板
export const rowsTemplateMapData: Map<string, CommonStringMapping> = new Map([
  ['default', new CommonStringMapping('1fr 1fr 1fr', '1fr 1fr 1fr')],
]);

// 操作模式映射数据，用于控制Grid是否支持编辑操作
export const operationModeMapData: Map<string, CommonBoolMapping> = new Map([
  ['default', new CommonBoolMapping('true', true)],
]);
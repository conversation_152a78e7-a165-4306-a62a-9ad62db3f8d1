// 导入提示操作模块，用于显示Toast消息
import { promptAction } from '@kit.ArkUI';
// 导入业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具，用于记录错误信息
import { Logger } from '@ohos/common';
// 导入组件详情管理器，用于管理组件详情视图
import { ComponentDetailManager } from '../../../viewmodel/ComponentDetailManager';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

// 日志标签，用于标识PenKit构建器的日志
const TAG: string = '[PenKitBuilder]';

/**
 * PenKit构建器函数
 * 用于构建手写笔功能的按钮组件，支持系统能力检测和跳转
 * @param $$ 描述器包装对象，包含组件的配置信息
 */
@Builder
export function PenKitBuilder($$: DescriptorWrapper) {
  // 创建PenKit按钮
  Button($r('app.string.penKit'), { buttonStyle: ButtonStyleMode.NORMAL })
    // 设置字体粗细为中等
    .fontWeight(FontWeight.Medium)
    // 设置字体大小为系统大号字体
    .fontSize($r('sys.float.Body_L'))
    // 设置字体颜色为强调色
    .fontColor($r('sys.color.font_emphasize'))
    // 设置点击事件处理
    .onClick(() => {
      // 检查系统是否支持手写功能
      if (canIUse('SystemCapability.Stylus.Handwrite')) {
        // 如果支持，跳转到PenKit视图
        ComponentDetailManager.getInstance().getDetailViewModel('Penkit')?.jumpToPenKitView();
      } else {
        // 如果不支持，显示不支持的提示消息
        try {
          // 显示Toast提示
          promptAction.showToast({ message: $r('app.string.function_handwrite_not_support') });
        } catch (err) {
          // 捕获并记录错误
          const error: BusinessError = err as BusinessError;
          Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
        }
      }
    })
}
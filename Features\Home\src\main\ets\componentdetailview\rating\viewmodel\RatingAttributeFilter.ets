// 导入可观察数组类型，用于响应式数据处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';
// 导入滑块组件属性类型，用于处理滑块相关属性
import type { SliderComAttribute } from '../../../viewmodel/ComponentDetailState';

/**
 * 评分组件属性过滤器类
 * 实现通用属性过滤器接口，用于根据星星数量调整评分值的范围
 */
export class RatingAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据星星数量动态调整评分值的最大范围
   * @param attributes 属性数组，包含所有组件属性
   */
  public filter(attributes: ObservedArray<Attribute | SliderComAttribute>): void {
    // 遍历所有属性，根据属性名称进行过滤处理
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'stars':
          // 查找评分属性的索引位置
          const ratingAttributeIndex = attributes.findIndex((attribute) => {
            return attribute.name === 'rating';
          })
          // 如果找到了评分属性
          if (ratingAttributeIndex !== -1) {
            // 将星星数量设置为评分值的最大范围
            (attributes[ratingAttributeIndex] as SliderComAttribute).rightRange = Number(attribute.currentValue);
          }
          break;
        default:
          // 其他属性不做处理
          break;
      }
    });
  }
}
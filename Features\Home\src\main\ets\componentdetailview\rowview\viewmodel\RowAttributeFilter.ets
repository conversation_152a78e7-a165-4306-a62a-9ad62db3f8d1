// 导入可观察数组类型，用于响应式数据处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 行布局属性过滤器类
 * 实现通用属性过滤器接口，用于根据内边距和对齐方式控制相关属性的启用状态
 */
export class RowAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据内边距类型和对齐方式控制相关属性的启用状态
   * @param attributes 属性数组，包含所有组件属性
   */
  filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，根据属性名称进行过滤处理
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'padding':
          // 查找内边距数值属性的索引位置
          const paddingNumIndex = attributes.findIndex((item) => item.name === 'paddingNum');
          // 查找弹性对齐属性的索引位置
          const flexAlignIndex = attributes.findIndex((item) => item.name === 'flexAlign');
          // 如果找到了内边距数值和弹性对齐属性
          if (paddingNumIndex !== -1 && flexAlignIndex !== -1) {
            // 如果内边距类型为None，禁用内边距数值，启用弹性对齐
            if (attribute.currentValue === 'None') {
              attributes[paddingNumIndex].enable = false;
              attributes[flexAlignIndex].enable = true;
            } else {
              // 如果内边距类型不为None，启用内边距数值，禁用弹性对齐
              attributes[paddingNumIndex].enable = true;
              attributes[flexAlignIndex].enable = false;
            }
          }
          break;
        case 'flexAlign':
          // 查找间距属性的索引位置
          const index = attributes.findIndex((item) => item.name === 'space');
          // 如果找到了间距属性
          if (index !== -1) {
            // 当对齐方式为SpaceBetween时禁用间距属性，其他情况启用
            attributes[index].enable = (attribute.currentValue !== 'SpaceBetween');
          }
          break;
        default:
          // 其他属性不做处理
          break;
      }
    });
  }
}
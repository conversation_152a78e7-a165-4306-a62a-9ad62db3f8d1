// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入文本描述器类型，用于描述文本组件配置
import type { TextDescriptor } from '../viewmodel/TextDescriptor';
// 导入文本属性修改器，用于修改文本组件属性
import { TextAttributeModifier } from '../viewmodel/TextAttributeModifier';

/**
 * 文本组件构建器函数
 * 用于构建文本组件，显示指定的文本内容
 * @param $$ 描述器包装对象，包含文本组件配置信息
 */
@Builder
export function TextBuilder($$: DescriptorWrapper) {
  // 创建文本组件，显示应用资源中的文本内容
  Text($r('app.string.textview_text'))
    // 设置属性修改器，用于动态修改文本组件属性
    .attributeModifier(new TextAttributeModifier($$.descriptor as TextDescriptor))
}
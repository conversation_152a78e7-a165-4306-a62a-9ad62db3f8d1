// 导入ArkUI工具包中的提示操作模块，用于显示Toast提示
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器，用于记录日志信息
import { Logger } from '@ohos/common';
// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入警告对话框描述器类型，用于获取对话框配置信息
import type { AlertDialogDescriptor } from '../viewmodel/AlertDialogDescriptor';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[AlertDialogBuilder]';

/**
 * 警告对话框构建器函数
 * 用于构建警告对话框组件的UI界面
 * @param $$ 描述器包装对象，包含警告对话框的配置信息
 */
@Builder
export function AlertDialogBuilder($$: DescriptorWrapper) {
  // 创建垂直列布局，设置子组件间距为小间距
  Column({ space: DetailPageConstant.SPACE_SMALL }) {
    // 创建触发警告对话框的按钮
    Button($r('app.string.alert_dialog_tip'))
      // 设置按钮样式为普通模式
      .buttonStyle(ButtonStyleMode.NORMAL)
      // 设置按钮字体粗细为中等
      .fontWeight(FontWeight.Medium)
      // 设置按钮字体大小
      .fontSize($r('sys.float.Body_L'))
      // 设置按钮字体颜色为强调色
      .fontColor($r('sys.color.font_emphasize'))
      // 设置按钮点击事件处理函数
      .onClick(() => {
        // 显示警告对话框
        AlertDialog.show(
          {
            // 设置对话框标题
            title: $r('app.string.title'),
            // 设置对话框副标题
            subtitle: $r('app.string.subtitle'),
            // 设置对话框消息内容
            message: $r('app.string.content'),
            // 设置是否允许自动取消（点击外部区域关闭）
            autoCancel: true,
            // 从描述器中获取对话框对齐方式
            alignment: ($$.descriptor as AlertDialogDescriptor).alertDialogAlignment,
            // 设置按钮排列方向为水平排列
            buttonDirection: DialogButtonDirection.HORIZONTAL,
            // 定义对话框按钮数组
            buttons: [
              // 第一个按钮配置
              {
                // 设置按钮文本
                value: $r('app.string.button_one'),
                // 设置按钮点击事件处理函数
                action: () => {
                  // 使用try-catch处理可能的异常
                  try {
                    // 显示第一个按钮被点击的提示信息
                    promptAction.showToast({
                      // 设置提示消息内容
                      message: 'Callback when button1 is clicked',
                      // 设置提示显示时长
                      duration: DetailPageConstant.LONG_DURATION,
                    });
                  } catch (err) {
                    // 捕获异常并转换为业务错误类型
                    const error: BusinessError = err as BusinessError;
                    // 记录错误日志，包含错误代码和错误消息
                    Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
                  }
                },
              },
              // 第二个按钮配置
              {
                // 设置按钮文本
                value: $r('app.string.button_two'),
                // 设置按钮点击事件处理函数
                action: () => {
                  // 使用try-catch处理可能的异常
                  try {
                    // 显示第二个按钮被点击的提示信息
                    promptAction.showToast({
                      // 设置提示消息内容
                      message: 'Callback when button2 is clicked',
                      // 设置提示显示时长
                      duration: DetailPageConstant.LONG_DURATION,
                    });
                  } catch (err) {
                    // 捕获异常并转换为业务错误类型
                    const error: BusinessError = err as BusinessError;
                    // 记录错误日志，包含错误代码和错误消息
                    Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
                  }
                },
              }
            ],
            // 设置对话框取消回调函数
            cancel: () => {
              // 记录对话框关闭的日志信息
              Logger.info(TAG, 'Closed callbacks');
            },
            // 设置对话框即将关闭时的回调函数
            onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
              // 检查关闭原因是否为按返回键
              if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
                // 执行关闭操作
                dismissDialogAction.dismiss();
              }
              // 检查关闭原因是否为点击外部区域
              if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
                // 执行关闭操作
                dismissDialogAction.dismiss();
              }
            },
          });
      })
  }
}
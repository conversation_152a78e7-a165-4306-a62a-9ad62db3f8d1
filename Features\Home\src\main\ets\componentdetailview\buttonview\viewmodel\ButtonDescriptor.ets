// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入按钮相关的映射数据，包含各种属性的映射关系
import {
  buttonActionMap,
  buttonBgColorMap,
  buttonTypeMapData,
  sizeMapData,
  styleMapData,
} from '../entity/ButtonAttributeMapping';
// 导入通用描述器基类，用于继承基础描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';

/**
 * 按钮描述器类
 * 继承通用描述器，用于描述按钮组件的属性和行为
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class ButtonDescriptor extends CommonDescriptor {
  // 按钮样式属性，控制按钮的外观样式
  public buttonStyle: ButtonStyleMode = styleMapData.get('Default')!.value;
  // 控件尺寸属性，控制按钮的大小
  public controlSize: ControlSize = sizeMapData.get('Default')!.value;
  // 按钮类型属性，控制按钮的形状类型
  public buttonType: ButtonType = buttonTypeMapData.get('Default')!.value;
  // 背景颜色属性，控制按钮的背景颜色
  public backgroundColor: ResourceColor = buttonBgColorMap.get('Default')!.value;
  // 操作类型属性，控制按钮的交互行为
  public operation: string = buttonActionMap.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为按钮描述器的具体属性值
   * @param attributes 原始属性数组，包含组件的各种配置属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理按钮样式属性
        case 'buttonStyle':
          // 从映射数据中获取对应的样式枚举值，如果不存在则使用默认值
          this.buttonStyle = styleMapData.get(attribute.currentValue)?.value ?? styleMapData.get('Default')!.value;
          break;
        // 处理控件尺寸属性
        case 'controlSize':
          // 从映射数据中获取对应的尺寸枚举值，如果不存在则使用默认值
          this.controlSize = sizeMapData.get(attribute.currentValue)?.value ?? sizeMapData.get('Default')!.value;
          break;
        // 处理按钮类型属性
        case 'buttonType':
          // 从映射数据中获取对应的类型枚举值，如果不存在则使用默认值
          this.buttonType =
            buttonTypeMapData.get(attribute.currentValue)?.value ?? buttonTypeMapData.get('Default')!.value;
          break;
        // 处理背景颜色属性
        case 'backgroundColor':
          // 根据按钮样式决定背景颜色的处理方式
          if (this.buttonStyle === ButtonStyleMode.EMPHASIZED) {
            // 强调样式使用自定义颜色
            this.backgroundColor = attribute.currentValue;
          } else if (this.buttonStyle === ButtonStyleMode.NORMAL) {
            // 普通样式使用系统颜色
            this.backgroundColor = $r('sys.color.comp_background_tertiary');
          } else {
            // 文本样式使用透明背景
            this.backgroundColor = Color.Transparent;
          }
          break;
        // 处理操作类型属性
        case 'operation':
          // 直接使用属性的当前值
          this.operation = attribute.currentValue;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
// 导入相机选择器模块，用于定义媒体类型
import { cameraPicker } from '@kit.CameraKit';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../../viewmodel/CommonDescriptor';
// 导入相机选择器媒体类型映射数据
import { pickerMediaType } from '../entity/CameraPickerMapping';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';

/**
 * 相机选择器描述器类
 * 继承通用描述器，专门用于描述相机选择器组件的配置
 * 包含媒体类型的配置信息
 */
@Observed
export class CameraPickerDescriptor extends CommonDescriptor {
  // 媒体类型数组，默认使用默认媒体类型
  public mediaTypes: cameraPicker.PickerMediaType[] = pickerMediaType.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为相机选择器的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'mediaTypes':
          // 设置媒体类型，如果找不到对应值则使用默认值
          this.mediaTypes =
            pickerMediaType.get(attribute.currentValue)?.value ?? pickerMediaType.get('Default')!.value;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    })
  }
}
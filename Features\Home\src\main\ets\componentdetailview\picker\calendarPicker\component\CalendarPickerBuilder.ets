// 导入详情页面常量，用于获取配置参数
import { DetailPageConstant } from '../../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../../viewmodel/DescriptorWrapper';
// 导入日历选择器属性修改器，用于修改组件属性
import { CalendarPickerAttributeModifier } from '../viewmodel/CalendarPickerAttributeModifier';
// 导入日历选择器描述器类型，用于描述组件配置
import type { CalendarPickerDescriptor } from '../viewmodel/CalendarPickerDescriptor';

/**
 * 日历选择器构建器函数
 * 用于构建日历选择器组件，提供日期选择功能
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function CalendarPickerBuilder($$: DescriptorWrapper) {
  // 创建列布局容器
  Column() {
    // 创建日历选择器组件
    CalendarPicker({ hintRadius: DetailPageConstant.DATE_HINT_RADIUS })
      // 设置组件宽度为30%
      .width('30%')
      // 应用属性修改器，根据描述器配置组件属性
      .attributeModifier(new CalendarPickerAttributeModifier($$.descriptor as CalendarPickerDescriptor))
  }
  // 设置列布局垂直对齐方式为居中
  .justifyContent(FlexAlign.Center)
  // 设置列布局水平对齐方式为居中
  .alignItems(HorizontalAlign.Center)
  // 设置列布局宽度为100%
  .width('100%')
}
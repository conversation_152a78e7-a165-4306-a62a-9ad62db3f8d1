// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的响应数据类型
import type { ResponseData } from '@ohos/common';
// 导入通用模块中的日志记录器、模拟请求和偏好设置管理器
import { Logger, MockRequest, PreferenceManager } from '@ohos/common';
// 导入示例数据类型
import type { SampleCardData, SampleCategory, SampleData } from '../model/SampleData';
// 导入示例详情数据类型
import type { SampleCardDetail, SingleSampleData } from '../model/SampleDetailData';

// 定义日志标签常量
const TAG: string = '[SampleService]';

// 导出示例服务类
export class SampleService {
  // 定义公共构造函数
  public constructor() {
  }

  // 定义通过模拟获取示例列表的方法
  public getSampleListByMock(): Promise<ResponseData<SampleData>> {
    // 返回Promise对象
    return new Promise((resolve: (value: ResponseData<SampleData>) => void,
      reject: (reason?: BusinessError) => void) => {
      // 调用模拟请求获取示例页面数据
      MockRequest.call<ResponseData<SampleData>>(SampleTrigger.SAMPLE_PAGE)
        .then((result: Object) => {
          // 解析结果并返回
          resolve(result as ResponseData<SampleData>);
        })
        .catch((error: BusinessError) => {
          // 捕获错误并拒绝
          reject(error);
        });
    });
  }

  // 定义通过模拟获取示例详情的方法
  public getSampleDetailsByMock(): Promise<SampleCardDetail[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: SampleCardDetail[]) => void,
      reject: (reason?: BusinessError) => void) => {
      // 调用模拟请求获取所有示例详情数据
      MockRequest.call<SampleCardDetail[]>(SampleTrigger.SAMPLE_DETAILS_ALL)
        .then((result: SampleCardDetail[]) => {
          // 解析结果并返回
          resolve(result as SampleCardDetail[]);
        })
        .catch((error: BusinessError) => {
          // 捕获错误并拒绝
          reject(error);
        });
    });
  }

  // 定义获取示例页面的方法
  public getSamplePage(currentPage?: number, pageSize?: number): Promise<ResponseData<SampleData>> {
    // 记录日志信息
    Logger.info(TAG, `getComponentList param: currentPage ${currentPage}, pageSize: ${pageSize} `);
    // 调用通过模拟获取示例列表的方法
    return this.getSampleListByMock();
  }

  // 定义通过偏好设置获取示例页面的方法
  public getSamplePageByPreference(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>> {
    // 返回Promise对象
    return new Promise((resolve: (value: ResponseData<SampleData>) => void,
      reject: (reason?: string) => void) => {
      // 从偏好设置管理器获取示例页面数据
      PreferenceManager.getInstance()
        .getValue<Record<string, ResponseData<SampleData>>>(SampleTrigger.SAMPLE_PAGE)
        .then((resp) => {
          // 如果没有响应数据则拒绝
          if (!resp) {
            reject('There is no data in the Preference');
          }
          // 转换响应数据类型
          resp = (resp as Record<string, ResponseData<SampleData>>);
          // 根据当前页码和页面大小获取数据
          const ret = resp[`${currentPage}_${pageSize}`];
          // 如果没有找到数据则拒绝
          if (!ret) {
            reject('There is no data in the Preference');
          }
          // 返回找到的数据
          resolve(ret);
        });
    });
  }

  // 定义设置示例页面到偏好设置的方法
  public setSamplePageToPreference(data: ResponseData<SampleData>): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置管理器是否有示例页面数据
      PreferenceManager.getInstance().hasValue(SampleTrigger.SAMPLE_PAGE)
        .then((result) => {
          // 如果已有数据
          if (result) {
            // 获取现有数据
            PreferenceManager.getInstance()
              .getValue<Record<string, ResponseData<SampleData>>>(SampleTrigger.SAMPLE_PAGE)
              .then((resp) => {
                // 转换响应数据类型
                resp = (resp as Record<string, ResponseData<SampleData>>);
                // 设置新数据
                resp[`${data.currentPage}_${data.pageSize}`] = data;
                // 保存到偏好设置
                PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_PAGE, resp);
                // 完成操作
                resolve();
              });
          } else {
            // 创建新的记录对象
            const record: Record<string, ResponseData<SampleData>> = {};
            // 设置数据
            record[`${data.currentPage}_${data.pageSize}`] = data;
            // 保存到偏好设置
            PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_PAGE, record);
          }
        });
    });
  }

  // 定义获取示例列表的方法
  public getSampleList(categoryType: number, currentPage: number,
    pageSize: number): Promise<ResponseData<SampleCardData[]>> {
    // 返回Promise对象
    return new Promise((resolve: (value: ResponseData<SampleCardData[]>) => void,
      reject: (reason?: Object) => void) => {
      // 通过模拟获取示例列表数据
      this.getSampleListByMock().then((result: ResponseData<SampleData>) => {
        // 获取示例分类列表
        const sampleCategoryList = result.data.sampleCategories;
        // 根据分类类型查找对应的分类数据
        const categoryData =
          sampleCategoryList.find((category: SampleCategory) => category.categoryType === categoryType);
        // 创建响应数据对象
        const response: ResponseData<SampleCardData[]> = {
          currentPage,
          pageSize,
          totalSize: 0,
          data: [],
        };
        // 如果找到分类数据
        if (categoryData) {
          // 设置总数量
          response.totalSize = categoryData.sampleCards.length;
          // 设置数据
          response.data = categoryData.sampleCards;
        }
        // 返回响应数据
        resolve(response);
      }).catch(() => {
        // 捕获错误并拒绝
        reject();
      });
    });
  }

  // 定义通过偏好设置获取示例列表的方法
  public getSampleListByPreference(categoryType: number, currentPage: number,
    pageSize: number): Promise<ResponseData<SampleCardData[]>> {
    // 返回Promise对象
    return new Promise((resolve: (value: ResponseData<SampleCardData[]>) => void,
      reject: (reason?: string) => void) => {
      // 从偏好设置管理器获取示例列表数据
      PreferenceManager.getInstance()
        .getValue<Record<string, ResponseData<SampleCardData[]>>>(SampleTrigger.SAMPLE_LIST)
        .then((resp) => {
          // 如果没有响应数据则拒绝
          if (!resp) {
            reject('There is no data in the Preference');
          }
          // 转换响应数据类型
          resp = (resp as Record<string, ResponseData<SampleCardData[]>>);
          // 根据分类类型、当前页码和页面大小获取数据
          const ret = resp[`${categoryType}_${currentPage}_${pageSize}`];
          // 如果没有找到数据则拒绝
          if (!ret) {
            reject('There is no data in the Preference');
          }
          // 返回找到的数据
          resolve(ret);
        })
    })
  }

  // 定义设置示例列表到偏好设置的方法
  public setSampleListToPreference(categoryType: number, currentPage: number,
    pageSize: number, data: ResponseData<SampleCardData[]>): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置管理器是否有示例列表数据
      PreferenceManager.getInstance().hasValue(SampleTrigger.SAMPLE_LIST)
        .then((result) => {
          // 如果已有数据
          if (result) {
            // 获取现有数据
            PreferenceManager.getInstance()
              .getValue<Record<string, ResponseData<SampleCardData[]>>>(SampleTrigger.SAMPLE_LIST)
              .then((resp) => {
                // 转换响应数据类型
                resp = (resp as Record<string, ResponseData<SampleCardData[]>>);
                // 设置新数据
                resp[`${categoryType}_${currentPage}_${pageSize}`] = data;
                // 保存到偏好设置
                PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_LIST, resp);
                // 完成操作
                resolve();
              });
          } else {
            // 创建新的记录对象
            const record: Record<string, ResponseData<SampleCardData[]>> = {};
            // 设置数据
            record[`${categoryType}_${currentPage}_${pageSize}`] = data;
            // 保存到偏好设置
            PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_LIST, record);
          }
        });
    });
  }

  // 定义获取示例详情的方法
  public getSampleDetails(sampleCardId: number): Promise<SingleSampleData[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: SingleSampleData[]) => void, reject: (reason?: Object) => void) => {
      // 通过模拟获取示例详情数据
      this.getSampleDetailsByMock().then((result: SampleCardDetail[]) => {
        // 根据示例卡片ID查找对应的卡片详情
        const cardDetail = result.find((item: SampleCardDetail) => item.id === sampleCardId);
        // 如果找到卡片详情
        if (cardDetail) {
          // 返回示例详情数据
          resolve(cardDetail.sampleDetail);
        } else {
          // 拒绝并返回错误信息
          reject(`Cann't find this Sample Card. id: ${sampleCardId}`);
        }
      }).catch((error: BusinessError) => {
        // 捕获错误并拒绝
        reject(error);
      });
    });
  }

  // 定义通过偏好设置获取示例详情的方法
  public getSampleDetailsByPreference(sampleCardId: number): Promise<SingleSampleData[]> {
    // 返回Promise对象
    return new Promise((resolve: (value: SingleSampleData[]) => void,
      reject: (reason?: string) => void) => {
      // 从偏好设置管理器获取示例详情数据
      PreferenceManager.getInstance()
        .getValue<Record<string, SingleSampleData[]>>(SampleTrigger.SAMPLE_DETAILS)
        .then((resp) => {
          // 如果没有响应数据则拒绝
          if (!resp) {
            reject('There is no data in the Preference');
          }
          // 转换响应数据类型
          resp = (resp as Record<string, SingleSampleData[]>);
          // 根据示例卡片ID获取数据
          const ret = resp[`SampleCardDetail_${sampleCardId}`];
          // 如果没有找到数据则拒绝
          if (!ret) {
            reject('There is no data in the Preference');
          }
          // 返回找到的数据
          resolve(ret);
        });
    });
  }

  // 定义设置示例详情列表到偏好设置的方法
  public setSampleDetailsToPreference(data: SampleCardDetail[]): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置管理器是否有示例详情数据
      PreferenceManager.getInstance().hasValue(SampleTrigger.SAMPLE_DETAILS)
        .then((result) => {
          // 如果已有数据
          if (result) {
            // 获取现有数据
            PreferenceManager.getInstance()
              .getValue<Record<string, SingleSampleData[]>>(SampleTrigger.SAMPLE_DETAILS)
              .then((resp) => {
                // 转换响应数据类型或创建空对象
                const res: Record<string, SingleSampleData[]> = (resp as Record<string, SingleSampleData[]>) || {};
                // 遍历数据数组
                data.forEach((item: SampleCardDetail) => {
                  // 设置每个示例卡片详情的数据
                  res[`SampleCardDetail_${item.id}`] = item.sampleDetail;
                });
                // 保存到偏好设置
                PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_DETAILS, res);
                // 完成操作
                resolve();
              });
          } else {
            // 创建新的记录对象
            const record: Record<string, SingleSampleData[]> = {};
            // 遍历数据数组
            data.forEach((item: SampleCardDetail) => {
              // 设置每个示例卡片详情的数据
              record[`SampleCardDetail_${item.id}`] = item.sampleDetail;
            });
            // 保存到偏好设置
            PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_DETAILS, record);
          }
        });
    });
  }

  // 定义设置单个示例详情到偏好设置的方法
  public setSampleDetailToPreference(sampleCardId: number, data: SingleSampleData[]): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置管理器是否有示例详情数据
      PreferenceManager.getInstance().hasValue(SampleTrigger.SAMPLE_DETAILS)
        .then((result) => {
          // 如果已有数据
          if (result) {
            // 获取现有数据
            PreferenceManager.getInstance()
              .getValue<Record<string, SingleSampleData[]>>(SampleTrigger.SAMPLE_DETAILS)
              .then((resp) => {
                // 转换响应数据类型或创建空对象
                const res: Record<string, SingleSampleData[]> = (resp as Record<string, SingleSampleData[]>) || {};
                // 设置指定示例卡片ID的数据
                res[`SampleCardDetail_${sampleCardId}`] = data;
                // 保存到偏好设置
                PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_DETAILS, res);
                // 完成操作
                resolve();
              });
          } else {
            // 创建新的记录对象
            const record: Record<string, SingleSampleData[]> = {};
            // 设置指定示例卡片ID的数据
            record[`SampleCardDetail_${sampleCardId}`] = data;
            // 保存到偏好设置
            PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_DETAILS, record);
          }
        });
    });
  }
}

// 定义示例触发器枚举
enum SampleTrigger {
  // 示例页面触发器
  SAMPLE_PAGE = 'sample-page',
  // 示例列表触发器
  SAMPLE_LIST = 'sample-list',
  // 示例详情触发器
  SAMPLE_DETAILS = 'sample-details',
  // 所有示例详情触发器
  SAMPLE_DETAILS_ALL = 'sample-details-all',
}
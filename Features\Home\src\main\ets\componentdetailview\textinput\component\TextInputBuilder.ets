// 导入方舟UI工具包中的窗口模块，用于监听软键盘状态
import { window } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型和日志记录器
import { GlobalInfoModel, Logger } from '@ohos/common';
// 导入详情页常量，包含动画持续时间等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入组件详情管理器，用于管理组件状态和事件
import { ComponentDetailManager } from '../../../viewmodel/ComponentDetailManager';
// 导入属性变更启用枚举，用于控制属性变更
import { AttributeChangeEnable } from '../../../viewmodel/ComponentDetailPageVM';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入文本输入属性修改器，用于修改文本输入组件属性
import { TextInputAttributeModifier } from '../viewmodel/TextInputAttributeModifier';
// 导入文本输入描述器类型，用于描述文本输入组件配置
import type { TextInputDescriptor } from '../viewmodel/TextInputDescriptor';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[TextInputBuilder]';

@Builder
export function TextInputBuilder($$: DescriptorWrapper) {
  TextInputComponent({ textInputComponentDescriptor: $$.descriptor as TextInputDescriptor })
}

@Component
struct TextInputComponent {
  @Prop textInputComponentDescriptor: TextInputDescriptor;
  @State isText: boolean = false;
  @State textInputHeight: number = 0;
  @State contentOffset: number = 0;

  aboutToAppear(): void {
    ComponentDetailManager.getInstance()
      .getDetailViewModel('TextInput')?.sendEvent(new AttributeChangeEnable('fontColor', this.isText));
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
    let keyboardHeight: number = 0;

    window.getLastWindow(getContext(this)).then(currentWindow => {
      // Monitor the appearance and disappearance of the soft keyboard.
      try {
        currentWindow.on('avoidAreaChange', async data => {
          if (data.type !== window.AvoidAreaType.TYPE_KEYBOARD) {
            return;
          }
          keyboardHeight = px2vp(data.area.bottomRect.height);
          /* When the size of the component preview area exceeds half the screen, the keyboard
           will cover the preview area. At this time, the component needs to move up when the keyboard appears.
           */
          if (keyboardHeight > 0 && this.textInputHeight / globalInfoModel.deviceHeight > 0.5) {
            this.contentOffset = keyboardHeight / 2;
          } else {
            this.contentOffset = 0;
          }
        });
      } catch (err) {
        const error: BusinessError = err as BusinessError;
        Logger.error(TAG, `CurrentWindow invoke error, the code is ${error.message}, the message is ${error.message}`);
      }
    });
  }

  handleAttributeChange() {
    ComponentDetailManager.getInstance()
      .getDetailViewModel('TextInput')?.sendEvent(new AttributeChangeEnable('fontColor', !this.isText));
    ComponentDetailManager.getInstance()
      .getDetailViewModel('TextInput')?.sendEvent(new AttributeChangeEnable('placeholderFont', this.isText));
    this.isText = !this.isText;
  }

  build() {
    Column() {
      TextInput({ placeholder: $r('app.string.text_placeholder') })
        .margin({
          left: $r('app.float.common_left_right_margin'),
          right: $r('app.float.common_left_right_margin'),
          bottom: this.contentOffset,
        })
        .height($r('app.float.common_component_height'))
        .onChange((value: string) => {
          if ((value.trim().length === 0 && this.isText) || (value.trim().length !== 0 && !this.isText)) {
            this.handleAttributeChange();
          }
        })
        .fontColor(this.textInputComponentDescriptor.fontColor)
        .fontSize($r('sys.float.Body_L'))
        .fontWeight(FontWeight.Regular)
        .enterKeyType(EnterKeyType.Done)
        .borderRadius($r('sys.float.corner_radius_level12'))
        .backgroundColor($r('sys.color.comp_background_tertiary'))
        .caretStyle({ color: $r('sys.color.font_emphasize') })
        .placeholderFont(this.textInputComponentDescriptor.placeholderFont)
        .attributeModifier(new TextInputAttributeModifier(this.textInputComponentDescriptor))
        .animation({
          duration: DetailPageConstant.UP_DURATION,
          curve: Curve.Linear,
          playMode: PlayMode.Normal,
        })
    }
    .height('100%')
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .onAreaChange((_: Area, newArea: Area) => {
      this.textInputHeight = Number(newArea.height);
    })
  }
}
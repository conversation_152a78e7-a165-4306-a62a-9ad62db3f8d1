// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入进度条描述器类型，用于描述组件配置
import type { ProgressDescriptor } from './ProgressDescriptor';

/**
 * 进度条属性修改器类
 * 继承通用属性修改器，专门用于修改进度条组件的属性
 * 支持颜色和样式等属性的动态修改
 */
@Observed
export class ProgressAttributeModifier extends CommonAttributeModifier<ProgressDescriptor, ProgressAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性值应用到进度条实例上
   * @param instance 进度条属性实例
   */
  applyNormalAttribute(instance: ProgressAttribute): void {
    // 分配颜色属性，从描述器获取颜色配置并应用到实例
    this.assignAttribute((descriptor => descriptor.color), (val) => instance.color(val));
    // 分配样式属性，从描述器获取样式配置并应用到实例
    this.assignAttribute((descriptor => descriptor.style), (val) => instance.style(val));
  }
}

// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的产品系列枚举
import { ProductSeriesEnum } from '@ohos/common';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';

// 使用Component装饰器定义体验项目组件
@Component
export struct ExperienceItem {
  // 使用Prop装饰器定义发现内容属性
  @Prop discoverContent: DiscoverContent;
  // 使用State装饰器定义背景顶部颜色状态
  @State bgTopColor: string = '';
  // 使用State装饰器定义背景底部颜色状态
  @State bgBottomColor: string = '';

  // 定义构建方法
  build() {
    // 创建堆栈布局，内容对齐到底部
    Stack({ alignContent: Alignment.Bottom }) {
      // 创建图片组件
      Image($rawfile(this.discoverContent.mediaUrl))
        // 设置占位图片
        .alt($r('app.media.img_placeholder'))
        // 设置对象适应方式为覆盖
        .objectFit(ImageFit.Cover)
        // 禁用拖拽
        .draggable(false)
        // 设置宽度为100%
        .width('100%')
        // 设置高度为100%
        .height('100%')
      // 创建列布局
      Column() {
        // 创建副标题文本
        Text(this.discoverContent.subTitle)
          // 设置字体颜色为主要字体色
          .fontColor($r('sys.color.font_on_primary'))
          // 设置字体大小为Body_S
          .fontSize($r('sys.float.Body_S'))
          // 设置字体粗细为常规
          .fontWeight(FontWeight.Regular)
        // 创建标题文本
        Text(this.discoverContent.title)
          // 设置边距
          .margin({ top: $r('sys.float.padding_level3'), bottom: $r('sys.float.padding_level2') })
          // 设置字体颜色为主要字体色
          .fontColor($r('sys.color.font_on_primary'))
          // 设置字体大小为Title_S
          .fontSize($r('sys.float.Title_S'))
          // 设置字体粗细为粗体
          .fontWeight(FontWeight.Bold)
        // 创建描述文本
        Text(this.discoverContent.desc)
          // 设置字体颜色为次要字体色
          .fontColor($r('sys.color.font_on_secondary'))
          // 设置最大行数为2
          .maxLines(2)
          // 设置文本溢出处理为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置字体大小为Body_M
          .fontSize($r('sys.float.Body_M'))
          // 设置字体粗细为常规
          .fontWeight(FontWeight.Regular)
      }
      // 设置内边距
      .padding({
        left: $r('sys.float.padding_level8'),
        right: $r('sys.float.padding_level8'),
        bottom: $r('sys.float.padding_level8'),
        top: $r('sys.float.padding_level6'),
      })
      // 设置高度
      .height($r('app.float.img_card_content_height'))
      // 设置宽度为100%
      .width('100%')
      // 设置主轴对齐方式为末端对齐
      .justifyContent(FlexAlign.End)
      // 设置交叉轴对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
    }
    // 设置点击效果为重度
    .clickEffect({ level: ClickEffectLevel.HEAVY })
    // 根据产品系列设置高度
    .height(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('app.float.img_card_height_verde') :
    $r('app.float.img_card_height'))
    // 设置背景颜色为列表卡片背景色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 启用裁剪
    .clip(true)
  }
}
// 导入通用数字映射和字符串映射类，用于处理基础数据类型的映射
import { CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

/**
 * 列对齐映射类
 * 用于存储列对齐方式的代码字符串和实际枚举值的映射关系
 */
class ColumnAlignMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 水平对齐枚举值，只读属性
  public readonly value: HorizontalAlign;

  /**
   * 构造函数
   * @param code 代码字符串，表示水平对齐的代码形式
   * @param value 水平对齐枚举值
   */
  constructor(code: string, value: HorizontalAlign) {
    // 初始化代码字符串
    this.code = code;
    // 初始化水平对齐枚举值
    this.value = value;
  }
}

// 导出列对齐映射数据，包含所有可用的对齐方式选项
export const columnAlignMapData: Map<string, ColumnAlignMapping> = new Map([
  // 起始对齐，子元素靠左对齐
  ['Start', new ColumnAlignMapping('HorizontalAlign.Start', HorizontalAlign.Start)],
  // 居中对齐，子元素水平居中
  ['Center', new ColumnAlignMapping('HorizontalAlign.Center', HorizontalAlign.Center)],
  // 结束对齐，子元素靠右对齐
  ['End', new ColumnAlignMapping('HorizontalAlign.End', HorizontalAlign.End)],
  // 默认对齐方式，使用居中对齐
  ['Default', new ColumnAlignMapping('HorizontalAlign.Center', HorizontalAlign.Center)],
]);

// 导出列间距映射数据，包含子元素之间的间距配置
export const columnSpaceMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认间距，设置为3个单位
  ['Default', new CommonNumberMapping('3', 3)],
]);

// 导出列内边距映射数据，包含所有可用的内边距类型选项
export const columnPaddingMapData: Map<string, CommonStringMapping> = new Map([
  // 垂直内边距，只设置上下边距
  ['Vertical', new CommonStringMapping('Vertical', 'Vertical')],
  // 水平内边距，只设置左右边距
  ['Horizontal', new CommonStringMapping('Horizontal', 'Horizontal')],
  // 全部内边距，设置四个方向的边距
  ['All', new CommonStringMapping('All', 'All')],
  // 无内边距，不设置任何边距
  ['None', new CommonStringMapping('None', 'None')],
  // 默认内边距类型，使用全部内边距
  ['Default', new CommonStringMapping('All', 'All')],
]);

// 导出内边距数值映射数据，包含内边距的具体数值配置
export const paddingNumMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认内边距数值，设置为3个单位
  ['Default', new CommonNumberMapping('3', 3)],
]);
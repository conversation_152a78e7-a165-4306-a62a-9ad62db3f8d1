// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * AI图像代码生成器类
 * 实现通用代码生成器接口，用于生成AI图像分析相关的代码
 */
export class AIImageCodeGenerator implements CommonCodeGenerator {
  /**
   * 生成AI图像组件代码方法
   * 生成包含AI图像分析功能的完整组件代码字符串
   * @param _attributes 原始属性数组（此组件不需要动态属性配置）
   * @returns 生成的AI图像组件代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 返回生成的完整AI图像组件代码字符串
    return `// 导入图像工具包，用于处理图像相关操作
import { image } from '@kit.ImageKit';

// AI图像组件，实现图像AI分析功能
@Component
export struct AIImageComponent {
  // 图像像素图状态，存储当前显示的图像数据
  @State imagePixelMap?: image.PixelMap = undefined;

  // 组件即将出现时的生命周期方法
  async aboutToAppear() {
    // 图片资源替换项目src/main/resources/base/media路径下资源
    this.imagePixelMap = await this.getPixmapFromMedia($r("app.media.image_ai"));
  }

  // 组件即将消失时的生命周期方法
  aboutToDisappear(): void {
    // 释放像素图资源，防止内存泄漏
    this.imagePixelMap?.release();
  }

  // 构建组件UI方法
  build() {
    Column() {
      // 使用Stack布局，内容居中对齐
      Stack({ alignContent: Alignment.Center }) {
        // 图片资源替换项目src/main/resources/base/media路径下资源
        Image(this.imagePixelMap ?? $r("app.media.image_ai"))
          // 启用AI分析器功能
          .enableAnalyzer(true)
          .width('100%')
          .height('100%')
          // 设置图像适配方式为包含
          .objectFit(ImageFit.Contain)
          // 设置圆角边框
          .borderRadius($r('sys.float.corner_radius_level8'))
          // 禁用拖拽功能
          .draggable(false)
      }
      .width('80%')
    }
    // 设置垂直居中对齐
    .justifyContent(FlexAlign.Center)
    // 设置水平居中对齐
    .alignItems(HorizontalAlign.Center)
    .width('100%')
    .height('100%')
  }

  // 从媒体资源获取像素图的私有方法
  private async getPixmapFromMedia(resource: Resource) {
    // 声明像素图变量
    let createPixelMap: image.PixelMap;
    try {
      // 从资源管理器获取媒体内容的字节数组
      const unit8Array = await getContext(this)?.resourceManager?.getMediaContent({
        bundleName: resource.bundleName,
        moduleName: resource.moduleName,
        id: resource.id,
      });
      // 创建图像源对象
      const imageSource = image.createImageSource(unit8Array.buffer.slice(0, unit8Array.buffer.byteLength));
      // 创建像素图，指定RGBA_8888格式
      createPixelMap = await imageSource.createPixelMap({
        desiredPixelFormat: image.PixelMapFormat.RGBA_8888,
      });
      // 释放图像源资源
      await imageSource.release();
      // 释放之前的像素图资源
      this.imagePixelMap?.release();
    } catch (err) {
      // 捕获并处理错误
      const error: BusinessError = err as BusinessError;
      // 记录错误日志
      console.error(\`Get pixmapFromMedia error, the code is \${error.code}, the message is \${error.message}\`);
      return;
    }
    // 返回创建的像素图
    return createPixelMap;
  }
}`;
  }
}
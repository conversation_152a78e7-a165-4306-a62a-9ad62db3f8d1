// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的通用常量、日志工具、页面上下文、窗口工具
import { CommonConstants, Logger, PageContext, WindowUtil, } from '@ohos/common';
// 导入启动页视图模型和事件类型枚举
import { SplashEventTypeEnum, SplashViewModel } from '../viewmodel/SplashViewModel';

// 日志标签常量
const TAG: string = '[SplashPage]';

/**
 * 启动页组件
 * 应用的启动页面，负责初始化和资源预加载
 * 显示启动动画并跳转到主页面
 */
@Entry
@Component
struct SplashPage {
  // 页面上下文，从应用存储中获取
  private pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
  // 应用路径信息，用于导航管理
  private appPathInfo: NavPathStack = this.pageContext.navPathStack;
  // 启动页视图模型，处理启动逻辑
  private viewModel: SplashViewModel = new SplashViewModel();

  /**
   * 页面隐藏时的回调方法
   * 恢复状态栏颜色为系统默认设置
   */
  onPageHide() {
    // 记录页面隐藏日志
    Logger.info(TAG, 'onPageHide');
    // 根据系统颜色模式更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this),
      AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
  }

  /**
   * 组件即将出现时的生命周期回调
   * 设置状态栏颜色、检查首次启动、预加载资源并启动动画
   */
  aboutToAppear(): void {
    // 设置状态栏为深色模式
    WindowUtil.updateStatusBarColor(getContext(this), true);
    // 发送检查首次启动事件
    this.viewModel.sendEvent(SplashEventTypeEnum.CHECK_FIRST_START);
    // 启动动画，延迟后跳转到主页面
    animateTo({
      // 设置动画延迟时间
      delay: CommonConstants.ANIMATION_DELAY,
      // 设置动画持续时间
      duration: CommonConstants.ANIMATION_DURATION,
      // 动画完成后的回调
      onFinish: () => {
        // 发送跳转到主页面事件
        this.viewModel.sendEvent(SplashEventTypeEnum.JUMP_TO_MAIN);
      }
    }, () => {
      // 动画开始时预加载资源
      this.viewModel.sendEvent(SplashEventTypeEnum.PRELOAD_RESOURCES);
    })
  }

  /**
   * 构建启动页组件的UI结构
   * 创建简单的导航容器和背景
   */
  build() {
    // 创建导航容器
    Navigation(this.appPathInfo) {
      // 创建垂直布局的列容器
      Column()
        // 设置宽度为100%
        .width('100%')
        // 设置高度为100%
        .height('100%')
        // 设置启动窗口背景颜色
        .backgroundColor($r('app.color.start_window_background'))
    }
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置导航模式为堆栈模式
    .mode(NavigationMode.Stack)
    // 设置高度为100%
    .height('100%')
    // 设置宽度为100%
    .width('100%')
  }
}
<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>开箱即用的AI语音播报能力</title>
  <link rel="stylesheet" href="../common/css/banner.css">
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
  <link rel="preload" as="image" href="./image/ai_speech.png">
  <link rel="preload" as="image" href="./image/pause.png">
</head>

<body>
  <div class="main-content">
    <!-- header -->
    <div class="header">
      <div class="title">开箱即用的AI语音播报能力</div>
      <div class="content image-cover">
        <img class="banner-img" src="./image/ai_speech.png" alt="">
        <div class="image-words">
          <div>开箱即用的AI语音播报能力</div>
          <div>承载最新亮点特性</div>
        </div>
      </div>
    </div>

    <!-- body -->
    <div class="chapter">
      <div class="section">
        <p class="text">本期为大家介绍 TextToSpeech 组件，解锁自然流畅新体验。</p>
        <div class="divider" style="color-scheme: light dark;"></div>
        <h3>播报体验</h3>
        <p class="text">引擎集成“聆小珊”音色，完美模拟真人语速音调，针对大篇幅文本的转换也不会卡顿，而且在聆听过程中也不会有很重的“机械音”。</p>
        <p class="text">个性化定制，用户可以根据自身习惯自定义语速、音调等，并且在播放过程中支持暂停和续播，完美契合不同场景下的要求，而且结合 HarmonyOS 的语音识别能力，可实现文本与语音的双转换。</p>
        <h3>操作体验</h3>
        <p class="text">操作简单方便，开箱即用。大家不需要很繁琐的步骤，提前准备好一段文字，点击播放按钮即可。现在让我们一起遨游在“听”的世界中吧。</p>
      </div>
      <div class="bg-container">
        <div class="title-container">
          <div class="main-title">AI语音播报能力</div>
          <div class="sub-title">开箱即用</div>
        </div>
        <div class="wrap-phobe">
          <video id="voice-video" class="phone-gif"
            src="./image/001.mp4"
            ></video>
          <img class="phone-border"
            src="./image/phone.png"
            alt="" />
        </div>
        <div class="wrap-btn" style="color-scheme: light dark;">
          <div id="play-btn" class="video-btn"></div>
          <div id="stop-btn" class="video-btn"></div>
        </div>
        <div class="bottom-text">
          <div>通过点击立即体验按钮，直接在HMOS</div>
          <div>世界中体验TextToSpeech案例</div>
        </div>
      </div>
      <div class="section button">
        <div class="section-button" onclick="jump()">立即体验</div>
      </div>
    </div>

    <!-- footer -->
    <div class="footer">
      <img class="footerImg"
        src="../common/image/f_icon.png"
        alt="" />
    </div>
  </div>
</body>
<script src="../common/js/banner.js"></script>
<script src="./index.js"></script>

</html>
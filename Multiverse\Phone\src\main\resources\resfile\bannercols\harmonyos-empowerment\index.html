<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>HarmonyOS赋能套件</title>
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
  <link rel="preload" as="image" href="./image/header.jpg">
  <link rel="preload" as="image" href="./image/path.png">
  <link rel="preload" as="image" href="./image/position.png">
</head>

<body>
  <!-- header -->
  <div class="header" style="color-scheme: light dark;">
      <img class="header-img" src="./image/header.png" alt="">
    <div class="title-container">
      <div class="main-title">HarmonyOS赋能套件</div>
      <div class="sub-title">
        打造业界一流的开发者赋能产品，助力开发者开发卓越应用
      </div>
    </div>
  </div>
  <!-- body -->
  <div class="chapter" style="color-scheme: light dark;">
    <img class="chapter-img" src="./image/path.png" alt="">
    <!-- 感知阶段 -->
    <div class="section">
      <!-- 目录标题 -->
      <div class="catalog-title title1">
        <img class="title-icon" src="./image/position.png" />
        <div class="title-text">感知阶段</div>
      </div>
      <!-- 详细目录 -->
      <div class="catalog-container">
        <div class="catalog-item" url="article_empowerment_1">
          <div class="item-title">
            1.&nbsp;HarmonyOS&nbsp;<span>白皮书</span>
          </div>
          <div class="item-detail">
            快速、准确、全面地掌握HarmonyOS开发的关键技术、核心理念以及解决方案。
          </div>
        </div>

        <div class="catalog-item" url="article_empowerment_2">
          <div class="item-title">
            2.&nbsp;HarmonyOS应用开发<span>知识地图</span>
          </div>
          <div class="item-detail">
            构建HarmonyOS学习路径，导航HarmonyOS开发的星辰大海。
          </div>
        </div>
      </div>
    </div>

    <!-- 学习阶段 -->
    <div class="section">
      <!-- 目录标题 -->
      <div class="catalog-title title2">
        <img class="title-icon" src="./image/position.png" />
        <div class="title-text">学习阶段</div>
      </div>
      <!-- 详细目录 -->
      <div class="catalog-container">
        <div class="catalog-item" url="article_empowerment_3">
          <div class="item-title">
            3.&nbsp;HarmonyOS&nbsp;<span>第一课</span>
          </div>
          <text class="item-detail">采用多维融合的教学方式，提供所见即所得的代码讲解和即学即练的操作实践。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_4">
          <div class="item-title">
            4.&nbsp;HarmonyOS应用开发<span>快速入门</span>
          </div>
          <text class="item-detail">通过手把手的教学实践案例，从零开始，掌握应用开发的必备技能。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_5">
          <div class="item-title">5.&nbsp;<span>Codelabs</span></div>
          <text class="item-detail">动手学编程，每一行代码都是成长的印记。</text>
        </div>
      </div>
    </div>

    <!-- 开发阶段 -->
    <div class="section">
      <!-- 目录标题 -->
      <div class="catalog-title title3">
        <div class="title-text">开发阶段</div>
        <img class="title-icon" src="./image/position.png" />
      </div>
      <!-- 详细目录 -->
      <div class="catalog-container">
        <div class="catalog-item" url="article_empowerment_6">
          <div class="item-title">6.&nbsp;开发<span>指南</span></div>
          <text class="item-detail">提供丰富的场景化的开发指导，助力开发者高效开发应用。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_7">
          <div class="item-title">7.&nbsp;<span>API</span>参考</div>
          <text class="item-detail">提供全量的组件、接口、错误码参考，方便开发者快速查找所需的API信息。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_8">
          <div class="item-title">8.&nbsp;<span>示例代码</span></div>
          <text class="item-detail">提供可直接引用的示例代码，加速开发进程，简化编程工作。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_9">
          <div class="item-title">9.&nbsp;最佳<span>实践</span></div>
          <text class="item-detail">汇集实战经验，覆盖高频开发场景，提升开发效率。</text>
        </div>
      </div>
    </div>

    <!-- 支持阶段 -->
    <div class="section">
      <!-- 目录标题 -->
      <div class="catalog-title title4">
        <img class="title-icon" src="./image/position.png" />
        <div class="title-text">支持阶段</div>
      </div>
      <!-- 详细目录 -->
      <div class="catalog-container">
        <div class="catalog-item" url="article_empowerment_10">
          <div class="item-title">10.&nbsp;开发者<span>社区</span></div>
          <text class="item-detail">提供丰富的场景化开发指导，助力开发者高效开发应用。</text>
        </div>

        <div class="catalog-item" url="article_empowerment_11">
          <div class="item-title">11.&nbsp;<span>AI</span>智能回答</div>
          <text class="item-detail">提供全量的组件、接口、错误码参考，方便开发者快速查找所需的API信息。</text>
        </div>
      </div>
    </div>
  </div>

  <!-- footer -->
  <div class="footer">
    <img class="footerImg"
      src="../common/image/f_icon.png"
      alt="" />
  </div>
</body>
<script src="../common/js/banner.js"></script>
<script type="module" src="./index.js"></script>

</html>
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型和通用常量
import { BreakpointType, CommonConstants } from '@ohos/common';
// 导入标签栏数据模型类型
import type { TabBarData } from '../model/TabBarModel';
// 导入标签列表
import { TABS_LIST } from '../model/TabBarModel';

/**
 * 自定义标签栏组件
 * 支持响应式设计，在不同屏幕尺寸下显示不同的布局
 * 小屏和中屏显示底部标签栏，大屏显示侧边标签栏
 */
@Component
export struct CustomTabBar {
  // 全局信息模型，包含断点信息和导航指示器高度
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 模糊渲染组状态
  @StorageProp('BlurRenderGroup') blurRenderGroup: boolean = false;
  // 当前选中的标签索引，必需属性
  @Prop @Require currentIndex: number;
  // 标签栏切换回调函数
  tabBarChange: (index: number) => void = (index: number) => {
  };

  /**
   * 标签项构建器
   * 构建单个标签项的UI，包含图标和文本
   * @param tabBar 标签栏数据
   */
  @Builder
  TabItemBuilder(tabBar: TabBarData) {
    // 创建垂直布局的列容器
    Column() {
      // 符号图标
      SymbolGlyph(tabBar.icon)
        // 设置图标字体大小
        .fontSize($r('sys.float.Title_M'))
        // 根据是否选中设置图标颜色
        .fontColor(tabBar.id === this.currentIndex ? [$r('sys.color.interactive_active')] :
          [$r('sys.color.font_tertiary')])
        // 设置渲染策略为多重透明度
        .renderingStrategy(SymbolRenderingStrategy.MULTIPLE_OPACITY)
        // 设置符号效果，选中时显示弹跳动画
        .symbolEffect(new BounceSymbolEffect(EffectScope.LAYER, EffectDirection.UP), tabBar.id === this.currentIndex)
      // 标签文本
      Text(tabBar.title)
        // 设置文本字体大小
        .fontSize($r('sys.float.Caption_M'))
        // 设置文本上边距
        .margin({ top: $r('sys.float.padding_level1') })
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 根据是否选中设置文本颜色
        .fontColor(tabBar.id === this.currentIndex ? $r('sys.color.interactive_active') : $r('sys.color.font_tertiary'))
    }
    // 设置宽度为100%
    .width('100%')
    // 设置高度为100%
    .height('100%')
    // 设置点击事件处理
    .onClick(() => {
      // 如果点击的不是当前选中项，触发切换
      if (this.currentIndex !== tabBar.id) {
        this.tabBarChange(tabBar.id);
      }
    })
    // 设置水平居中对齐
    .alignItems(HorizontalAlign.Center)
    // 设置垂直居中对齐
    .justifyContent(FlexAlign.Center)
  }

  /**
   * 构建自定义标签栏组件的UI结构
   * 使用响应式布局，根据屏幕尺寸调整布局方向和尺寸
   */
  build() {
    // 创建弹性布局容器，根据断点调整布局方向
    Flex({
      // 根据断点设置布局方向：小屏和中屏为列反向，大屏为行
      direction: new BreakpointType({
        sm: FlexDirection.ColumnReverse,
        md: FlexDirection.ColumnReverse,
        lg: FlexDirection.Row,
      }).getValue(this.globalInfoModel.currentBreakpoint),
      // 设置子项居中对齐
      alignItems: ItemAlign.Center,
    }) {
      // 标签项容器
      Flex({
        // 根据断点设置标签项布局方向：小屏和中屏为行，大屏为列
        direction: new BreakpointType({
          sm: FlexDirection.Row,
          md: FlexDirection.Row,
          lg: FlexDirection.Column,
        }).getValue(this.globalInfoModel.currentBreakpoint),
        // 设置子项居中对齐
        alignItems: ItemAlign.Center,
        // 设置子项均匀分布
        justifyContent: FlexAlign.SpaceAround,
      }) {
        // 遍历标签列表，创建标签项
        ForEach(TABS_LIST, (item: TabBarData) => {
          // 调用标签项构建器
          this.TabItemBuilder(item)
        }, (item: TabBarData) => JSON.stringify(item) + this.currentIndex)
      }
      // 根据断点设置标签项容器尺寸
      .size(new BreakpointType<SizeOptions>({
        sm: { width: '100%', height: CommonConstants.TAB_BAR_HEIGHT },
        md: { width: '100%', height: CommonConstants.TAB_BAR_HEIGHT },
        lg: { width: CommonConstants.TAB_BAR_WIDTH, height: '50%' },
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 根据断点设置底部边距，小屏和中屏需要考虑导航指示器高度
      .margin({
        bottom: new BreakpointType({
          sm: this.globalInfoModel.naviIndicatorHeight,
          md: this.globalInfoModel.naviIndicatorHeight,
          lg: 0,
        }).getValue(this.globalInfoModel.currentBreakpoint),
      })

      // 分割线
      Divider()
        // 根据断点设置分割线方向：小屏和中屏为水平，大屏为垂直
        .vertical(new BreakpointType({
          sm: false,
          md: false,
          lg: true,
        }).getValue(this.globalInfoModel.currentBreakpoint))
        // 设置分割线颜色
        .color($r('sys.color.comp_divider'))
    }
    // 根据断点设置整个标签栏的尺寸
    .size(new BreakpointType<SizeOptions>({
      sm: { width: '100%', height: CommonConstants.TAB_BAR_HEIGHT + this.globalInfoModel.naviIndicatorHeight },
      md: { width: '100%', height: CommonConstants.TAB_BAR_HEIGHT + this.globalInfoModel.naviIndicatorHeight },
      lg: { width: CommonConstants.TAB_BAR_WIDTH, height: '100%' },
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 设置背景模糊效果
    .backgroundBlurStyle(BlurStyle.COMPONENT_THICK)
    // 设置渲染组
    .renderGroup(this.blurRenderGroup)
  }
}
// 导入ArkUI工具包中的自定义内容对话框和提示对话框组件
import { CustomContentDialog, TipsDialog } from '@kit.ArkUI';
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入自定义对话框样式枚举和对话框资源映射数据
import { CustomDialogStyle, dialogResourceMapData } from '../entity/CustomDialogAttributeMapping';
// 导入自定义对话框描述器类型，用于获取对话框配置信息
import type { CustomDialogDescriptor } from '../viewmodel/CustomDialogDescriptor';

// 日志标签，用于标识日志来源
const TAG: string = '[CustomDialogComponent]';
// 进度值常量，用于进度对话框显示
const PROGRESS_VALUE: number = 20;

/**
 * 自定义对话框组件
 * 提供提示对话框和进度对话框两种样式的自定义对话框
 */
@Component
export struct CustomDialogComponent {
  // 自定义对话框描述器属性，用于配置对话框样式和行为
  @Prop customDialogDescriptor: CustomDialogDescriptor;
  // 复选框选中状态，用于提示对话框的复选框控制
  @State isChecked: boolean = true;
  // 提示对话框控制器，用于控制提示对话框的显示和隐藏
  private tipDialogController?: CustomDialogController = new CustomDialogController({
    // 设置对话框构建器
    builder: this.tipDialogBuilder,
    // 设置取消回调函数
    cancel: this.existApp,
    // 设置对话框即将关闭时的回调
    onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      // 如果是按返回键关闭
      if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
        // 执行关闭操作
        dismissDialogAction.dismiss();
      }
      // 如果是点击外部区域关闭
      if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
        // 执行关闭操作
        dismissDialogAction.dismiss();
      }
    },
    // 设置对话框对齐方式为居中
    alignment: DialogAlignment.Center,
    // 启用自动取消功能
    autoCancel: true,
    // 设置对话框圆角半径
    cornerRadius: $r('sys.float.padding_level10'),
  });
  // 进度对话框控制器，用于控制进度对话框的显示和隐藏
  private progressDialogController?: CustomDialogController = new CustomDialogController({
    // 设置对话框构建器
    builder: this.progressDialogBuilder,
    // 设置取消回调函数
    cancel: this.existApp,
    // 设置对话框即将关闭时的回调
    onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      // 如果是按返回键关闭
      if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
        // 执行关闭操作
        dismissDialogAction.dismiss();
      }
      // 如果是点击外部区域关闭
      if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
        // 执行关闭操作
        dismissDialogAction.dismiss();
      }
    },
    // 设置对话框对齐方式为居中
    alignment: DialogAlignment.Center,
    // 启用自动取消功能
    autoCancel: true,
    // 设置对话框圆角半径
    cornerRadius: $r('sys.float.padding_level10'),
  });

  /**
   * 提示对话框构建器
   * 构建包含图片、标题、内容和复选框的提示对话框
   */
  @Builder
  tipDialogBuilder() {
    // 创建提示对话框组件
    TipsDialog({
      // 设置对话框图片资源
      imageRes: $r('app.media.image_dialog'),
      // 设置图片尺寸
      imageSize: {
        width: 'auto',
        height: $r('app.float.dialog_contain_image_height'),
      },
      // 设置对话框标题
      title: $r('app.string.dialog_graphic_title'),
      // 设置对话框内容
      content: $r('app.string.dialog_graphic_content'),
      // 设置复选框选中状态
      isChecked: this.isChecked,
      // 设置复选框提示文本
      checkTips: $r('app.string.dialog_graphic_tip'),
      // 设置复选框状态改变回调
      onCheckedChange: () => {
        // 切换复选框状态
        this.isChecked = !this.isChecked;
      },
      // 设置主要按钮（取消按钮）
      primaryButton: {
        // 设置按钮文本
        value: $r('app.string.dialog_cancel'),
        // 设置按钮点击回调
        action: () => {
          // 执行取消操作
          this.onCancel();
          // 关闭提示对话框
          this.tipDialogController?.close();
        },
      },
      // 设置次要按钮（确认按钮）
      secondaryButton: {
        // 设置按钮文本
        value: $r('app.string.dialog_confirm'),
        // 设置按钮点击回调
        action: () => {
          // 执行确认操作
          this.onAccept();
          // 关闭提示对话框
          this.tipDialogController?.close();
        },
      },
    })
  }

  /**
   * 进度对话框构建器
   * 构建包含自定义内容和按钮的进度对话框
   */
  @Builder
  progressDialogBuilder() {
    // 创建自定义内容对话框组件
    CustomContentDialog({
      // 设置内容构建器
      contentBuilder: () => {
        // 调用构建内容方法
        this.buildContent();
      },
      // 设置对话框按钮数组
      buttons: [
        {
          // 设置取消按钮文本
          value: $r('app.string.dialog_cancel'),
          // 设置取消按钮点击回调
          action: () => {
            // 执行取消操作
            this.onCancel();
            // 关闭进度对话框
            this.progressDialogController?.close();
          },
        }, {
        // 设置确认按钮文本
        value: $r('app.string.dialog_confirm'),
        // 设置确认按钮点击回调
        action: () => {
          // 执行确认操作
          this.onAccept();
          // 关闭进度对话框
          this.progressDialogController?.close();
        },
      },
      ],
    })
  }

  /**
   * 构建对话框内容
   * 创建包含标题、进度文本和进度条的内容布局
   */
  @Builder
  buildContent(): void {
    // 创建垂直列布局
    Column() {
      // 创建水平行布局，包含标题和进度文本
      Row() {
        // 显示标题文本
        Text($r('app.string.title'))
          // 设置字体颜色为主要字体色
          .fontColor($r('sys.color.font_primary'))
          // 设置字体大小为中等副标题
          .fontSize($r('sys.float.Subtitle_M'))
          // 设置文本左对齐
          .textAlign(TextAlign.Start)
        // 添加空白填充区域
        Blank()
        // 显示进度文本，包含进度值
        Text($r('app.string.dialog_progress', PROGRESS_VALUE))
          // 设置字体颜色为次要字体色
          .fontColor($r('sys.color.font_secondary'))
          // 设置字体大小为中等正文
          .fontSize($r('sys.float.Body_M'))
          // 设置文本宽度
          .width($r('app.float.text_height_large'))
          // 设置文本居中对齐
          .textAlign(TextAlign.Center)
      }
      // 设置行宽度为100%
      .width('100%')
      // 设置行高度
      .height($r('app.float.dialog_text_height'))
      // 设置垂直居中对齐
      .alignItems(VerticalAlign.Center)

      // 创建进度条组件
      Progress({ value: PROGRESS_VALUE })
        // 设置进度条高度
        .height($r('app.float.dialog_progress_height'))
        // 设置进度条边距
        .margin({ top: $r('sys.float.padding_level4'), bottom: $r('sys.float.padding_level2') })
    }
  }

  /**
   * 组件即将销毁时的回调方法
   * 清理对话框控制器资源，防止内存泄漏
   */
  aboutToDisappear() {
    // 清理提示对话框控制器
    this.tipDialogController = undefined;
    // 清理进度对话框控制器
    this.progressDialogController = undefined;
  }

  /**
   * 取消按钮点击回调方法
   * 处理用户点击取消按钮的逻辑
   */
  onCancel() {
    // 记录取消按钮点击日志
    Logger.info(TAG, 'Callback when the first button is clicked');
  }

  /**
   * 确认按钮点击回调方法
   * 处理用户点击确认按钮的逻辑
   */
  onAccept() {
    // 记录确认按钮点击日志
    Logger.info(TAG, 'Callback when the second button is clicked');
  }

  /**
   * 点击空白区域回调方法
   * 处理用户点击对话框外部区域的逻辑
   */
  existApp() {
    // 记录点击空白区域日志
    Logger.info(TAG, 'Click the callback in the blank area');
  }

  /**
   * 构建组件UI界面
   * 创建包含触发按钮的主界面布局
   */
  build() {
    // 创建垂直列布局
    Column() {
      // 创建触发对话框的按钮
      Button(dialogResourceMapData.get(this.customDialogDescriptor.style))
        // 设置按钮样式为普通模式
        .buttonStyle(ButtonStyleMode.NORMAL)
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为大号正文
        .fontSize($r('sys.float.Body_L'))
        // 设置字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置按钮点击事件
        .onClick(() => {
          // 根据对话框样式决定打开哪种对话框
          if (this.customDialogDescriptor.style === CustomDialogStyle.StyleProgress) {
            // 打开进度对话框
            this.progressDialogController?.open();
          } else {
            // 打开提示对话框
            this.tipDialogController?.open();
          }
        })
    }
  }
}
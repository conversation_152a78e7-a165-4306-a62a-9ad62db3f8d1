// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入文本描述器类型，用于描述文本组件配置
import type { TextDescriptor } from './TextDescriptor';

/**
 * 文本属性修改器类
 * 继承自通用属性修改器，用于修改文本组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class TextAttributeModifier extends CommonAttributeModifier<TextDescriptor, TextAttribute> {
  /**
   * 应用普通属性到文本组件实例
   * 将描述器中的属性值应用到实际的文本组件上
   * @param instance 文本属性实例，用于设置文本组件属性
   */
  applyNormalAttribute(instance: TextAttribute): void {
    // 分配字体粗细属性，将描述器中的fontWeight值应用到组件实例
    this.assignAttribute((descriptor => descriptor.fontWeight), (val) => instance.fontWeight(val));
    // 分配字体大小属性，将描述器中的fontSize值转换为数字后应用到组件实例
    this.assignAttribute((descriptor => descriptor.fontSize), (val) => instance.fontSize(Number(val)));
    // 分配字体颜色属性，将描述器中的fontColor值应用到组件实例
    this.assignAttribute((descriptor => descriptor.fontColor), (val) => instance.fontColor(val));
    // 分配透明度属性，将描述器中的opacity值应用到组件实例
    this.assignAttribute((descriptor => descriptor.opacity), (val) => instance.opacity(val));
    // 分配字符间距属性，将描述器中的letterSpacing值应用到组件实例
    this.assignAttribute((descriptor => descriptor.letterSpacing), (val) => instance.letterSpacing(val));
    // 分配文本阴影属性，将描述器中的textShadowRadius值应用到组件实例，并设置默认阴影颜色
    this.assignAttribute((descriptor => descriptor.textShadowRadius), (val) => instance.textShadow({ radius: val,
      color: $r('sys.color.ohos_id_color_foreground')}));
  }
}
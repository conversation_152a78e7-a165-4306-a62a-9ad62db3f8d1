// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入切换组件相关的属性映射数据
import { backgroundColorMapData, isOnMapData, toggleTypeMapData } from '../entity/ToggleAttributeMapping';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';

/**
 * 切换组件代码生成器类
 * 实现通用代码生成器接口，用于生成切换组件的代码
 */
export class ToggleCodeGenerator implements CommonCodeGenerator {
  // 私有切换类型属性，默认使用默认值
  private toggleType: string = toggleTypeMapData.get('Default')!.code;
  // 私有开关状态属性，默认使用默认值
  private isOn: string = isOnMapData.get('Default')!.code;
  // 私有背景颜色属性，默认使用默认值
  private backgroundColor: string = backgroundColorMapData.get('Default')!.code;

  /**
   * 生成切换组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的切换组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性进行处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理切换类型属性
        case 'toggleType':
          // 从映射数据中获取对应的代码，如果未找到则使用默认值
          this.toggleType =
            toggleTypeMapData.get(attribute.currentValue)?.code ?? toggleTypeMapData.get('Default')!.code;
          break;
        case 'isOn':
          this.isOn = JSON.parse(attribute.currentValue);
          break;
        case 'backgroundColor':
          this.backgroundColor = attribute.currentValue;
          break;
        default:
          break;
      }
    });
    const codeOne = this.toggleType === 'ToggleType.Switch' ? `
      Text('Switch样式')
        .fontSize(16)
        .fontColor($r('sys.color.font_secondary'))
      Blank()` : '';
    if (this.toggleType === 'ToggleType.Button') {
      return `@Component
struct ToggleComponent {
  // You can view different styles by changing the toggleType.
  build() {
    Row() {${codeOne}
       Toggle({
        type: ${this.toggleType},
        isOn: ${this.isOn}
      }){
          Text('状态按钮')
            .fontColor($r('sys.color.font_on_primary'))
            .fontSize(12)
        }
      .selectedColor('${this.backgroundColor}')
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .padding({ left: $r('sys.float.padding_level16'), right: $r('sys.float.padding_level16') })
  }
}`;
    } else {
      return `@Component
struct ToggleComponent {
  // You can view different styles by changing the toggleType.
  build() {
    Row() {${codeOne}
       Toggle({
        type: ${this.toggleType},
        isOn: ${this.isOn}
      })
      .selectedColor('${this.backgroundColor}')
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .padding({ left: $r('sys.float.padding_level16'), right: $r('sys.float.padding_level16') })
  }
}`
    }
  }
}
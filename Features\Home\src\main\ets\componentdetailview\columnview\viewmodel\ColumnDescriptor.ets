// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入列组件相关的映射数据，包含各种属性的映射关系
import {
  columnAlignMapData,
  columnPaddingMapData,
  columnSpaceMapData,
  paddingNumMapData,
} from '../entity/ColumnAttributeMapping';
// 导入行组件的内容对齐映射数据，用于列组件的垂直对齐
import { rowJustifyContentMapData } from '../../rowview/entity/RowAttributeMapping';
// 导入通用描述器基类，用于继承基础描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';

/**
 * 列描述器类
 * 继承通用描述器，用于描述列组件的属性和行为
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class ColumnDescriptor extends CommonDescriptor {
  // 对齐项目属性，控制子元素的水平对齐方式
  public alignItems: HorizontalAlign = columnAlignMapData.get('Default')!.value;
  // 弹性对齐属性，控制子元素的垂直对齐方式
  public flexAlign: FlexAlign = rowJustifyContentMapData.get('Default')!.value;
  // 间距属性，控制子元素之间的间距数值
  public space: number = columnSpaceMapData.get('Default')!.value;
  // 内边距类型属性，控制内边距的应用方式
  public padding: string = columnPaddingMapData.get('Default')!.value;
  // 内边距数值属性，控制内边距的具体数值
  public paddingNum: number = paddingNumMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为列描述器的具体属性值
   * @param attributes 原始属性数组，包含组件的各种配置属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理对齐项目属性
        case 'alignItems':
          // 从映射数据中获取对应的水平对齐枚举值，如果不存在则保持原值
          this.alignItems = columnAlignMapData.get(attribute.currentValue)?.value ?? this.alignItems;
          break;
        // 处理弹性对齐属性
        case 'flexAlign':
          // 从行组件映射数据中获取对应的弹性对齐枚举值，如果不存在则保持原值
          this.flexAlign = rowJustifyContentMapData.get(attribute.currentValue)?.value ?? this.flexAlign;
          break;
        // 处理间距属性
        case 'space':
          // 将字符串值转换为数字类型
          this.space = Number(attribute.currentValue);
          break;
        // 处理内边距类型属性
        case 'padding':
          // 直接使用属性的当前值
          this.padding = attribute.currentValue;
          break;
        // 处理内边距数值属性
        case 'paddingNum':
          // 将字符串值转换为数字类型
          this.paddingNum = Number(attribute.currentValue);
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
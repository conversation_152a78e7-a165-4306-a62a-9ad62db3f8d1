// 导入通用模块中的全局信息模型、加载失败视图、加载状态、加载视图和无网络视图
import { GlobalInfoModel, LoadingFailedView, LoadingStatus, LoadingView, NoNetworkView } from '@ohos/common';

/**
 * 基础详情组件
 * 通用的详情页面组件，提供统一的加载状态管理和视图切换
 * 支持加载中、加载失败、无网络等状态的显示
 * 通过BuilderParam接收自定义的内容视图和标题视图
 */
@Component
export struct BaseDetailComponent {
  // 全局信息模型，用于获取设备信息和断点状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 加载状态属性
  @Prop loadingStatus: LoadingStatus;
  // 详情内容视图构建器参数，必需
  @BuilderParam @Require detailContentView: () => void;
  // 顶部标题视图构建器参数，必需
  @BuilderParam @Require topTitleView: () => void;
  // 重新加载数据的回调函数，可选
  reloadData?: Function;

  /**
   * 构建基础详情组件的UI结构
   * 根据加载状态显示不同的视图内容
   */
  build() {
    // 创建左上角对齐的堆叠容器
    Stack({ alignContent: Alignment.TopStart }) {
      // 如果加载状态不是空闲状态，显示详情内容视图
      if (this.loadingStatus !== LoadingStatus.IDLE) {
        this.detailContentView()
      }
      // 根据加载状态显示相应的状态视图
      if (this.loadingStatus === LoadingStatus.IDLE || this.loadingStatus === LoadingStatus.LOADING) {
        // 显示加载视图
        LoadingView(this.globalInfoModel.currentBreakpoint)
      } else if (this.loadingStatus === LoadingStatus.FAILED) {
        // 显示加载失败视图，并提供重新加载功能
        LoadingFailedView(this.globalInfoModel.currentBreakpoint, () => {
          this.reloadData?.();
        })
      } else if (this.loadingStatus === LoadingStatus.NO_NETWORK) {
        // 显示无网络视图，并提供重新加载功能
        NoNetworkView(this.globalInfoModel.currentBreakpoint, () => {
          this.reloadData?.();
        })
      }
      // 显示顶部标题视图，始终在最上层
      this.topTitleView()
    }
    // 设置堆叠容器背景为透明
    .backgroundColor(Color.Transparent)
  }
}
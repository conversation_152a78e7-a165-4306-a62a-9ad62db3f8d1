// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入瀑布流描述器类型，用于描述瀑布流组件配置
import type { WaterFlowDescriptor } from './WaterFlowDescriptor';

/**
 * 瀑布流属性修改器类
 * 继承自通用属性修改器，用于修改瀑布流组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class WaterFlowAttributeModifier extends CommonAttributeModifier<WaterFlowDescriptor, WaterFlowAttribute> {
  /**
   * 应用普通属性到瀑布流组件实例
   * 将描述器中的属性值应用到实际的瀑布流组件上
   * @param instance 瀑布流属性实例，用于设置瀑布流组件属性
   */
  applyNormalAttribute(instance: WaterFlowAttribute): void {
    // 分配布局方向属性，将描述器中的layoutDirection值应用到组件实例
    this.assignAttribute((descriptor => descriptor.layoutDirection), (val) => instance.layoutDirection(val));
    // 分配摩擦力属性，将描述器中的friction值转换为数字后应用到组件实例
    this.assignAttribute((descriptor => descriptor.friction), (val) => instance.friction(Number(val)));
    // 分配列模板属性，将描述器中的columnsTemplate值应用到组件实例
    this.assignAttribute((descriptor => descriptor.columnsTemplate), (val) => instance.columnsTemplate(val));
    // 分配行模板属性，将描述器中的rowsTemplate值应用到组件实例
    this.assignAttribute((descriptor => descriptor.rowsTemplate), (val) => instance.rowsTemplate(val));
    // 分配列间距属性，将描述器中的columnsGap值应用到组件实例
    this.assignAttribute((descriptor => descriptor.columnsGap), (val) => instance.columnsGap(val));
  }
}
// 导入方舟UI工具包中的文本测量功能
import { MeasureText } from '@kit.ArkUI';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型枚举和通用常量
import { BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入通用业务模块中的卡片样式类型枚举
import { CardStyleTypeEnum } from '@ohos/commonbusiness';
// 导入示例详情常量
import { SampleDetailConstant } from '../constant/CommonConstants';

// 定义标签信息接口
interface TagInfo {
  // 标签文本
  tag: string;
  // 标签宽度
  width: number;
}

// 定义内边距宽度常量
const PADDING_WIDTH: number = 24;
// 定义最小宽度常量
const MIN_WIDTH: number = 42;
// 定义省略号常量
const ELLIPSIS = '...';

// 使用Component装饰器定义标签标签组件
@Component
export struct TagLabel {
  // 使用Prop装饰器定义标签数组属性
  @Prop tags: string[];
  // 使用Prop和Watch装饰器定义最大宽度属性并监听变化
  @Prop @Watch('changeTagList') maxWidth: number;
  // 使用Prop装饰器定义是否深色模式属性
  @Prop isDark: boolean;
  // 使用Prop装饰器定义卡片样式类型属性
  @Prop cardStyleType: CardStyleTypeEnum;
  // 使用State装饰器定义标签列表状态
  @State tagList: TagInfo[] = [];
  // 使用State装饰器定义显示省略号状态
  @State showEllipsis: boolean = false;
  // 使用State装饰器定义全局信息模型状态
  @State globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义显示弹窗状态
  @State displayPopup: boolean = false;
  // 定义所有标签字符串私有变量
  private allTagStr: string = '';

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 改变标签列表
    this.changeTagList();
  }

  // 定义改变标签列表的方法
  changeTagList() {
    // 创建临时标签列表
    const tempList: TagInfo[] = [];
    // 初始化当前宽度
    let currentWidth: number = 0;
    // 根据断点类型计算省略号宽度
    const ellipsisWidth = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? MIN_WIDTH : 0;
    // 遍历标签数组
    this.tags.forEach((tag: string) => {
      // 计算标签宽度
      const tagWidth: number = Math.ceil(px2vp(MeasureText.measureText({
        textContent: tag,
        fontSize: $r('sys.float.Body_S')
      }))) + PADDING_WIDTH + CommonConstants.SPACE_8;
      // 如果没有最大宽度限制或当前宽度加标签宽度小于最大宽度
      if (!this.maxWidth || currentWidth + tagWidth < this.maxWidth) {
        // 累加当前宽度
        currentWidth += tagWidth;
        // 添加标签到临时列表
        tempList.push({ tag: tag, width: tagWidth - CommonConstants.SPACE_8 });
      } else if (currentWidth < this.maxWidth - ellipsisWidth) {
        // 判断是否隐藏省略号
        const hideEllipse: boolean = this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL ||
          (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL &&
            tempList.length === 0)
        // 计算剩余宽度
        const width: number = this.maxWidth - currentWidth - CommonConstants.SPACE_8;
        // 如果隐藏省略号
        if (hideEllipse) {
          // 不显示省略号
          this.showEllipsis = false;
        } else {
          // 显示省略号
          this.showEllipsis = true;
        }
        // 如果剩余宽度大于最小宽度且隐藏省略号
        if (width > MIN_WIDTH && hideEllipse) {
          // 添加标签到临时列表
          tempList.push({ tag, width });
        }
        // 设置当前宽度为最大宽度
        currentWidth = this.maxWidth;
      }
    });
    // 设置标签列表
    this.tagList = tempList;
    // 如果标签数组长度大于0
    if (this.tags.length > 0) {
      // 将所有标签用分号连接
      this.allTagStr = this.tags.join(';');
    }
  }

  // 定义构建方法
  build() {
    // 创建标签行布局
    Row({ space: CommonConstants.SPACE_8 }) {
      // 遍历标签列表
      ForEach(this.tagList, (item: TagInfo) => {
        // 创建标签行布局
        Row() {
          // 显示标签文本
          Text(item.tag)
            .fontColor(this.cardStyleType === CardStyleTypeEnum.PICTURE_TO_SWIPER ?
            $r('app.color.card_font_secondary_color') : $r('sys.color.font_secondary'))
            .width(item.width - PADDING_WIDTH)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .fontSize($r('sys.float.Body_S'))
            .fontWeight(FontWeight.Regular)
        }
        // 设置标签行居中对齐
        .justifyContent(FlexAlign.Center)
        // 设置标签行圆角边框
        .borderRadius('50%')
        // 设置标签行内边距
        .padding({
          left: $r('sys.float.padding_level6'),
          right: $r('sys.float.padding_level6'),
          top: $r('sys.float.padding_level2'),
          bottom: $r('sys.float.padding_level2'),
        })
        // 设置标签行宽度
        .width(item.width)
        // 根据深色模式设置标签行背景颜色
        .backgroundColor(this.isDark ? $r('sys.color.icon_on_tertiary') :
        $r('sys.color.comp_background_tertiary'))
      }, (item: TagInfo) => item.tag)
      // 如果显示省略号
      if (this.showEllipsis) {
        // 创建省略号行布局
        Row() {
          // 显示省略号文本
          Text(ELLIPSIS)
            .fontColor(this.isDark ? $r('sys.color.font_on_secondary') : $r('sys.color.font_secondary'))
            .width(MIN_WIDTH - PADDING_WIDTH)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .fontSize($r('sys.float.Body_S'))
            .fontWeight(FontWeight.Regular)
            .textAlign(TextAlign.Center)
        }
        // 设置省略号行宽度
        .width(MIN_WIDTH)
        // 设置省略号行内边距
        .padding({
          left: $r('sys.float.padding_level6'),
          right: $r('sys.float.padding_level6'),
          top: $r('sys.float.padding_level2'),
          bottom: $r('sys.float.padding_level2')
        })
        // 设置省略号行圆角边框
        .borderRadius('50%')
        // 根据深色模式设置省略号行背景颜色
        .backgroundColor(this.isDark ? $r('sys.color.icon_on_tertiary') :
        $r('sys.color.comp_background_tertiary'))
        // 设置省略号行右边距
        .margin({ right: $r('sys.float.padding_level8') })
        // 绑定弹窗
        .bindPopup(this.displayPopup, {
          message: this.allTagStr,
          mask: false,
          popupColor: $r('sys.color.ohos_id_blur_style_component_regular_color'),
          shadow: ShadowStyle.OUTER_DEFAULT_SM,
          offset: { x: SampleDetailConstant.HOVER_POPUP_LEFT, y: 0 },
          autoCancel: true,
          radius: ($r('sys.float.corner_radius_level4')),
          messageOptions: {
            textColor: $r('sys.color.font_primary'),
            font: { size: $r('sys.float.Body_M') }
          }
        })
        // 设置省略号行悬停事件
        .onHover((isHover: boolean) => {
          // 设置显示弹窗状态
          this.displayPopup = isHover;
          // 如果标签数组长度为0则不显示弹窗
          if (this.tags.length == 0) {
            this.displayPopup = false;
          }
        })
      }
    }
    // 设置标签行顶部边距
    .margin({ top: $r('sys.float.padding_level4') })
  }
}
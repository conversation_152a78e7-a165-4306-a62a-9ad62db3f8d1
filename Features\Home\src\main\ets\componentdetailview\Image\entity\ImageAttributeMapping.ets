/**
 * 图片适配样式映射类
 * 用于存储图片适配方式的代码字符串和实际值的映射关系
 */
class ImageFitStyleMap {
  // 代码字符串，用于代码生成
  public code: string;
  // 图片适配方式的实际值
  public value: ImageFit;

  /**
   * 构造函数
   * @param code 代码字符串
   * @param value 图片适配方式值
   */
  constructor(code: string, value: ImageFit) {
    this.code = code;
    this.value = value;
  }
}

export const imageFitStyleMapData: Map<string, ImageFitStyleMap> = new Map([
  ['Default', new ImageFitStyleMap('ImageFit.Cover', ImageFit.Cover)],
  ['Contain', new ImageFitStyleMap('ImageFit.Contain', ImageFit.Contain)],
  ['Cover', new ImageFitStyleMap('ImageFit.Cover', ImageFit.Cover)],
  ['Auto', new ImageFitStyleMap('ImageFit.Auto', ImageFit.Auto)],
  ['Fill', new ImageFitStyleMap('ImageFit.Fill', ImageFit.Fill)],
  ['ScaleDown', new ImageFitStyleMap('ImageFit.ScaleDown', ImageFit.ScaleDown)],
  ['None', new ImageFitStyleMap('ImageFit.None', ImageFit.None)],
  ['TopStart', new ImageFitStyleMap('ImageFit.TOP_START', ImageFit.TOP_START)],
  ['Top', new ImageFitStyleMap('ImageFit.TOP', ImageFit.TOP)],
  ['TopEnd', new ImageFitStyleMap('ImageFit.TOP_END', ImageFit.TOP_END)],
  ['Start', new ImageFitStyleMap('ImageFit.START', ImageFit.START)],
  ['Center', new ImageFitStyleMap('ImageFit.CENTER', ImageFit.CENTER)],
  ['End', new ImageFitStyleMap('ImageFit.END', ImageFit.END)],
  ['Bottom', new ImageFitStyleMap('ImageFit.BOTTOM', ImageFit.BOTTOM)],
  ['BottomStart', new ImageFitStyleMap('ImageFit.BOTTOM_START', ImageFit.BOTTOM_START)],
  ['BottomEnd', new ImageFitStyleMap('ImageFit.BOTTOM_END', ImageFit.BOTTOM_END)],
]);

class FilterStyleMap {
  public code: string;
  public value: string;

  constructor(code: string, value: string) {
    this.code = code;
    this.value = value;
  }
}

export const filterStyleMapData: Map<string, FilterStyleMap> = new Map([
  // The input parameter is a 4x5 RGBA conversion matrix.
  ['无滤镜', new FilterStyleMap('1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0', '1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0')],
  ['色彩旋转',
    new FilterStyleMap('0,1,0,0,0,0,0,1,0,0,1,0,0,0,0,0,0,0,1,0', '0,1,0,0,0,0,0,1,0,0,1,0,0,0,0,0,0,0,1,0')],
  ['灰色滤镜',
    new FilterStyleMap('0.3086,0.6094,0.0820,0,0,0.3086,0.6094,0.0820,0,0,0.3086,0.6094,0.0820,0,0,0,0,0,1,0',
      '0.3086,0.6094,0.0820,0,0,0.3086,0.6094,0.0820,0,0,0.3086,0.6094,0.0820,0,0,0,0,0,1,0')],
  ['增色滤镜',
    new FilterStyleMap('1,0,0,0,0,0,1,0,0,255,0,0,1,0,0,0,0,0,1,0', '1,0,0,0,0,0,1,0,0,255,0,0,1,0,0,0,0,0,1,0')],
  ['Default', new FilterStyleMap('1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0', '1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0')]
]);
// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入切换组件相关的属性映射数据
import {
  backgroundColorMapData,
  isOnMapData,
  toggleTypeMapData,
  trackBorderRadiusMapData,
} from '../entity/ToggleAttributeMapping';

/**
 * 切换组件描述器类
 * 继承自通用描述器，用于描述切换组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class ToggleDescriptor extends CommonDescriptor {
  // 切换类型属性，默认使用默认值
  public toggleType: ToggleType = toggleTypeMapData.get('Default')!.value as ToggleType;
  // 轨道边框圆角属性，默认使用默认值
  public trackBorderRadius: number = trackBorderRadiusMapData.get('Default')!.value as number;
  // 开关状态属性，默认使用默认值
  public isOn: boolean = isOnMapData.get('Default')!.value as boolean;
  // 背景颜色属性，默认使用默认值
  public backgroundColor: ResourceColor = backgroundColorMapData.get('Default')!.value as ResourceColor;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'toggleType':
          this.toggleType = toggleTypeMapData.get(attribute.currentValue)?.value as ToggleType ??
            toggleTypeMapData.get('Default')!.value as ToggleType;
          break;
        case 'isOn':
          this.isOn = JSON.parse(attribute.currentValue) ?? isOnMapData.get('Default')!.value as boolean;
          break;
        case 'backgroundColor':
          this.backgroundColor = attribute.currentValue;
          break;
        default:
          break;
      }
    });
  }
}
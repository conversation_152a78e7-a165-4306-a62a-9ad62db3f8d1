// 导入详情页常量，包含容器边框等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入行布局属性修改器，用于修改组件属性
import { RowAttributeModifier } from '../viewmodel/RowAttributeModifier';
// 导入行布局描述器类型，用于描述组件配置
import type { RowDescriptor } from '../viewmodel/RowDescriptor';

/**
 * 行布局构建器函数
 * 用于构建行布局组件，包含三个不同大小的列子组件
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function RowBuilder($$: DescriptorWrapper) {
  // 创建行布局容器，设置子组件间距
  Row({ space: ($$.descriptor as RowDescriptor).space }) {
    // 创建第一个列子组件
    Column()
      // 设置第一个列的尺寸
      .size({ width: $r('app.float.container_size_1'), height: $r('app.float.container_size_1') })
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
    // 创建第二个列子组件
    Column()
      // 设置第二个列的尺寸
      .size({ width: $r('app.float.container_size_2'), height: $r('app.float.container_size_2') })
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
    // 创建第三个列子组件
    Column()
      // 设置第三个列的尺寸
      .size({ width: $r('app.float.container_size_3'), height: $r('app.float.container_size_3') })
      // 设置背景颜色为强调背景色
      .backgroundColor($r('sys.color.comp_background_emphasize'))
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level4'))
  }
  // 根据内边距类型设置行布局宽度
  .width(($$.descriptor as RowDescriptor).padding === 'None' ? '80%' : 'auto')
  // 根据内边距类型设置行布局高度
  .height(($$.descriptor as RowDescriptor).padding === 'None' ? '50%' : 'auto')
  // 根据内边距类型设置不同的内边距配置
  .padding(($$.descriptor as RowDescriptor).padding === 'Vertical' ?
    {
      // 垂直内边距：设置上下内边距
      top: ($$.descriptor as RowDescriptor).paddingNum,
      bottom: ($$.descriptor as RowDescriptor).paddingNum
    } : ($$.descriptor as RowDescriptor).padding === 'Horizontal' ? {
      // 水平内边距：设置左右内边距
      left: ($$.descriptor as RowDescriptor).paddingNum,
      right: ($$.descriptor as RowDescriptor).paddingNum,
    } : ($$.descriptor as RowDescriptor).paddingNum)
  // 设置属性修改器，用于动态修改组件属性
  .attributeModifier(new RowAttributeModifier($$.descriptor as RowDescriptor))
  // 设置行布局圆角半径
  .borderRadius($r('sys.float.corner_radius_level6'))
  // 设置行布局边框样式
  .border({ width: DetailPageConstant.CONTAINER_BORDER, color: $r('sys.color.comp_background_emphasize') })
}
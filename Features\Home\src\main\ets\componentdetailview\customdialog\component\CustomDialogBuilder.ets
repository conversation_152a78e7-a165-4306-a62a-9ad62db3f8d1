// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入自定义对话框描述器类型，用于获取自定义对话框配置信息
import type { CustomDialogDescriptor } from '../viewmodel/CustomDialogDescriptor';
// 导入自定义对话框组件，用于创建自定义对话框实例
import { CustomDialogComponent } from './CustomDialogComponent';

/**
 * 自定义对话框构建器函数
 * 用于构建自定义对话框组件的UI界面
 * @param $$ 描述器包装对象，包含自定义对话框的配置信息
 */
@Builder
export function CustomDialogBuilder($$: DescriptorWrapper) {
  // 创建自定义对话框组件，传入自定义对话框描述器配置
  CustomDialogComponent({ customDialogDescriptor: $$.descriptor as CustomDialogDescriptor })
}
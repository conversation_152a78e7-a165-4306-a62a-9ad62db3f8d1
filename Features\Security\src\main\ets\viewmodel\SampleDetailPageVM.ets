// 导入能力工具包中的通用类型
import type { common } from '@kit.AbilityKit';
// 导入方舟UI工具包中的提示操作
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误和事件发射器
import { BusinessError, emitter } from '@kit.BasicServicesKit';
// 导入网络工具包中的连接模块
import { connection } from '@kit.NetworkKit';
// 导入商店工具包中的模块安装管理器
import { moduleInstallManager } from '@kit.StoreKit';
// 导入通用模块中的相关类和工具
import {
  BaseVM,
  BaseVMEvent,
  BreakpointTypeEnum,
  CommonConstants,
  DynamicInstallManager,
  GlobalInfoModel,
  LoadingStatus,
  Logger,
  PageContext,
} from '@ohos/common';
// 导入示例详情状态相关类
import { SampleCardData, SampleDetailData, SampleDetailState } from './SampleDetailState';
// 导入单个示例数据类型
import type { SingleSampleData } from '../model/SampleDetailData';
// 导入示例详情模型
import { SampleDetailModel } from '../model/SampleDetailModel';
// 导入示例详情常量
import { SampleDetailConstant } from '../constant/CommonConstants';
// 导入示例类型枚举
import { SampleTypeEnum } from '../common/SampleConstant';

// 定义日志标签常量
const TAG = '[SampleDetailPageVM]';

// 导出示例详情页面视图模型类
export class SampleDetailPageVM extends BaseVM<SampleDetailState> {
  // 定义静态实例私有变量
  private static instance: SampleDetailPageVM;
  // 定义示例详情模型私有变量
  private sampleDetailModel = SampleDetailModel.getInstance();

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数
    super(new SampleDetailState());
  }

  // 定义获取实例的公共静态方法
  public static getInstance(): SampleDetailPageVM {
    // 如果实例不存在
    if (!SampleDetailPageVM.instance) {
      // 创建新实例
      SampleDetailPageVM.instance = new SampleDetailPageVM();
    }
    // 返回实例
    return SampleDetailPageVM.instance;
  }

  // 定义发送事件的公共方法
  public sendEvent(baseVMEvent: BaseVMEvent): void {
    // 如果是终止任务事件
    if (baseVMEvent instanceof TerminateTaskEvent) {
      // 调用终止任务方法
      this.terminateTask(baseVMEvent);
    } else if (baseVMEvent instanceof LoadSampleEvent) {
      // 调用加载示例方法
      this.loadSample();
    } else if (baseVMEvent instanceof ChangeDownloadProgressEvent) {
      // 调用更改下载进度方法
      this.changeDownloadProgress(baseVMEvent);
    } else if (baseVMEvent instanceof BindSheetEvent) {
      // 调用更改绑定表单方法
      this.changeBindSheet(baseVMEvent);
    } else if (baseVMEvent instanceof PopEvent) {
      // 调用弹出方法
      this.pop();
    } else if (baseVMEvent instanceof SetIndexEvent) {
      // 调用设置当前索引方法
      this.setCurrentIndex(baseVMEvent);
    } else if (baseVMEvent instanceof SetInstalledEvent) {
      // 调用设置安装状态方法
      this.setInstalledStatus(baseVMEvent);
    } else if (baseVMEvent instanceof InitSampleDetailEvent) {
      // 调用初始化示例详情方法
      this.initSampleDetail(baseVMEvent);
    }
  }

  // 定义初始化示例详情的私有方法
  private initSampleDetail(event: InitSampleDetailEvent): void {
    // 设置加载状态为加载中
    this.state.loadingStatus = LoadingStatus.LOADING;
    // 设置当前索引
    this.state.currentIndex = event.currentIndex;
    // 设置下载状态为false
    this.state.downloadingStatus = false;
    // 设置安装状态为false
    this.state.installingStatus = false;
    // 初始化示例数据
    this.initSampleData(event.sampleCardId);
  }

  // 定义设置安装状态的私有方法
  private setInstalledStatus(event: SetInstalledEvent): void {
    // 如果模块状态为已安装
    if (DynamicInstallManager.getModuleStatus(this.state.sampleDatas[event.sampleIndex].sampleCard.moduleName) ===
    moduleInstallManager.InstallStatus.INSTALLED) {
      // 设置示例安装状态为true
      this.setSampleInstalledStatus(event.sampleIndex, true);
    }
  }

  // 定义设置当前索引的私有方法
  private setCurrentIndex(event: SetIndexEvent): void {
    // 设置状态中的当前索引
    this.state.currentIndex = event.currentIndex;
  }

  // 定义终止任务的私有方法
  private terminateTask(event: TerminateTaskEvent): void {
    // 如果正在下载
    if (this.state.downloadingStatus) {
      // 取消下载任务
      DynamicInstallManager.cancelDownloadTask(this.state.taskId);
    }
    // 清空任务ID
    this.state.taskId = '';
    // 设置示例下载状态
    this.setSampleDownloadingStatus(event.sampleIndex, false, -1);
  }

  // 定义更改下载进度的私有方法
  private changeDownloadProgress(event: ChangeDownloadProgressEvent): void {
    // 设置示例数据中的下载进度
    this.state.sampleDatas[event.sampleIndex].sampleCard.downloadProgress = event.downloadProgress;
  }

  // 定义更改绑定表单的私有方法
  private changeBindSheet(event: BindSheetEvent): void {
    // 设置示例数据中的绑定表单显示状态
    this.state.sampleDatas[event.sampleIndex].sampleCard.bindSheetShow = event.dataValue;
  }

  // 定义弹出的私有方法
  private pop(): void {
    // 如果正在安装
    if (this.state.installingStatus) {
      // 显示安装状态提示
      promptAction.showToast({ message: $r('app.string.installing_status') });
    } else {
      // 设置返回按钮被按下状态
      this.state.isBackPressed = true;
      // 如果正在下载
      if (this.state.downloadingStatus) {
        // 设置下载状态为false
        this.state.downloadingStatus = false;
        // 取消下载任务
        DynamicInstallManager.cancelDownloadTask(this.state.taskId);
        // 清空任务ID
        this.state.taskId = '';
      }
      // 关闭动态安装事件监听
      emitter.off(CommonConstants.DYNAMIC_INSTALL_EVENT);
      // 记录取消下载监听成功日志
      Logger.info(TAG, 'cancelDownloadListener success');
      // 获取全局信息模型
      const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
      // 根据断点类型获取页面上下文
      const pageContext: PageContext =
        globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('samplePageContext')! :
          AppStorage.get('pageContext') as PageContext;
      // 弹出页面
      pageContext.popPage();
    }
  }

  // 定义初始化示例数据的私有方法
  private initSampleData(sampleCardId: number): void {
    // 创建示例详情数据数组
    const sampleDetailData: SampleDetailData[] = [];
    // 清空状态中的示例数据
    this.state.sampleDatas = [];
    // 获取示例卡片详情
    this.sampleDetailModel.getSampleCardDetails(sampleCardId).then((result: SingleSampleData[]) => {
      // 设置示例数量
      this.state.sampleCount = result.length;
      // 遍历单个示例数据
      result.forEach((singleSampleData: SingleSampleData) => {
        // 创建示例详情数据对象
        const sampleData: SampleDetailData = new SampleDetailData();
        // 创建示例卡片数据对象
        sampleData.sampleCard = new SampleCardData();
        // 设置示例数据ID和示例卡片ID
        sampleData.id = sampleData.sampleCard.sampleId = singleSampleData.id;
        // 设置是否收藏
        sampleData.isFavorite = singleSampleData.isFavorite;
        // 设置媒体URL
        sampleData.mediaUrl = singleSampleData.mediaUrl;
        // 设置媒体类型
        sampleData.mediaType = singleSampleData.mediaType;
        // 设置示例卡片标题
        sampleData.sampleCard.title = singleSampleData.title;
        // 设置示例卡片安装状态
        sampleData.sampleCard.installed = singleSampleData.preInstalled;
        // 设置示例卡片类型
        sampleData.sampleCard.sampleType = singleSampleData.sampleType;
        // 设置示例卡片描述
        sampleData.sampleCard.desc = singleSampleData.desc;
        // 设置示例卡片原始URL
        sampleData.sampleCard.originalUrl = singleSampleData.originalUrl;
        // 设置示例卡片模块名称
        sampleData.sampleCard.moduleName = singleSampleData.moduleName;
        // 设置示例卡片能力名称
        sampleData.sampleCard.abilityName = singleSampleData.abilityName;
        // 如果模块状态为已安装
        if (DynamicInstallManager.getModuleStatus(sampleData.sampleCard.moduleName) ===
        moduleInstallManager.InstallStatus.INSTALLED) {
          // 设置示例卡片安装状态为true
          sampleData.sampleCard.installed = true;
        }
        // 添加到示例详情数据数组
        sampleDetailData.push(sampleData);
      });
      // 设置状态中的示例数据
      this.state.sampleDatas = sampleDetailData;
      // 初始化下载监听器
      this.initDownloadListener();
      // 设置加载状态为成功
      this.state.loadingStatus = LoadingStatus.SUCCESS;
    }).catch(() => {
      // 设置加载状态为失败
      this.state.loadingStatus = LoadingStatus.FAILED;
    });
  }

  // 定义初始化下载监听器的私有方法
  private initDownloadListener(): void {
    // 监听动态安装事件
    emitter.on(CommonConstants.DYNAMIC_INSTALL_EVENT, (eventData) => {
      // 如果任务状态为下载中
      if (eventData.data?.taskStatus === moduleInstallManager.TaskStatus.DOWNLOADING) {
        // 如果下载大小等于总大小
        if (eventData.data?.downloadedSize === eventData.data?.totalSize) {
          // 设置安装状态为true
          this.setInstallingStatus(true);
        }
        // 设置示例下载状态和进度
        this.setSampleDownloadingStatus(this.state.currentIndex, true,
          Math.floor(((eventData.data?.downloadedSize ?? SampleDetailConstant.PROGRESS_START) /
            (eventData.data?.totalSize ?? SampleDetailConstant.PROGRESS_FINISH)) *
          SampleDetailConstant.PROGRESS_FINISH));
        // 记录下载大小日志
        Logger.info(TAG, `loadSampleCallback downloading size: ${eventData.data?.downloadedSize}`);
      } else if (eventData.data?.taskStatus === moduleInstallManager.TaskStatus.INSTALL_SUCCESSFUL) {
        // 设置示例安装状态为true
        this.setSampleInstalledStatus(this.state.currentIndex, true);
        // 设置示例下载状态为false
        this.setSampleDownloadingStatus(this.state.currentIndex, false, -1);
        // 设置安装状态为false
        this.setInstallingStatus(false);
        // 加载模块
        DynamicInstallManager.loadModule(getContext(this) as common.UIAbilityContext,
          this.state.sampleDatas[this.state.currentIndex].sampleCard.abilityName);
        // 记录安装成功日志
        Logger.info(TAG,
          `loadSampleCallback installed : ${this.state.sampleDatas[this.state.currentIndex].sampleCard.abilityName}`);
      } else if (eventData.data?.taskStatus === moduleInstallManager.TaskStatus.INSTALL_WAITING ||
        eventData.data?.taskStatus === moduleInstallManager.TaskStatus.INSTALLING) {
        // 设置安装状态为true
        this.setInstallingStatus(true);
        // 记录安装中日志
        Logger.info(TAG, `loadSampleCallback installing`);
      } else if (eventData.data?.taskStatus === moduleInstallManager.TaskStatus.TASK_UNFOUND) {
        // 取消订阅下载进度
        DynamicInstallManager.unsubscribeDownloadProgress();
      }
    });
    // 记录初始化下载监听器成功日志
    Logger.info(TAG, 'initDownloadListener success');
  }

  // 定义加载示例的私有方法
  private loadSample(): void {
    // 如果当前索引小于0或示例数据不存在
    if (this.state.currentIndex < 0 || !this.state.sampleDatas[this.state.currentIndex]) {
      // 直接返回
      return;
    }
    // 获取当前示例类型
    const currentSampleType = this.state.sampleDatas[this.state.currentIndex].sampleCard.sampleType;
    // 如果是通用客户端类型
    if (currentSampleType === SampleTypeEnum.COMMON_CLIENT) {
      // 显示客户端提示
      promptAction.showToast({ message: $r('app.string.client_prompt') });
    } else if (currentSampleType === SampleTypeEnum.WEARABLE_CLIENT) {
      // 显示手表提示
      promptAction.showToast({ message: $r('app.string.watch_prompt') });
    } else {
      // 开始任务
      this.startTask();
    }
  }

  // 定义开始任务的私有方法
  private startTask(): void {
    // 如果示例已安装
    if (this.state.sampleDatas[this.state.currentIndex].sampleCard.installed) {
      // 获取UI能力上下文
      const ctx: common.UIAbilityContext = getContext() as common.UIAbilityContext;
      try {
        // 加载模块
        DynamicInstallManager.loadModule(ctx, this.state.sampleDatas[this.state.currentIndex].sampleCard.abilityName);
      } catch (error) {
        // 记录加载模块失败错误日志
        Logger.error(TAG, `Failed to loadModule, error code: ${error.code}, error data: ${error.message}`);
      }
    } else {
      // 订阅下载进度
      DynamicInstallManager.subscribeDownloadProgress();
      // 设置示例下载状态
      this.setSampleDownloadingStatus(this.state.currentIndex, false, -1);
      // 获取默认网络
      connection.getDefaultNet().then((netHandle: connection.NetHandle) => {
        // 获取网络能力
        connection.getNetCapabilities(netHandle).then((data: connection.NetCapabilities) => {
          // 记录获取连接数据成功日志
          Logger.info(TAG, `succeeded to get connection data: ${data.bearerTypes[0]}`);
          // 如果是WiFi连接
          if (data.bearerTypes[0] === connection.NetBearType.BEARER_WIFI) {
            // 设置示例下载状态
            this.setSampleDownloadingStatus(this.state.currentIndex, true, 0);
          }
        }).catch((error: BusinessError) => {
          // 记录获取网络能力失败错误日志
          Logger.error(TAG, `Failed to getNetCapabilities, error code: ${error.code}, error data: ${error.message}`);
        })
      }).catch((error: BusinessError) => {
        // 记录获取连接失败错误日志
        Logger.error(TAG, `Failed to get connection error code: ${error.code}, error data: ${error.message}`);
      });
      // 开始下载
      this.startDownload();
    }
  }

  // 定义开始下载的私有方法
  private startDownload(): void {
    // 记录开始下载示例日志
    Logger.info(TAG, `start download sample: ${this.state.sampleDatas[this.state.currentIndex].sampleCard.title}`);
    // 获取UI能力上下文
    const ctx: common.UIAbilityContext = getContext() as common.UIAbilityContext;
    // 获取模块
    DynamicInstallManager.fetchModule(ctx, this.state.sampleDatas[this.state.currentIndex].sampleCard.moduleName)
      .then((data: moduleInstallManager.ModuleInstallSessionState) => {
        // 如果请求成功
        if (data.code === moduleInstallManager.RequestErrorCode.SUCCESS) {
          // 设置任务ID
          this.state.taskId = data.taskId;
        } else if (data.code === moduleInstallManager.RequestErrorCode.DOWNLOAD_WAIT_WIFI) {
          // 显示蜂窝数据确认对话框
          moduleInstallManager.showCellularDataConfirmation(ctx, data.taskId);
          // 设置任务ID
          this.state.taskId = data.taskId;
        } else {
          // 如果模块不可用
          if (data.code === moduleInstallManager.RequestErrorCode.MODULE_UNAVAILABLE) {
            // 显示模块不存在提示
            promptAction.showToast({ message: $r('app.string.module_nonexistent') });
          } else if (data.code === moduleInstallManager.RequestErrorCode.NETWORK_ERROR) {
            // 显示网络错误提示
            promptAction.showToast({ message: $r('app.string.internet_error') });
          } else if (data.code === moduleInstallManager.RequestErrorCode.INVALID_REQUEST) {
            // 显示请求信息错误提示
            promptAction.showToast({ message: $r('app.string.requestinfo_error') });
          }
          // 设置示例下载状态为false
          this.setSampleDownloadingStatus(this.state.currentIndex, false, -1);
        }
      }).catch((error: BusinessError) => {
      // 记录获取模块失败错误日志
      Logger.error(TAG, `Failed to fetchModule. error code: ${error.code}, error data: ${error.message}`);
    });
  }

  // 定义设置示例下载状态的私有方法
  private setSampleDownloadingStatus(sampleIndex: number, downloading: boolean, progress: number): void {
    // 设置示例数据中的下载状态
    this.state.sampleDatas[sampleIndex].sampleCard.downloading = downloading;
    // 设置示例数据中的下载进度
    this.state.sampleDatas[sampleIndex].sampleCard.downloadProgress = progress;
    // 设置状态中的下载状态
    this.state.downloadingStatus = downloading;
  }

  // 定义设置示例安装状态的私有方法
  private setSampleInstalledStatus(sampleIndex: number, installed: boolean): void {
    // 设置示例数据中的安装状态
    this.state.sampleDatas[sampleIndex].sampleCard.installed = installed;
  }

  // 定义设置安装状态的私有方法
  private setInstallingStatus(installing: boolean): void {
    // 设置状态中的安装状态
    this.state.installingStatus = installing;
  }
}

// 导出绑定表单事件类
export class BindSheetEvent implements BaseVMEvent {
  // 定义示例索引只读属性
  public readonly sampleIndex: number;
  // 定义数据值只读属性
  public readonly dataValue: boolean;

  // 定义构造函数
  public constructor(sampleIndex: number, dataValue: boolean) {
    // 设置示例索引
    this.sampleIndex = sampleIndex;
    // 设置数据值
    this.dataValue = dataValue;
  }
}

// 导出更改示例数据事件类
export class ChangeSampleDataEvent implements BaseVMEvent {
  // 定义示例卡片ID只读属性
  public readonly sampleCardId: number;

  // 定义构造函数
  public constructor(sampleCardId: number) {
    // 设置示例卡片ID
    this.sampleCardId = sampleCardId;
  }
}

// 导出终止任务事件类
export class TerminateTaskEvent implements BaseVMEvent {
  // 定义示例索引只读属性
  public readonly sampleIndex: number;

  // 定义构造函数
  public constructor(sampleIndex: number) {
    // 设置示例索引
    this.sampleIndex = sampleIndex;
  }
}

// 导出加载示例事件类
export class LoadSampleEvent implements BaseVMEvent {
}

// 导出更改下载进度事件类
export class ChangeDownloadProgressEvent implements BaseVMEvent {
  // 定义示例索引只读属性
  public readonly sampleIndex: number;
  // 定义下载进度只读属性
  public readonly downloadProgress: number;

  // 定义构造函数
  public constructor(sampleIndex: number, downloadProgress: number) {
    // 设置示例索引
    this.sampleIndex = sampleIndex;
    // 设置下载进度
    this.downloadProgress = downloadProgress;
  }
}

// 导出弹出事件类
export class PopEvent implements BaseVMEvent {
}

// 导出设置索引事件类
export class SetIndexEvent implements BaseVMEvent {
  // 定义当前索引只读属性
  public readonly currentIndex: number;

  // 定义构造函数
  public constructor(currentIndex: number) {
    // 设置当前索引
    this.currentIndex = currentIndex;
  }
}

// 导出设置安装事件类
export class SetInstalledEvent implements BaseVMEvent {
  // 定义示例索引只读属性
  public readonly sampleIndex: number;

  // 定义构造函数
  public constructor(sampleIndex: number) {
    // 设置示例索引
    this.sampleIndex = sampleIndex;
  }
}

// 导出初始化示例详情事件类
export class InitSampleDetailEvent implements BaseVMEvent {
  // 定义示例卡片ID只读属性
  public readonly sampleCardId: number;
  // 定义当前索引只读属性
  public readonly currentIndex: number;

  // 定义构造函数
  public constructor(sampleCardId: number, currentIndex: number) {
    // 设置示例卡片ID
    this.sampleCardId = sampleCardId;
    // 设置当前索引
    this.currentIndex = currentIndex;
  }
}
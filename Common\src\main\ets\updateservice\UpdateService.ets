// 导入StoreKit包中的更新管理器
import { updateManager } from '@kit.StoreKit';
// 导入AbilityKit包中的通用类型
import type { common } from '@kit.AbilityKit';
// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from '../util/Logger';

// 日志标签常量
const TAG: string = '[UpdateService]';

/**
 * 应用更新服务类
 * 提供应用版本检查和更新功能
 * 使用单例模式确保全局唯一实例
 * 封装华为应用市场的更新管理器功能
 */
export class UpdateService {
  // 静态单例实例
  private static instance: UpdateService;

  /**
   * 私有构造函数
   * 防止外部直接实例化，确保单例模式
   */
  private constructor() {
  }

  /**
   * 获取单例实例方法
   * 如果实例不存在则创建新实例
   * @returns UpdateService 更新服务实例
   */
  public static getInstance(): UpdateService {
    // 如果实例不存在
    if (!UpdateService.instance) {
      // 创建新的实例
      UpdateService.instance = new UpdateService();
      // 返回新创建的实例
      return UpdateService.instance;
    }
    // 返回已存在的实例
    return UpdateService.instance;
  }

  /**
   * 检查应用更新方法
   * 检查是否有新版本可用
   * @returns Promise<boolean> 是否有更新可用的Promise
   */
  public checkUpdate(): Promise<boolean> {
    // 返回一个新的Promise
    return new Promise((resolve: Function, reject: Function) => {
      try {
        // 获取UI能力上下文
        const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
        // 调用更新管理器检查应用更新
        updateManager.checkAppUpdate(context)
          .then((checkResult: updateManager.CheckUpdateResult) => {
            // 记录检查结果信息日志
            Logger.info(TAG, `Succeeded in checking Result updateAvailable:` + checkResult.updateAvailable);
            // 如果有新版本存在
            if (checkResult.updateAvailable === updateManager.UpdateAvailableCode.LATER_VERSION_EXIST) {
              // 解析Promise为true
              resolve(true);
            } else {
              // 解析Promise为false
              resolve(false);
            }
          })
          .catch((error: BusinessError) => {
            // 记录检查更新失败的错误日志
            Logger.error(TAG, `checkAppUpdate onError.code is ${error.code}, message is ${error.message}`);
            // 拒绝Promise
            reject(false);
          });
      } catch (error) {
        // 记录异常错误日志
        Logger.error(TAG, `checkAppUpdate onError.code is ${error.code}, message is ${error.message}`);
        // 拒绝Promise
        reject(false);
      }
    })
  }

  /**
   * 更新应用版本方法
   * 显示更新对话框，引导用户进行应用更新
   * @returns Promise<boolean> 显示更新对话框是否成功的Promise
   */
  public updateVersion(): Promise<boolean> {
    // 返回一个新的Promise
    return new Promise((resolve: Function, reject: Function) => {
      // 获取UI能力上下文
      const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
      try {
        // 调用更新管理器显示更新对话框
        updateManager.showUpdateDialog(context)
          .then((resultCode: updateManager.ShowUpdateResultCode) => {
            // 记录显示更新对话框成功的信息日志
            Logger.info(TAG, `Succeeded in showing UpdateDialog resultCode:` + resultCode);
            // 解析Promise为true
            resolve(true);
          })
          .catch((error: BusinessError) => {
            // 记录显示更新对话框失败的错误日志
            Logger.error(TAG, `showUpdateDialog onError.code is ${error.code}, message is ${error.message}`);
            // 拒绝Promise
            reject(false);
          });
      } catch (error) {
        // 记录异常错误日志
        Logger.error(TAG, `showUpdateDialog onError.code is ${error.code}, message is ${error.message}`);
        // 拒绝Promise
        reject(false);
      }
    });
  }
}
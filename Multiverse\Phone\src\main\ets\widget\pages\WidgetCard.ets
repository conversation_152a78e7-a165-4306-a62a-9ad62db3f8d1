/**
 * 小组件卡片组件
 * 桌面小组件的主要显示内容
 * 包含背景图片、标题和详情文本
 */
@Entry
@Component
struct WidgetCard {
  // 文本最大行数限制
  readonly MAX_LINES: number = 1;
  // 操作类型，设置为路由跳转
  readonly ACTION_TYPE: string = 'router';
  // 目标能力名称，指向主入口能力
  readonly ABILITY_NAME: string = 'EntryAbility';
  // 宽度百分比设置，占满容器宽度
  readonly FULL_WIDTH_PERCENT: string = '100%';
  // 高度百分比设置，占满容器高度
  readonly FULL_HEIGHT_PERCENT: string = '100%';

  /**
   * 构建小组件卡片的UI结构
   * 使用FormLink创建可点击的小组件链接
   */
  build() {
    // 创建表单链接，点击时跳转到指定能力
    FormLink({
      // 设置操作类型为路由跳转
      action: this.ACTION_TYPE,
      // 设置目标能力名称
      abilityName: this.ABILITY_NAME,
    }) {
      // 创建堆叠容器，用于叠加背景图片和文本内容
      Stack() {
        // 背景图片
        Image($r('app.media.ic_widget'))
          // 设置图片宽度为100%
          .width(this.FULL_WIDTH_PERCENT)
          // 设置图片高度为100%
          .height(this.FULL_HEIGHT_PERCENT)
        // 文本内容列容器
        Column() {
          // 主标题文本
          Text($r('app.string.title_immersive'))
            // 设置字体大小为中等副标题
            .fontSize($r('sys.float.Subtitle_M'))
            // 设置文本溢出时显示省略号
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            // 设置主要字体颜色
            .fontColor($r('sys.color.font_on_primary'))
            // 设置字体粗细为粗体
            .fontWeight(FontWeight.Bold)
            // 设置最大行数
            .maxLines(this.MAX_LINES)
          // 详情文本
          Text($r('app.string.detail_immersive'))
            // 设置字体大小为小号正文
            .fontSize($r('sys.float.Body_S'))
            // 设置顶部边距
            .margin({ top: $r('sys.float.padding_level1') })
            // 设置文本溢出时显示省略号
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            // 设置次要字体颜色
            .fontColor($r('sys.color.font_on_secondary'))
            // 设置字体粗细为常规
            .fontWeight(FontWeight.Regular)
            // 设置最大行数
            .maxLines(this.MAX_LINES)
        }
        // 设置列容器宽度为100%
        .width(this.FULL_WIDTH_PERCENT)
        // 设置列容器高度为100%
        .height(this.FULL_HEIGHT_PERCENT)
        // 设置水平对齐方式为左对齐
        .alignItems(HorizontalAlign.Start)
        // 设置垂直对齐方式为顶部对齐
        .justifyContent(FlexAlign.Start)
        // 设置内边距
        .padding($r('sys.float.padding_level6'))
      }
      // 设置堆叠容器宽度为100%
      .width(this.FULL_WIDTH_PERCENT)
      // 设置堆叠容器高度为100%
      .height(this.FULL_HEIGHT_PERCENT)
    }
  }
}
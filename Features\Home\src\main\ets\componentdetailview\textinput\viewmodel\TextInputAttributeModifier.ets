// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入文本输入描述器类型，用于描述文本输入组件配置
import type { TextInputDescriptor } from './TextInputDescriptor';

/**
 * 文本输入属性修改器类
 * 继承自通用属性修改器，用于修改文本输入组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class TextInputAttributeModifier extends CommonAttributeModifier<TextInputDescriptor, TextInputAttribute> {
  /**
   * 应用普通属性到文本输入组件实例
   * 将描述器中的属性值应用到实际的文本输入组件上
   * @param instance 文本输入属性实例，用于设置文本输入组件属性
   */
  applyNormalAttribute(instance: TextInputAttribute): void {
    // 分配输入类型属性，将描述器中的type值应用到组件实例
    this.assignAttribute((descriptor => descriptor.type), (val) => instance.type(val));
  }
}
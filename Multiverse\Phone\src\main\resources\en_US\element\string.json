{"string": [{"name": "module_desc", "value": "module description"}, {"name": "EntryAbility_desc", "value": "description"}, {"name": "EntryAbility_label", "value": "Evolution of the Dao"}, {"name": "internet_reason", "value": "You need to obtain your network permission to obtain network resource display data."}, {"name": "network_reason", "value": "You need to obtain your network status information to determine whether the current device is connected to the network."}, {"name": "vibrator_reason", "value": "You need to obtain your vibrator permission to provide vibrator feel"}, {"name": "tab_home", "value": "Home"}, {"name": "tab_security", "value": "Security"}, {"name": "tab_community", "value": "Community"}, {"name": "tab_mine", "value": "Me"}, {"name": "PhoneFormAbility_desc", "value": "form_description"}, {"name": "PhoneFormAbility_label", "value": "form_label"}, {"name": "widget_desc", "value": "Harmony Application Development Assistant"}, {"name": "widget_display_name", "value": "<PERSON><PERSON> in HarmonyOS"}, {"name": "title_immersive", "value": "<PERSON><PERSON> in HarmonyOS"}, {"name": "detail_immersive", "value": "Harmony Application Development Assistant"}, {"name": "back_toast", "value": "Swiper again to exit."}]}
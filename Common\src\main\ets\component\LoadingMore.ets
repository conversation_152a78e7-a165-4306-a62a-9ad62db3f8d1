/**
 * 加载更多组件
 * 用于在列表底部显示加载状态，包含加载动画和加载文字提示
 * 通常用于分页加载场景，提示用户正在加载更多数据
 */
@Builder
export function LoadingMore() {
  // 创建水平排列的行容器，用于放置加载动画和文字
  Row() {
    // 创建加载进度指示器组件
    LoadingProgress()
      // 设置加载动画的颜色为系统主要字体颜色
      .color($r('sys.color.font_primary'))
      // 设置加载动画的尺寸，宽度和高度都使用系统预定义的12级内边距值
      .size({ width: $r('sys.float.padding_level12'), height: $r('sys.float.padding_level12') })

    // 创建加载提示文字组件
    Text($r('app.string.loading'))
      // 设置文字颜色为系统主要字体颜色
      .fontColor($r('sys.color.font_primary'))
      // 设置文字大小为系统预定义的小标题字号
      .fontSize($r('sys.float.Subtitle_S'))
      // 设置文字左边距，与加载动画保持适当间距
      .margin({ left: $r('sys.float.padding_level4') })
  }
  // 设置行容器宽度为100%，占满父容器
  .width('100%')
  // 设置行容器高度为应用预定义的加载更多组件高度
  .height($r('app.float.loading_more_height'))
  // 设置子元素在主轴方向居中对齐
  .justifyContent(FlexAlign.Center)
  // 设置行容器底部外边距，与下方内容保持间距
  .margin({ bottom: $r('sys.float.padding_level6') })
}
// 导入可观察数组类型，用于响应式数据处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 堆叠布局属性过滤器类
 * 实现通用属性过滤器接口，用于根据对齐方向控制相关对齐内容属性的启用状态
 */
export class StackAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据对齐方向控制顶部、居中、底部对齐内容属性的启用状态
   * @param attributes 属性数组，包含所有组件属性
   */
  filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，根据属性名称进行过滤处理
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'alignDirection':
          // 查找顶部对齐内容属性的索引位置
          const topIndex = attributes.findIndex((item) => item.name === 'alignContentTop');
          // 查找居中对齐内容属性的索引位置
          const centerIndex = attributes.findIndex((item) => item.name === 'alignContentCenter');
          // 查找底部对齐内容属性的索引位置
          const bottomIndex = attributes.findIndex((item) => item.name === 'alignContentBottom');
          // 如果找到了所有对齐内容属性
          if (topIndex !== -1 && centerIndex !== -1 && bottomIndex !== -1) {
            // 如果对齐方向为顶部，启用顶部对齐内容，禁用其他
            if (attribute.currentValue === 'Top') {
              attributes[topIndex].enable = true;
              attributes[centerIndex].enable = false;
              attributes[bottomIndex].enable = false;
            } else if (attribute.currentValue === 'Center') {
              // 如果对齐方向为居中，启用居中对齐内容，禁用其他
              attributes[topIndex].enable = false;
              attributes[centerIndex].enable = true;
              attributes[bottomIndex].enable = false;
            } else if (attribute.currentValue === 'Bottom') {
              // 如果对齐方向为底部，启用底部对齐内容，禁用其他
              attributes[topIndex].enable = false;
              attributes[centerIndex].enable = false;
              attributes[bottomIndex].enable = true;
            }
          }
          break;
        default:
          // 其他属性不做处理
          break;
      }
    });
  }
}
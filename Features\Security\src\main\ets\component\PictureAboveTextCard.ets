// 导入通用模块中的断点类型、断点类型枚举、通用常量和全局信息模型
import { BreakpointType, BreakpointTypeEnum, CommonConstants, GlobalInfoModel } from '@ohos/common';
// 导入示例数据模型类型
import type { SampleCardData, SampleContent } from '../model/SampleData';
// 导入标签标签组件
import { TagLabel } from './TagLabel';
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用业务模块中的卡片样式类型枚举
import { CardStyleTypeEnum } from '@ohos/commonbusiness';

// 定义图片大小常量
const IMAGE_SIZE: number = 40;
// 定义标签内边距常量
const TAG_PADDING: number = 32;

// 使用Component装饰器定义图片上方文本卡片组件
@Component
export struct PictureAboveTextCard {
  // 使用StorageProp和Watch装饰器获取全局信息模型并监听变化
  @StorageProp('GlobalInfoModel') @Watch('calculateTagMaxWidth') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp装饰器获取系统颜色模式
  @StorageProp('systemColorMode') systemColorMode: ConfigurationConstant.ColorMode = AppStorage.get('systemColorMode')!;
  // 使用Prop装饰器定义示例卡片数据属性
  @Prop sampleCardData: SampleCardData;
  // 使用State装饰器定义示例内容状态
  @State sampleContent?: SampleContent = undefined;
  // 使用State装饰器定义最大宽度状态
  @State maxWidth: number = 0;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 获取示例卡片数据的第一个内容
    this.sampleContent = this.sampleCardData.sampleContents?.[0];
    // 计算标签最大宽度
    this.calculateTagMaxWidth();
  }

  // 定义计算标签最大宽度的方法
  calculateTagMaxWidth() {
    // 根据断点类型获取侧边栏宽度
    let barWidth: number = new BreakpointType({
      sm: 0,
      md: 0,
      lg: CommonConstants.TAB_BAR_WIDTH,
      xl: CommonConstants.SIDE_BAR_WIDTH,
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 根据断点类型获取内边距值
    const paddingVal = new BreakpointType({
      sm: CommonConstants.SPACE_16,
      md: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SPACE_32 :
          CommonConstants.SPACE_24,
      lg: CommonConstants.SPACE_32,
      xl: CommonConstants.SPACE_32,
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 计算当前宽度
    const currentWidth: number = this.globalInfoModel.deviceWidth - barWidth;
    // 根据断点类型获取列数
    const count: number = new BreakpointType({
      sm: 1,
      md: 2,
      lg: 3,
      xl: 3,
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 定义间距
    const gutter = CommonConstants.SPACE_16;
    // 计算最大宽度
    this.maxWidth = Math.floor((currentWidth - paddingVal * 2 - gutter * (count - 1)) / count) - TAG_PADDING -
      IMAGE_SIZE - CommonConstants.SPACE_16;
  }

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 创建图片行布局
      Row() {
        // 显示示例内容图片
        Image($rawfile(this.sampleContent?.mediaUrl))
          .alt($r('app.media.img_placeholder'))
          .height('100%')
          .height('100%')
          .objectFit(ImageFit.Contain)
          .draggable(true)
      }
      // 设置行布局居中对齐
      .justifyContent(FlexAlign.Center)
      // 设置行布局宽度为100%
      .width('100%')
      // 设置行布局裁剪
      .clip(true)
      // 设置行布局权重为1
      .layoutWeight(1)
      // 设置行布局内边距
      .padding({ top: $r('sys.float.padding_level16'), bottom: $r('sys.float.padding_level8') })

      // 创建内容行布局
      Row() {
        // 创建文本列布局
        Column() {
          // 显示示例内容标题
          Text(this.sampleContent?.title)
            .fontSize($r('sys.float.Subtitle_M'))
            .fontWeight(FontWeight.Medium)
            .fontColor(this.sampleCardData.cardStyleType === CardStyleTypeEnum.PICTURE_TO_SWIPER ?
            $r('app.color.card_font_primary_color') : $r('sys.color.font_primary'))
          // 显示标签标签组件
          TagLabel({
            maxWidth: this.maxWidth,
            tags: this.sampleContent?.tags || [],
            cardStyleType: this.sampleCardData.cardStyleType
          })
        }
        // 设置文本列左对齐
        .alignItems(HorizontalAlign.Start)
        // 设置文本列权重为1
        .layoutWeight(1)

        // 创建按钮
        Button({ type: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? ButtonType.ROUNDED_RECTANGLE :
                ButtonType.Circle }) {
          // 显示右箭头符号
          SymbolGlyph($r('sys.symbol.chevron_right'))
            .fontColor([this.sampleCardData.cardStyleType === CardStyleTypeEnum.PICTURE_TO_SWIPER ?
            $r('app.color.icon_secondary_color') : $r('sys.color.icon_secondary')])
            .fontSize($r('sys.float.Title_M'))
        }
        // 设置按钮左边距
        .margin({ left: $r('sys.float.padding_level6') })
        // 设置按钮高度
        .height($r('app.float.card_button_height'))
        // 设置按钮宽高比为1:1
        .aspectRatio(1)
        // 设置按钮背景颜色
        .backgroundColor($r('sys.color.comp_background_tertiary'))
      }
      // 设置内容行内边距
      .padding({
        left: $r('sys.float.padding_level8'),
        right: $r('sys.float.padding_level8'),
        top: $r('sys.float.padding_level6'),
        bottom: $r('sys.float.padding_level10'),
      })
      // 设置内容行高度
      .height($r('app.float.picture_card_content_height'))
      // 根据卡片样式类型设置内容行背景颜色
      .backgroundColor(this.sampleCardData.cardStyleType === CardStyleTypeEnum.PICTURE_TO_SWIPER ?
      $r('app.color.card_color') : $r('sys.color.comp_background_secondary'))
    }
    // 设置主列裁剪
    .clip(true)
    // 设置主列背景颜色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置主列背景图片
    .backgroundImage($rawfile(this.sampleCardData.cardImage))
    // 设置主列背景图片大小
    .backgroundImageSize({ width: '100%', height: '100%' })
    // 设置主列点击效果
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置主列边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 设置主列高度
    .height($r('app.float.picture_card_height'))
  }
}
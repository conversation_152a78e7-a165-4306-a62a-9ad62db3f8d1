// 导入能力工具包中的通用模块和配置常量
import { common, ConfigurationConstant } from '@kit.AbilityKit';
// 导入数据工具包中的统一类型描述符
import { uniformTypeDescriptor as utd } from '@kit.ArkData';
// 导入UI工具包中的提示操作和窗口模块
import { promptAction, window } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误和剪贴板模块
import { BusinessError, pasteboard } from '@kit.BasicServicesKit';
// 导入分享工具包中的系统分享模块
import { systemShare } from '@kit.ShareKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的各种工具类和常量
import {
  BreakpointTypeEnum,
  CommonConstants,
  Logger,
  ScreenOrientation,
  WebNodeController,
  WindowUtil,
} from '@ohos/common';
// 导入代码预览JavaScript工具类
import { CodePreviewJSUtil } from '../util/CodePreviewJSUtil';

// 日志标签
const TAG: string = '[CodePreviewComponent]';

/**
 * 代码预览组件
 * 用于显示和预览代码内容，支持多种交互功能
 * 包括代码复制、颜色模式切换、屏幕方向切换等
 */
@Component
export struct CodePreviewComponent {
  // 系统颜色模式，监听变化并触发处理方法
  @StorageProp('systemColorMode') @Watch('onHandleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 代码内容，监听变化并刷新代码显示
  @Prop @Watch('flushCode') code: string = '';
  // 是否为页面容器模式
  @Prop pageContainer: boolean = false;
  // 是否获得焦点
  @Prop isFocus: boolean = false;
  // 是否为返回状态
  @Prop isBack: boolean = false;
  // 顶部平移Y坐标
  @Prop topTranslateY: number = 0;
  // 底部平移Y坐标
  // 使用Prop装饰器定义底部平移Y坐标属性
  @Prop bottomTranslateY: number = 0;
  // 使用Prop装饰器定义导航栏透明度属性
  @Prop navigationOpacity: number = 1;
  // 定义当前颜色模式，从系统颜色模式获取
  colorMode: ConfigurationConstant.ColorMode = this.systemColorMode;
  // 使用State装饰器定义是否为深色模式状态
  @State isDarkMode: boolean = (this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
  // 使用State装饰器定义屏幕方向状态，根据断点类型判断横屏或竖屏
  @State screenOrientation: string = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ?
  ScreenOrientation.LANDSCAPE : ScreenOrientation.PORTRAIT;
  // 使用State装饰器定义是否为浮动窗口类型状态
  @State isFloatWindowType: boolean = false;
  // 使用State装饰器定义是否处于悬停状态
  @State isHover: boolean = false;
  // 使用State装饰器定义当前选中的图标索引
  @State currentIndex: number = 0;
  // 使用State装饰器定义返回图标背景颜色，根据断点类型设置透明或背景色
  @State backIconBgColor: ResourceColor =
    this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? Color.Transparent :
    $r('sys.color.comp_background_tertiary');
  // 使用State装饰器定义分享图标背景颜色，根据断点类型设置透明或背景色
  @State shareIconBgColor: ResourceColor =
    this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? Color.Transparent :
    $r('sys.color.comp_background_tertiary');
  // 使用Link装饰器定义Web节点控制器链接
  @Link webNodeController: WebNodeController;
  // 定义返回页面回调函数类型
  onBackPage?: () => void;
  // 定义公共推送页面回调函数类型
  public pushPage?: (value: string) => void;
  // 定义公共组件名称属性
  public componentName: string = '';
  // 定义私有窗口类实例，初始为undefined
  private windowClass: window.Window | undefined = undefined;
  // 定义私有顶部图标项数组
  private topIconItems: ItemData[] = [
    // 创建新的图标数据项
    new ItemData(
      // 设置图标资源对象
      {
        iconResource: $r('sys.symbol.arrow_up_left_and_arrow_down_right'),
      } as ResourceInterface,
      // 设置点击回调函数
      () => {
        // 调用推送页面方法并传递代码内容
        this.pushPage?.(this.code);
      }, 0)
  ];
  // 定义私有底部图标项数组
  private bottomIconItems: ItemData[] = [
    // 创建新的图标数据项
    new ItemData(
      // 设置图标资源对象，根据断点类型选择不同图标
      {
        iconResource: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
        $r('sys.symbol.plus_square_on_square') : $r('sys.symbol.plus_square_on_square_fill'),
        iconTitle: $r('app.string.copy')
      } as ResourceInterface,
      // 设置点击回调函数
      () => {
        // 调用复制文本方法并传递代码内容
        this.copyText(this.code);
      }, 0),
    // 创建新的图标数据项用于颜色模式切换
    new ItemData(
      // 设置默认图标资源对象，根据断点类型选择不同图标
      {
        iconResource: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
        $r('sys.symbol.sun_max') : $r('sys.symbol.sun_max_fill'),
        iconTitle: $r('app.string.dark_mode'),
      } as ResourceInterface,
      // 设置点击回调函数
      () => {
        // 调用切换颜色模式方法
        this.changeColorMode();
      }, 1,
      // 设置替代图标资源对象，根据断点类型选择不同图标
      {
        iconResource: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
        $r('sys.symbol.moon') : $r('sys.symbol.moon_fill'),
        iconTitle: $r('app.string.light_mode'),
      } as ResourceInterface),
    // 创建新的图标数据项用于屏幕方向切换
    new ItemData(
      // 设置图标资源对象
      {
        iconResource: $r('sys.symbol.screen_rotation'),
        iconTitle: this.screenOrientation === ScreenOrientation.PORTRAIT ? $r('app.string.landscape_screen') :
        $r('app.string.portrait_screen')
      } as ResourceInterface,
      // 设置点击回调函数
      () => {
        // 调用切换屏幕方向方法
        this.changeScreenOrientation();
      }, 2)
  ];
  // 定义私有PC端底部图标项数组，取底部图标项的第一个元素
  private pcBottomIconItems: ItemData[] = this.bottomIconItems.slice(0, 1);
  // 定义私有开始断点类型，从全局信息模型获取当前断点
  private beginBreakpoint: BreakpointTypeEnum = this.globalInfoModel.currentBreakpoint;
  // 定义私有窗口大小变化回调函数
  private windowSizeCallback: Callback<window.Size> = (size) => {
    // 判断窗口宽度是否大于高度
    if (size.width > size.height) {
      // 设置屏幕方向为横屏
      this.screenOrientation = ScreenOrientation.LANDSCAPE;
      // 判断是否为页面容器且不是返回状态
      if (this.pageContainer && !this.isBack) {
        // 根据是否为浮动窗口类型选择显示方法
        const showLandscapeMethod: string =
          this.isFloatWindowType ? 'showLandscapeFloatView(%param)' : 'showLandscapeView(%param)';
        // 将开始断点类型转换为JSON字符串
        const params: string = JSON.stringify(this.beginBreakpoint);
        // 调用代码预览JavaScript工具运行JS方法
        CodePreviewJSUtil.codeViewRunJS(showLandscapeMethod, params);
      }
    } else {
      // 设置屏幕方向为竖屏
      this.screenOrientation = ScreenOrientation.PORTRAIT;
      // 判断是否为页面容器且不是返回状态
      if (this.pageContainer && !this.isBack) {
        // 定义显示竖屏视图方法名
        const showPortraitMethod: string = 'showPortraitView()';
        // 调用代码预览JavaScript工具运行JS方法
        CodePreviewJSUtil.codeViewRunJS(showPortraitMethod);
      }
    }
    // 调用更改底部图标数据方法
    this.changeBottomIconData();
  };

  // 定义组件即将出现时的生命周期方法
  aboutToAppear?(): void {
    // 判断是否为页面容器模式
    if (this.pageContainer) {
      // 使用try-catch处理可能的异常
      try {
        // 获取最后一个窗口实例并处理结果
        window.getLastWindow(getContext()).then((windowClass: window.Window) => {
          // 检查窗口类是否为undefined
          if (windowClass === undefined) {
            // 记录错误日志
            Logger.error(TAG, `MainWindowClass is undefined`);
            // 直接返回
            return;
          }
          // 将窗口类赋值给实例变量
          this.windowClass = windowClass;
          // 注册窗口大小变化事件监听器
          this.windowClass.on('windowSizeChange', this.windowSizeCallback);
        });
      } catch (error) {
        // 将错误转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录注册窗口大小变化失败的错误日志
        Logger.error(TAG,
          `Failed to register windowSizeChange. Cause code: ${err.code}, message: ${err.message}`);
      }
    }
  }

  // 定义组件即将消失时的生命周期方法
  aboutToDisappear(): void {
    // 判断是否为页面容器模式
    if (this.pageContainer) {
      // 使用try-catch处理可能的异常
      try {
        // 取消注册窗口大小变化事件监听器
        this.windowClass?.off('windowSizeChange', this.windowSizeCallback);
      } catch (error) {
        // 将错误转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录取消注册窗口大小变化失败的错误日志
        Logger.error(TAG,
          `Failed to unregister windowSizeChange. Cause code: ${err.code}, message: ${err.message}`);
      }
    }
  }

  // 定义处理设备方向的方法
  handleDeviceOrientation() {
    // 使用try-catch处理可能的异常
    try {
      // 检查是否支持窗口会话管理器系统能力
      if (canIUse('SystemCapability.Window.SessionManager')) {
        // 注册窗口状态变化事件监听器
        this.windowClass!.on('windowStatusChange', (windowStatusType) => {
          // 判断窗口状态类型是否为浮动窗口
          if (windowStatusType === window.WindowStatusType.FLOATING) {
            // 设置为浮动窗口类型
            this.isFloatWindowType = true;
            // 首次直接设置为竖屏浮动模式
            this.screenOrientation = ScreenOrientation.PORTRAIT;
          } else {
            // 设置为非浮动窗口类型
            this.isFloatWindowType = false;
            // 如果是全屏模式，根据设备恢复状态
            // 将平板恢复为横屏模式
            if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM) {
              // 设置屏幕方向为竖屏
              this.screenOrientation = ScreenOrientation.PORTRAIT;
            }
          }
        });
      }
    } catch (error) {
      // 将错误转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录取消注册回调失败的错误日志
      Logger.error(TAG,
        `Failed to unregister callback. Cause code: ${err.code}, message: ${err.message}`);
    }
  }

  // 定义刷新代码显示的方法
  flushCode() {
    // 定义代码转HTML的方法名
    const codeToHtmlMethod: string = 'codeToHtml(%param)';
    // 将代码内容转换为JSON字符串参数
    const params: string = JSON.stringify(this.code);
    // 调用代码预览JavaScript工具运行JS方法
    CodePreviewJSUtil.codeViewRunJS(codeToHtmlMethod, params);
  }

  // 定义处理颜色模式变化的方法
  onHandleColorModeChange() {
    // 判断当前颜色模式是否与系统颜色模式不同
    if (this.colorMode !== this.systemColorMode) {
      // 调用切换颜色模式方法
      this.changeColorMode();
    }
  }

  // 定义切换颜色模式的方法
  changeColorMode() {
    // 判断当前颜色模式是否为浅色模式
    if (this.colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT) {
      // 设置颜色模式为深色模式
      this.colorMode = ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
      // 设置深色模式状态为true
      this.isDarkMode = true;
    } else {
      // 设置颜色模式为浅色模式
      this.colorMode = ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT;
      // 设置深色模式状态为false
      this.isDarkMode = false;
    }
    // 调用窗口工具更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this), this.isDarkMode);
    // 定义切换颜色模式的方法名
    const changeColorModeMethod: string = 'changeColorMode(%param)';
    // 将颜色模式转换为JSON字符串参数
    const params: string = JSON.stringify(this.colorMode);
    // 调用代码预览JavaScript工具运行JS方法
    CodePreviewJSUtil.codeViewRunJS(changeColorModeMethod, params);
  }

  // 定义私有切换屏幕方向的方法
  private changeScreenOrientation(): void {
    // 判断当前屏幕方向是否为竖屏
    if (this.screenOrientation === ScreenOrientation.PORTRAIT) {
      // 启用浮动窗口旋转功能
      WindowUtil.enableFloatWindowRotate(getContext());
      // 设置主窗口方向为横屏
      WindowUtil.setMainWindowOrientation(getContext(), window.Orientation.LANDSCAPE);
    } else {
      // 禁用浮动窗口旋转功能
      WindowUtil.disableFloatWindowRotate(getContext());
      // 设置主窗口方向为竖屏
      WindowUtil.setMainWindowOrientation(getContext(), window.Orientation.PORTRAIT);
    }
  }

  // 定义私有更改底部图标数据的方法
  private changeBottomIconData(): void {
    // 检查底部图标项数组第三个元素是否存在
    if (this.bottomIconItems[2]) {
      // 设置浅色模式下的图标数据
      this.bottomIconItems[2].lightMode = {
        iconResource: $r('sys.symbol.screen_rotation'),
        iconTitle: this.screenOrientation === ScreenOrientation.PORTRAIT ? $r('app.string.landscape_screen') :
        $r('app.string.portrait_screen'),
      };
      // 设置深色模式下的图标数据
      this.bottomIconItems[2].darkMode = {
        iconResource: $r('sys.symbol.screen_rotation'),
        iconTitle: this.screenOrientation === ScreenOrientation.PORTRAIT ? $r('app.string.landscape_screen') :
        $r('app.string.portrait_screen'),
      }
    }
  }

  // 定义私有处理分享的方法
  private handleShare(): void {
    // 获取UI能力上下文
    let context = getContext(this) as common.UIAbilityContext;
    // 从资源管理器获取分享描述字符串
    const shareDescription =
      context.resourceManager.getStringSync($r('app.string.share_description').id, this.componentName);
    // 创建系统分享数据对象
    const shareData: systemShare.SharedData = new systemShare.SharedData({
      utd: utd.UniformDataType.PLAIN_TEXT,
      content: this.code,
      title: this.componentName,
      description: shareDescription
    });

    // 创建系统分享控制器
    let controller: systemShare.ShareController = new systemShare.ShareController(shareData);
    // 显示分享界面并处理可能的错误
    controller.show(context, {
      selectionMode: systemShare.SelectionMode.SINGLE,
      previewMode: systemShare.SharePreviewMode.DEFAULT
    }).catch((error: BusinessError) => {
      // 记录组件代码分享错误日志
      Logger.error(TAG, `Component code sharing error. Code: ${error.code}, message: ${error.message}`);
    });
  }

  // 定义复制文本的方法
  copyText(text: string) {
    // 使用try-catch处理可能的异常
    try {
      // 创建纯文本类型的剪贴板数据
      const pasteboardData = pasteboard.createData(pasteboard.MIMETYPE_TEXT_PLAIN, text);
      // 获取系统剪贴板实例
      const systemPasteboard = pasteboard.getSystemPasteboard();
      // 设置数据到剪贴板并处理结果
      systemPasteboard.setData(pasteboardData).then(() => {
        // 使用try-catch处理显示提示的异常
        try {
          // 显示复制成功的提示
          promptAction.showToast({ message: $r('app.string.copy_success') });
        } catch (err) {
          // 将错误转换为业务错误类型
          const error: BusinessError = err as BusinessError;
          // 记录显示提示错误的日志
          Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
        }
      }).catch((error: BusinessError) => {
        // 显示复制失败的提示
        promptAction.showToast({ message: $r('app.string.copy_fail') });
        // 记录复制数据失败的错误日志
        Logger.error(TAG, `Copy data failed, the code is ${error.code}}, the message is ${error.message}`);
      })
    } catch (err) {
      // 将错误转换为业务错误类型
      const error: BusinessError = err as BusinessError;
      // 记录剪贴板调用错误的日志
      Logger.error(TAG, `Pasteboard invoke error, the code is ${error.code}}, the message is ${error.message}`);
    }
  }

  // 使用Builder装饰器定义顶部图标项构建器
  @Builder
  TopIconItem(resourceData: ResourceInterface | undefined, onClickIcon: (() => void) | undefined) {
    // 创建水平排列的行容器，设置间距
    Row({ space: CommonConstants.SPACE_4 }) {
      // 创建符号字形组件显示图标
      SymbolGlyph(resourceData?.iconResource)
        // 设置字体大小
        .fontSize($r('sys.float.Title_S'))
        // 设置字体颜色
        .fontColor([$r('sys.color.icon_primary')])
      // 判断图标标题是否存在
      if (resourceData?.iconTitle !== undefined) {
        // 创建文本组件显示图标标题
        Text(resourceData?.iconTitle)
          // 设置字体大小
          .fontSize($r('sys.float.Caption_L'))
          // 设置字体颜色
          .fontColor($r('sys.color.font_primary'))
          // 设置字体粗细
          .fontWeight(FontWeight.Medium)
      }
    }
    // 设置背景颜色
    .backgroundColor($r('sys.color.comp_background_tertiary'))
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level10'))
    // 设置内边距
    .padding($r('sys.float.padding_level5'))
    // 设置背景模糊样式
    .backgroundBlurStyle(BlurStyle.BACKGROUND_THIN)
    // 设置点击事件处理
    .onClick(() => {
      // 调用点击图标回调函数
      onClickIcon?.();
    })
  }

  // 使用Builder装饰器定义顶部菜单构建器
  @Builder
  TopMenu() {
    // 创建水平排列的行容器，设置间距
    Row({ space: CommonConstants.SPACE_8 }) {
      // 遍历顶部图标项数组
      ForEach(this.topIconItems, (item: ItemData) => {
        // 调用顶部图标项构建器，根据深色模式选择图标样式
        this.TopIconItem(this.isDarkMode ? item?.darkMode : item?.lightMode, item?.onClickIcon
        )
      }, (_item: ItemData, index: number) => index.toString())
    }
    // 设置宽度为100%
    .width('100%')
    // 设置内容对齐方式为右对齐
    .justifyContent(FlexAlign.End)
    // 设置内边距
    .padding({
      top: $r('sys.float.padding_level6'),
    })
  }

  // 使用Builder装饰器定义底部菜单构建器
  @Builder
  BottomMenu() {
    // 创建水平排列的行容器
    Row() {
      // 根据断点类型选择不同的图标项数组进行遍历
      ForEach(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? this.pcBottomIconItems :
      this.bottomIconItems, (item: ItemData) => {
        // 创建底部图标项组件
        BottomIconItem({
          isDark: this.isDarkMode,
          itemData: item,
        })
      }, (_item: ItemData, index: number) => index.toString())
    }
    // 设置Y轴平移变换
    .translate({ y: this.bottomTranslateY })
    // 设置宽度为100%
    .width('100%')
    // 设置高度为标签栏高度加导航指示器高度
    .height(CommonConstants.TAB_BAR_HEIGHT + this.globalInfoModel.naviIndicatorHeight)
    // 设置内容对齐方式为均匀分布
    .justifyContent(FlexAlign.SpaceAround)
    // 设置底部内边距
    .padding({ bottom: this.globalInfoModel.naviIndicatorHeight })
    // 设置背景模糊样式，根据深色模式选择颜色模式
    .backgroundBlurStyle(BlurStyle.COMPONENT_THICK,
      {
        colorMode: this.isDarkMode ? ThemeColorMode.DARK : ThemeColorMode.LIGHT
      })
  }

  // 使用Builder装饰器定义顶部导航菜单构建器
  @Builder
  TopNavigationMenu() {
    // 创建垂直排列的列容器
    Column() {
      // 创建水平排列的行容器
      Row() {
        // 创建圆形按钮
        Button({ type: ButtonType.Circle }) {
          // 创建符号字形组件显示返回图标
          SymbolGlyph($r('sys.symbol.chevron_backward'))
            // 根据深色模式设置字体颜色
            .fontColor(this.isDarkMode ? [$r('app.color.icon_primary_dark')] : [$r('app.color.icon_primary_light')])
            // 设置字体大小
            .fontSize($r('sys.float.Title_M'))
        }
        // 设置按钮高度
        .height($r('app.float.code_preview_top_navigation_height'))
        // 设置宽高比为1:1
        .aspectRatio(1)
        // 设置右边距
        .margin({ right: $r('sys.float.padding_level4') })
        // 设置背景颜色
        .backgroundColor(this.backIconBgColor)
        // 设置点击事件处理
        .onClick(() => this.onBackPage?.())
        // 设置悬停事件处理
        .onHover((isHover: boolean) => {
          // 根据悬停状态和颜色模式设置背景颜色
          this.backIconBgColor = !isHover ? Color.Transparent :
            this.colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT ?
            $r('sys.color.comp_background_tertiary') : $r('sys.color.icon_on_tertiary');
        })

        // 创建文本组件显示组件名称
        Text(this.componentName)
          // 设置字体大小
          .fontSize($r('sys.float.Title_S'))
          // 根据深色模式设置字体颜色
          .fontColor(this.isDarkMode ? $r('app.color.font_primary_dark') : $r('app.color.font_primary_light'))
          // 设置字体粗细
          .fontWeight(FontWeight.Bold)
          // 设置文本对齐方式
          .textAlign(TextAlign.Start)
          // 设置布局权重
          .layoutWeight(1)

        // 创建圆形分享按钮
        Button({ type: ButtonType.Circle }) {
          // 创建符号字形组件显示分享图标
          SymbolGlyph($r('sys.symbol.share'))
            // 根据深色模式设置字体颜色
            .fontColor(this.isDarkMode ? [$r('app.color.icon_primary_dark')] : [$r('app.color.icon_primary_light')])
            // 设置字体大小
            .fontSize($r('sys.float.Title_M'))
        }
        // 设置按钮高度
        .height($r('app.float.code_preview_top_navigation_height'))
        // 设置宽高比为1:1
        .aspectRatio(1)
        // 设置左边距
        .margin({ left: $r('sys.float.padding_level4') })
        // 设置背景颜色
        .backgroundColor(this.shareIconBgColor)
        // 设置点击事件处理
        .onClick(() => this.handleShare())
        // 设置悬停事件处理
        .onHover((isHover: boolean) => {
          // 根据悬停状态和颜色模式设置背景颜色
          this.shareIconBgColor = !isHover ? Color.Transparent :
            this.colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT ?
            $r('sys.color.comp_background_tertiary') : $r('sys.color.icon_on_tertiary');
        })

        // 判断当前断点是否为XL
        if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
          // 创建水平排列的行容器，设置间距
          Row({ space: CommonConstants.SPACE_8 }) {
            // 遍历PC端底部图标项数组
            ForEach(this.pcBottomIconItems, (item: ItemData, index: number) => {
              // 创建水平排列的行容器
              Row() {
                // 创建符号字形组件，根据颜色模式选择图标资源
                SymbolGlyph(this.colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT ?
                item.lightMode.iconResource : item.darkMode.iconResource)
                  // 设置字体大小
                  .fontSize($r('app.float.symbol_size_large'))
                  // 根据颜色模式设置字体颜色
                  .fontColor(this.colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT ?
                    [$r('sys.color.icon_primary')] : [$r('sys.color.icon_on_primary')])
                  // 根据焦点状态设置透明度
                  .opacity(this.isFocus ? 1 : 0.4)
              }
              // 设置内容对齐方式为居中
              .justifyContent(FlexAlign.Center)
              // 设置高度
              .height($r('app.float.code_preview_top_navigation_height'))
              // 设置宽高比为1:1
              .aspectRatio(1)
              // 根据当前索引和悬停状态设置背景颜色
              .backgroundColor(this.currentIndex === item.id && this.isHover ? $r('sys.color.ohos_id_color_hover') :
              // 设置透明背景色
              Color.Transparent)
              // 设置悬停事件处理
              .onHover((isHover: boolean) => {
                // 设置悬停状态
                this.isHover = isHover;
                // 设置当前索引
                this.currentIndex = index;
              })
              // 设置点击事件处理
              .onClick(() => {
                // 调用图标项的点击回调函数
                item.onClickIcon();
              })
            }, (index: number) => index.toString())
          }
          // 设置右边距
          .margin({ right: $r('app.float.code_preview_icon_margin') })
        }
      }
      // 设置透明度
      .opacity(this.navigationOpacity)
      // 设置垂直对齐方式为居中
      .alignItems(VerticalAlign.Center)
      // 设置水平对齐方式为两端对齐
      .justifyContent(FlexAlign.SpaceBetween)
      // 设置高度
      .height(CommonConstants.NAVIGATION_HEIGHT)
      // 设置内边距
      .padding({
        left: $r('sys.float.padding_level8'),
        right: $r('sys.float.padding_level8'),
      })
    }
    // 设置Y轴平移变换
    .translate({ y: this.topTranslateY })
    // 设置绝对定位
    .position({ x: 0, y: 0 })
    // 设置背景模糊样式，根据深色模式选择颜色模式
    .backgroundBlurStyle(BlurStyle.COMPONENT_THICK,
      {
        colorMode: this.isDarkMode ? ThemeColorMode.DARK : ThemeColorMode.LIGHT
      })
    // 设置顶部内边距
    .padding({
      top: this.globalInfoModel.statusBarHeight
    })
    // 设置宽度为100%
    .width('100%')
  }

  // 使用Styles装饰器定义正常状态样式
  @Styles
  normalStyles(): void {
    // 根据悬停状态设置背景颜色
    .backgroundColor(this.isHover ? $r('sys.color.interactive_hover') : Color.Transparent)
  }

  // 使用Styles装饰器定义按下状态样式
  @Styles
  pressedStyles(): void {
    // 设置按下状态背景颜色
    .backgroundColor($r('sys.color.interactive_pressed'))
  }

  // 使用Styles装饰器定义焦点状态样式
  @Styles
  focusStyles(): void {
    // 设置焦点状态边框颜色
    .borderColor($r('sys.color.comp_emphasize_tertiary'))
    // 设置焦点状态边框宽度
    .borderWidth($r('app.float.toolbar_focus_border_width'))
  }

  // 使用Builder装饰器定义工具栏构建器
  @Builder
  Toolbar() {
    // 创建分割线组件
    Divider()
      // 设置宽度为100%
      .width('100%')
      // 设置外边距
      .margin({
        top: $r('sys.float.padding_level6'),
        left: $r('sys.float.padding_level4'),
        right: $r('sys.float.padding_level4'),
        bottom: $r('sys.float.padding_level2'),
      })
    // 创建水平排列的行容器，设置间距
    Row({ space: CommonConstants.SPACE_8 }) {
      // 创建文本组件显示复制代码文本
      Text($r('app.string.copy_code'))
        // 设置字体大小
        .fontSize($r('sys.float.Subtitle_M'))
        // 设置字体粗细
        .fontWeight(FontWeight.Medium)
      // 创建符号字形组件显示复制图标
      SymbolGlyph($r('sys.symbol.plus_square_on_square'))
        // 设置字体大小
        .fontSize($r('sys.float.Title_S'))
        // 设置字体颜色
        .fontColor([$r('sys.color.icon_primary')])
    }
    // 设置宽度为100%
    .width('100%')
    // 设置内容对齐方式为两端对齐
    .justifyContent(FlexAlign.SpaceBetween)
    // 设置点击事件处理
    .onClick(() => this.copyText(this.code))
    // 设置内边距
    .padding({
      top: $r('sys.float.padding_level4'),
      bottom: $r('sys.float.padding_level4'),
      left: $r('sys.float.padding_level4'),
      right: $r('sys.float.padding_level4'),
    })
    // 设置状态样式
    .stateStyles({
      normal: this.normalStyles,
      pressed: this.pressedStyles,
      focused: this.focusStyles,
    })
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level6'))
  }

  // 使用Builder装饰器定义Web覆盖层构建器
  @Builder
  WebOverlay() {
    // 创建水平排列的行容器
    Row()
      // 设置宽度为100%
      .width('100%')
      // 设置高度
      .height($r('app.float.web_overlay_height'))
      // 设置线性渐变背景
      .linearGradient({
        colors: [[$r('app.color.web_overlay_color_start'), 0], [$r('sys.color.comp_background_primary'), 1]]
      })
      // 根据页面容器状态设置可见性
      .visibility(this.pageContainer ? Visibility.None : Visibility.Visible)
  }

  // 定义组件构建方法
  build() {
    // 创建垂直排列的列容器
    Column() {
      // 创建堆叠容器
      Stack() {
        // 创建节点容器组件
        NodeContainer(this.webNodeController)
          // 根据深色模式设置背景颜色
          .backgroundColor(this.isDarkMode ? $r('app.color.code_preview_bg_color') : Color.White)
          // 设置底部覆盖层
          .overlay(this.WebOverlay(), { align: Alignment.Bottom })
        // 判断是否为页面容器模式
        if (this.pageContainer === true) {
          // 调用顶部导航菜单构建器
          this.TopNavigationMenu()
          // 判断当前断点是否不为XL
          if (this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL) {
            // 调用底部菜单构建器
            this.BottomMenu()
          }
        } else {
          // 调用顶部菜单构建器
          this.TopMenu()
        }
      }
      // 设置布局权重
      .layoutWeight(1)
      // 根据页面容器状态设置内容对齐方式
      .alignContent(this.pageContainer ? Alignment.Bottom : Alignment.TopEnd)
      // 根据页面容器状态设置外边距
      .margin({
        left: this.pageContainer ? 0 : $r('sys.float.padding_level4'),
        right: this.pageContainer ? 0 : $r('sys.float.padding_level4'),
      })

      // 判断是否不为页面容器模式
      if (!this.pageContainer) {
        // 调用工具栏构建器
        this.Toolbar()
      }
    }
    // 设置组件出现时的回调
    .onAppear(() => {
      // 判断是否为页面容器模式
      if (this.pageContainer) {
        // 调用处理设备方向方法
        this.handleDeviceOrientation();
      }
    })
    // 设置组件消失时的回调
    .onDisAppear(() => {
      // 禁用浮动窗口旋转功能
      WindowUtil.disableFloatWindowRotate(getContext());
    })
    // 设置背景颜色
    .backgroundColor($r('sys.color.comp_background_primary'))
    // 根据页面容器状态设置内边距
    .padding(this.pageContainer === false ? {
      left: $r('sys.float.padding_level2'),
      right: $r('sys.float.padding_level2'),
      bottom: $r('sys.float.padding_level2'),
    } : $r('sys.float.padding_level0'))
    // 设置宽度为100%
    .width('100%')
    // 设置高度为100%
    .height('100%')
  }
}

// 使用Component装饰器定义底部图标项组件
@Component
struct BottomIconItem {
  // 使用Link装饰器定义深色模式状态链接
  @Link isDark: boolean;
  // 使用ObjectLink装饰器定义图标项数据对象链接
  @ObjectLink itemData: ItemData | undefined;

  // 定义组件构建方法
  build() {
    // 创建垂直排列的列容器
    Column() {
      // 创建符号字形组件，根据深色模式选择图标资源
      SymbolGlyph(this.isDark ? this.itemData?.darkMode.iconResource : this.itemData?.lightMode.iconResource)
        // 设置字体大小
        .fontSize($r('sys.float.Title_M'))
        // 根据深色模式设置字体颜色
        .fontColor(this.isDark ? [$r('app.color.icon_secondary_dark')] : [$r('app.color.icon_secondary_light')])
      // 创建文本组件，根据深色模式选择图标标题
      Text(this.isDark ? this.itemData?.darkMode.iconTitle : this.itemData?.lightMode.iconTitle)
        // 设置文本对齐方式为居中
        .textAlign(TextAlign.Center)
        // 设置字体大小
        .fontSize($r('sys.float.Caption_M'))
        // 设置字体粗细
        .fontWeight(FontWeight.Medium)
        // 根据深色模式设置字体颜色
        .fontColor(this.isDark ? $r('app.color.font_secondary_dark') : $r('app.color.font_secondary_light'))
        // 设置顶部外边距
        .margin({ top: $r('sys.float.padding_level2') })
    }
    // 设置点击事件处理
    .onClick(() => {
      // 调用图标项数据的点击回调函数
      this.itemData?.onClickIcon();
    })
  }
}

// 使用Observed装饰器定义资源接口类
@Observed
class ResourceInterface {
  // 定义公共图标资源属性
  public iconResource?: Resource;
  // 定义公共图标标题属性
  public iconTitle?: ResourceStr;
}

// 使用Observed装饰器定义图标项数据类
@Observed
class ItemData {
  // 定义公共ID属性
  public id: number;
  // 定义公共浅色模式属性
  public lightMode: ResourceInterface;
  // 定义公共深色模式属性
  public darkMode: ResourceInterface;
  // 定义公共点击图标回调函数属性
  public onClickIcon: () => void;

  // 定义构造函数
  constructor(lightMode: ResourceInterface, onClickIcon: () => void, index: number, darkMode?: ResourceInterface) {
    // 设置浅色模式属性
    this.lightMode = lightMode;
    // 设置深色模式属性，如果未提供则使用浅色模式
    this.darkMode = darkMode || lightMode;
    // 设置点击图标回调函数属性
    this.onClickIcon = onClickIcon;
    // 设置ID属性
    this.id = index;
  }
}
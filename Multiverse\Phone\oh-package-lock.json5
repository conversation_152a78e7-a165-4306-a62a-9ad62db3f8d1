{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/common@../../Common": "@ohos/common@../../Common", "@ohos/commonbusiness@../../Features/CommonBusiness": "@ohos/commonbusiness@../../Features/CommonBusiness", "@ohos/community@../../Features/Community": "@ohos/community@../../Features/Community", "@ohos/home@../../Features/Home": "@ohos/home@../../Features/Home", "@ohos/mine@../../Features/Mine": "@ohos/mine@../../Features/Mine", "@ohos/security@../../Features/Security": "@ohos/security@../../Features/Security"}, "packages": {"@ohos/common@../../Common": {"name": "@ohos/common", "version": "1.0.0", "resolved": "../../Common", "registryType": "local"}, "@ohos/commonbusiness@../../Features/CommonBusiness": {"name": "@ohos/commonbusiness", "version": "1.0.0", "resolved": "../../Features/CommonBusiness", "registryType": "local", "dependencies": {"@ohos/common": "file:../../Common"}}, "@ohos/community@../../Features/Community": {"name": "@ohos/community", "version": "1.0.0", "resolved": "../../Features/Community", "registryType": "local", "dependencies": {"@ohos/common": "file:../../Common", "@ohos/commonbusiness": "file:../CommonBusiness"}}, "@ohos/home@../../Features/Home": {"name": "@ohos/home", "version": "1.0.0", "resolved": "../../Features/Home", "registryType": "local", "dependencies": {"@ohos/common": "file:../../Common", "@ohos/commonbusiness": "file:../CommonBusiness"}}, "@ohos/mine@../../Features/Mine": {"name": "@ohos/mine", "version": "1.0.0", "resolved": "../../Features/Mine", "registryType": "local", "dependencies": {"@ohos/common": "file:../../Common"}}, "@ohos/security@../../Features/Security": {"name": "@ohos/security", "version": "1.0.0", "resolved": "../../Features/Security", "registryType": "local", "dependencies": {"@ohos/common": "file:../../Common", "@ohos/commonbusiness": "file:../CommonBusiness"}}}}
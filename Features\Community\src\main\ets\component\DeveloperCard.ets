// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举、通用常量和产品系列枚举
import { BreakpointType, BreakpointTypeEnum, CommonConstants, ProductSeriesEnum } from '@ohos/common';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';
// 导入开发者项目组件
import { DeveloperItem } from './DeveloperItem';

// 定义开发者项目比例常量
const DEVELOPER_ITEM_RATIO = 960 / 1312;
// 定义Verde产品系列开发者项目比例常量
const DEVELOPER_ITEM_RATIO_VERDE = 240 / 408;

// 使用Component装饰器定义开发者卡片组件
@Component
export struct DeveloperCard {
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用Prop和Require装饰器定义发现内容数组属性
  @Prop @Require discoverContents: DiscoverContent[];
  // 定义处理项目点击函数
  handleItemClick?: Function;

  // 定义构建方法
  build() {
    // 创建滑块组件
    Swiper() {
      // 创建重复组件
      Repeat(this.discoverContents)
        // 定义每个项目的渲染
        .each((repeatItem: RepeatItem<DiscoverContent>) => {
          // 创建列布局
          Column() {
            // 创建开发者项目组件
            DeveloperItem({ discoverContent: repeatItem.item })
              // 设置点击事件
              .onClick(() => {
                // 调用处理项目点击函数
                this.handleItemClick?.(repeatItem.item);
              })
          }
          // 设置内边距
          .padding({
            left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
            $r('sys.float.padding_level8') : 0,
            right: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
            $r('sys.float.padding_level8') : 0,
          })
        })
    }
    // 设置尺寸
    .size(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
      {
        height: ((this.globalInfoModel.deviceWidth - CommonConstants.SPACE_32) *
          (deviceInfo.productSeries === ProductSeriesEnum.VDE ? DEVELOPER_ITEM_RATIO_VERDE : DEVELOPER_ITEM_RATIO) +
        CommonConstants.SPACE_16) * this.discoverContents.length
      } :
      { height: $r('app.float.list_card_height') })
    // 设置边缘效果为无
    .effectMode(EdgeEffect.None)
    // 禁用循环
    .loop(false)
    // 隐藏指示器
    .indicator(false)
    // 根据断点类型禁用滑动
    .disableSwipe(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM)
    // 根据断点类型设置垂直方向
    .vertical(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM)
    // 设置前边距
    .prevMargin(new BreakpointType<Length>({
      sm: 0,
      md: $r('sys.float.padding_level6'),
      lg: CommonConstants.SPACE_16 + CommonConstants.TAB_BAR_WIDTH,
      xl: CommonConstants.SPACE_16,
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 设置后边距
    .nextMargin(new BreakpointType<Length>({
      sm: 0,
      md: $r('sys.float.padding_level6'),
      lg: $r('sys.float.padding_level8'),
    }).getValue(this.globalInfoModel.currentBreakpoint))
    // 设置显示数量
    .displayCount(
      new BreakpointType({
        sm: this.discoverContents.length,
        md: CommonConstants.LANE_MD,
        lg: CommonConstants.LANE_LG,
      }).getValue(this.globalInfoModel.currentBreakpoint)
    )
    // 设置项目间距
    .itemSpace(new BreakpointType({
      sm: CommonConstants.SPACE_16,
      md: CommonConstants.SPACE_12,
      lg: CommonConstants.SPACE_16,
    }).getValue(this.globalInfoModel.currentBreakpoint))
  }
}
// 导入ArkUI包中的显示和窗口模块
import { display, window } from '@kit.ArkUI';
// 导入基础服务包中的业务错误和设备信息
import { BusinessError, deviceInfo } from '@kit.BasicServicesKit';
// 导入产品系列枚举
import { ProductSeriesEnum } from '../constant/CommonEnums';
// 导入断点类型枚举和全局信息模型
import { BreakpointTypeEnum, GlobalInfoModel } from '../model/GlobalInfoModel';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG: string = '[BreakpointSystem]';

/**
 * 断点类型接口
 * 定义不同断点下的值类型结构
 * @template T 值的类型泛型
 */
export interface BreakpointTypes<T> {
  // 超小屏幕断点值，可选
  xs?: T;
  // 小屏幕断点值，必需
  sm: T;
  // 中等屏幕断点值，必需
  md: T;
  // 大屏幕断点值，必需
  lg: T;
  // 超大屏幕断点值，可选
  xl?: T;
}

/**
 * 断点类型类
 * 用于管理不同断点下的值
 * 提供根据当前断点获取对应值的功能
 * @template T 值的类型泛型
 */
export class BreakpointType<T> {
  // 超小屏幕断点值
  private xs: T;
  // 小屏幕断点值
  private sm: T;
  // 中等屏幕断点值
  private md: T;
  // 大屏幕断点值
  private lg: T;
  // 超大屏幕断点值
  private xl: T;

  /**
   * 构造函数
   * 初始化各断点的值，未提供的断点使用相邻断点的值
   * @param param 断点类型参数对象
   */
  public constructor(param: BreakpointTypes<T>) {
    // 超小屏幕值，如果未提供则使用小屏幕值
    this.xs = param.xs ?? param.sm;
    // 小屏幕值
    this.sm = param.sm;
    // 中等屏幕值
    this.md = param.md;
    // 大屏幕值
    this.lg = param.lg;
    // 超大屏幕值，如果未提供则使用大屏幕值
    this.xl = param.xl ?? param.lg;
  }

  /**
   * 根据当前断点获取对应值的方法
   * @param currentBreakpoint 当前断点类型字符串
   * @returns T 对应断点的值
   */
  public getValue(currentBreakpoint: string): T {
    // 如果是超小屏幕断点
    if (currentBreakpoint === BreakpointTypeEnum.XS) {
      return this.xs;
    }
    // 如果是小屏幕断点
    if (currentBreakpoint === BreakpointTypeEnum.SM) {
      return this.sm;
    }
    // 如果是中等屏幕断点
    if (currentBreakpoint === BreakpointTypeEnum.MD) {
      return this.md;
    }
    // 如果是超大屏幕断点
    if (currentBreakpoint === BreakpointTypeEnum.XL) {
      return this.xl;
    }
    // 默认返回大屏幕断点值
    return this.lg;
  }
}

/**
 * 断点系统类
 * 管理应用的响应式断点系统
 * 根据窗口尺寸和设备类型自动更新当前断点
 * 使用单例模式确保全局唯一实例
 */
export class BreakpointSystem {
  // 静态单例实例
  private static instance: BreakpointSystem;
  // 当前断点类型，默认为中等屏幕
  private currentBreakpoint: BreakpointTypeEnum = BreakpointTypeEnum.MD;

  /**
   * 私有构造函数
   * 防止外部直接实例化，确保单例模式
   */
  private constructor() {
  }

  /**
   * 获取单例实例方法
   * 如果实例不存在则创建新实例
   * @returns BreakpointSystem 断点系统实例
   */
  public static getInstance(): BreakpointSystem {
    // 如果实例不存在
    if (!BreakpointSystem.instance) {
      // 创建新的实例
      BreakpointSystem.instance = new BreakpointSystem();
    }
    // 返回单例实例
    return BreakpointSystem.instance;
  }

  /**
   * 更新当前断点方法
   * 当断点发生变化时更新全局信息模型
   * @param breakpoint 新的断点类型
   */
  public updateCurrentBreakpoint(breakpoint: BreakpointTypeEnum): void {
    // 如果当前断点与新断点不同
    if (this.currentBreakpoint !== breakpoint) {
      // 更新当前断点
      this.currentBreakpoint = breakpoint;
      // 获取或创建全局信息模型
      const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();
      // 更新全局信息模型中的当前断点
      globalInfoModel.currentBreakpoint = this.currentBreakpoint;
      // 将更新后的模型存储到AppStorage
      AppStorage.setOrCreate('GlobalInfoModel', globalInfoModel);
    }
  }

  /**
   * 窗口尺寸变化处理方法
   * 当窗口尺寸发生变化时调用此方法更新断点
   * @param window 窗口对象
   */
  public onWindowSizeChange(window: window.Window): void {
    // 调用更新宽度断点方法
    this.updateWidthBp(window);
  }

  /**
   * 根据窗口宽度更新断点方法
   * 根据窗口宽度和设备类型计算并更新当前断点
   * @param window 窗口对象
   */
  public updateWidthBp(window: window.Window): void {
    try {
      // 获取窗口属性
      const mainWindow: window.WindowProperties = window.getWindowProperties();
      // 获取窗口宽度（像素）
      const windowWidth: number = mainWindow.windowRect.width;
      // 将像素转换为vp单位
      const windowWidthVp = px2vp(windowWidth);
      // 获取设备类型
      const deviceType = deviceInfo.productSeries;
      // 如果是可折叠PC设备
      if (deviceType === ProductSeriesEnum.HPR) {
        // 直接设置为超大屏幕断点
        this.updateCurrentBreakpoint(BreakpointTypeEnum.XL);
        return;
      }
      // 默认断点为中等屏幕
      let widthBp: BreakpointTypeEnum = BreakpointTypeEnum.MD;
      // 根据窗口宽度判断断点类型
      if (windowWidthVp < 320) {
        // 小于320vp为超小屏幕
        widthBp = BreakpointTypeEnum.XS;
      } else if (windowWidthVp >= 320 && windowWidthVp < 600) {
        // 320-600vp为小屏幕
        widthBp = BreakpointTypeEnum.SM;
      } else if (windowWidthVp >= 600 && windowWidthVp < 840) {
        // 600-840vp为中等屏幕
        widthBp = BreakpointTypeEnum.MD;
      } else if (windowWidthVp >= 840 && windowWidthVp < 1440) {
        // 840-1440vp为大屏幕
        widthBp = BreakpointTypeEnum.LG;
      } else {
        // 大于等于1440vp为超大屏幕
        widthBp = BreakpointTypeEnum.XL;
      }
      // 更新当前断点
      this.updateCurrentBreakpoint(widthBp);
    } catch (error) {
      // 捕获异常并转换为业务错误类型
      const err: BusinessError = error as BusinessError;
      // 记录获取窗口属性失败的错误日志
      Logger.error(TAG, `Failed to getWindowProperties. Cause: ${err.code}, ${err.message}`);
    }
  }
}
// 导入字符串工具类，用于字符串处理
import { StringUtil } from '../../../util/StringUtil';
// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入图片相关的属性映射数据
import { filterStyleMapData, imageFitStyleMapData } from '../entity/ImageAttributeMapping';

/**
 * 图片描述器类
 * 继承自通用描述器，用于描述图片组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class ImageDescriptor extends CommonDescriptor {
  // 私有颜色滤镜矩阵字符串，默认使用默认值
  private colorFilterMatrixStr: string = filterStyleMapData.get('Default')!.value;
  // 对象适配属性，默认使用默认值
  public objectFit: ImageFit = imageFitStyleMapData.get('Default')!.value;
  // 颜色滤镜矩阵数组，将字符串转换为数组
  public colorFilterMatrix: number[] = StringUtil.stringToArray(this.colorFilterMatrixStr);
  // 裁剪属性，默认为false
  public clip: boolean = false;

  /**
   * 转换属性方法
   * 将原始属性转换为描述器属性
   * @param attributes 原始属性数组，包含需要转换的属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性进行转换
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'objectFit':
          this.objectFit =
            imageFitStyleMapData.get(attribute.currentValue)?.value ?? imageFitStyleMapData.get('Default')!.value;
          break;
        case 'colorFilterMatrixStr':
          this.colorFilterMatrixStr =
            filterStyleMapData.get(attribute.currentValue)?.value ?? filterStyleMapData.get('Default')!.value;
          this.colorFilterMatrix = StringUtil.stringToArray(this.colorFilterMatrixStr);
          break;
        case 'clip':
          this.clip = JSON.parse(attribute.currentValue);
          break;
        default:
          break;
      }
    });
  }
}
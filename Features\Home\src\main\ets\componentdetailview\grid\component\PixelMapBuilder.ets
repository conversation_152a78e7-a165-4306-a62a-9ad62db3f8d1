// 导入项目数据类型，用于获取拖拽项目的信息
import type { ItemData } from '../entity/GridAttributeMapping';

/**
 * 像素图构建器函数
 * 用于创建Grid拖拽时显示的拖拽图像，提供视觉反馈
 * @param $$ 项目数据对象，包含拖拽项目的文本、宽度和高度信息
 */
@Builder
export function pixelMapBuilder($$: ItemData) {
  // 创建文本组件作为拖拽图像
  Text($$.text)
    // 设置字体大小
    .fontSize($r('sys.float.Body_L'))
    // 设置组件尺寸，使用传入的宽度和高度
    .size({ width: $$.width, height: $$.height })
    // 设置字体颜色
    .fontColor($r('sys.color.icon_emphasize'))
    // 设置文本居中对齐
    .textAlign(TextAlign.Center)
    // 设置背景颜色
    .backgroundColor($r('sys.color.comp_background_primary'))
    // 添加阴影效果，增强拖拽时的视觉层次
    .shadow(ShadowStyle.OUTER_DEFAULT_SM)
    // 设置圆角
    .borderRadius($r('sys.float.corner_radius_level4'))
    // 设置边框样式
    .border({ width: $r('app.float.border_width_large'), color: $r('sys.color.comp_background_emphasize') })
}
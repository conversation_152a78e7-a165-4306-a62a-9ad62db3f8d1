/**
 * 自定义对话框样式枚举
 * 定义自定义对话框的两种样式类型
 */
export enum CustomDialogStyle {
  // 图文弹窗样式，包含图片和文本内容
  StyleImage = '图文弹窗',
  // 进度弹窗样式，包含进度条和进度信息
  StyleProgress = '进度弹窗',
}

// 导出对话框样式映射数据，用于将字符串键映射到对话框样式枚举
export const dialogStyleMapData: Map<string, CustomDialogStyle> = new Map([
  // 默认样式，使用图文弹窗
  ['Default', CustomDialogStyle.StyleImage],
  // 图文弹窗样式映射
  ['图文弹窗', CustomDialogStyle.StyleImage],
  // 进度弹窗样式映射
  ['进度弹窗', CustomDialogStyle.StyleProgress],
]);

// 导出对话框资源映射数据，用于将对话框样式映射到对应的资源字符串
export const dialogResourceMapData: Map<CustomDialogStyle, ResourceStr> = new Map([
  // 图文弹窗对应的资源字符串
  [CustomDialogStyle.StyleImage, $r('app.string.custom_graphic_dialog')],
  // 进度弹窗对应的资源字符串
  [CustomDialogStyle.StyleProgress, $r('app.string.custom_progress_dialog')],
]);

// 导出对话框导入代码映射数据，用于将对话框样式映射到对应的导入语句
export const dialogImportCodeMapData: Map<CustomDialogStyle, string> = new Map([
  // 图文弹窗需要导入TipsDialog组件
  [CustomDialogStyle.StyleImage, `import { TipsDialog } from '@kit.ArkUI';`],
  // 进度弹窗需要导入CustomContentDialog组件
  [CustomDialogStyle.StyleProgress, `import { CustomContentDialog } from '@kit.ArkUI';`],
]);

// 导出图文弹窗构建器代码模板，用于生成图文弹窗的完整代码
export const styleGraphicBuilderCode = `  @Builder
  tipDialogBuilder() {
    TipsDialog({
      // 替换自己项目src/main/resources/base/media下的资源图片
      imageRes: $r('app.media.image_dialog'),
      imageSize: {
        width: 'auto',
        height: 180
      },
      title: '带图形确认框',
      content: '必要时可以通过图形化方式展现确认框，以使用户更好理解或认同确认内容',
      isChecked: this.isChecked,
      checkTips: '我已知晓上述内容，不再提醒',
      onCheckedChange: () => {
        this.isChecked = !this.isChecked;
      },
      primaryButton: {
        value: '取消',
        action: () => {
          this.onCancel();
          this.dialogController?.close();
          console.info('Callback when the first button is clicked');
        },
      },
      secondaryButton: {
        value: '确认',
        action: () => {
          this.onAccept();
          this.dialogController?.close();
          console.info('Callback when the second button is clicked');
        }
      }
    })
  }`;

// 导出进度弹窗构建器代码模板，用于生成进度弹窗的完整代码
export const styleProgressBuilderCode: string = `  @Builder
  progressDialogBuilder() {
    CustomContentDialog({
      contentBuilder: () => {
        this.buildContent();
      },
      buttons: [{
        value: '取消',
        action: () => {
          this.onCancel();
          this.dialogController?.close();
        }
      }, {
        value: '确认',
        action: () => {
          this.onAccept();
          this.dialogController?.close();
        }
      }]
    })
  }

  @Builder
  buildContent(): void {
    Column() {
      Row() {
        Text('标题')
          .fontColor($r('sys.color.font_primary'))
          .fontSize($r('sys.float.Subtitle_M'))
          .textAlign(TextAlign.Start)
        Blank()
        Text('20%')
          .fontColor($r('sys.color.font_secondary'))
          .fontSize($r('sys.float.Body_M'))
          .width(32)
          .textAlign(TextAlign.Center)
      }
      .width('100%')
      .height(20)
      .alignItems(VerticalAlign.Center)

      Progress({ value: 20 })
        .height(24)
        .margin({ top: $r('sys.float.padding_level4'), bottom: $r('sys.float.padding_level2') })
    }
  }`;

// 导出对话框构建器代码映射数据，用于将对话框样式映射到对应的代码模板
export const dialogBuilderCodeMapData: Map<CustomDialogStyle, string> = new Map([
  // 图文弹窗样式对应的代码模板
  [CustomDialogStyle.StyleImage, styleGraphicBuilderCode],
  // 进度弹窗样式对应的代码模板
  [CustomDialogStyle.StyleProgress, styleProgressBuilderCode],
]);
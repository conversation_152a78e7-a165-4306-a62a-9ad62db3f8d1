// 导入通用属性修改器基类，用于继承属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入List描述器类型，用于获取List的属性配置
import type { ListDescriptor } from './ListDescriptor';

/**
 * List属性修改器类
 * 继承通用属性修改器，专门用于动态修改List组件的属性
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class ListAttributeModifier extends CommonAttributeModifier<ListDescriptor, ListAttribute> {
  /**
   * 应用普通属性方法
   * 将List描述器中的属性值应用到List组件实例上
   * @param instance List属性实例，用于设置List组件的各种属性
   */
  public applyNormalAttribute(instance: ListAttribute): void {
    // 设置List的滚动方向属性
    this.assignAttribute((descriptor => descriptor.listDirection), (val) => instance.listDirection(val));
    // 设置List的多列布局属性，包括列数和列间距
    this.assignAttribute((descriptor => descriptor.lanes), (val) => instance.lanes(val?.value, val?.gutter));
    // 设置List的边缘滚动效果属性
    this.assignAttribute((descriptor => descriptor.edgeEffect), (val) => instance.edgeEffect(val));
  }
}
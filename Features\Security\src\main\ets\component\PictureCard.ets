// 导入通用模块中的通用常量和图片工具
import { CommonConstants, ImageUtil } from '@ohos/common';
// 导入示例数据模型类型
import type { SampleCardData, SampleContent } from '../model/SampleData';
// 导入标签标签组件
import { TagLabel } from './TagLabel';

// 使用Component装饰器定义图片卡片组件
@Component
export struct PictureCard {
  // 使用Prop装饰器定义示例卡片数据属性
  @Prop sampleCardData: SampleCardData;
  // 使用State装饰器定义背景顶部颜色状态
  @State bgTopColor: string = '';
  // 使用State装饰器定义背景底部颜色状态
  @State bgBottomColor: string = '';
  // 使用State装饰器定义示例内容状态
  @State sampleContent?: SampleContent = undefined;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 获取示例卡片数据的第一个内容
    this.sampleContent = this.sampleCardData.sampleContents[0];
    // 从图片URL获取颜色并设置背景渐变色
    ImageUtil.getColorFromImgUrl(this.sampleContent.mediaUrl, true).then((colorArr: number[]) => {
      // 设置背景顶部颜色为透明
      this.bgTopColor = `rgba(${colorArr[0]},${colorArr[1]},${colorArr[2]},0)`;
      // 设置背景底部颜色为不透明
      this.bgBottomColor = `rgba(${colorArr[0]},${colorArr[1]},${colorArr[2]},1)`;
    });
  }

  // 定义构建方法
  build() {
    // 创建主行布局
    Row() {
      // 创建堆叠布局，内容底部对齐
      Stack({ alignContent: Alignment.Bottom }) {
        // 显示卡片背景图片
        Image($rawfile(this.sampleCardData.cardImage))
          .draggable(false)
          .linearGradientBlur(60,
            {
              fractionStops: [[0, 0], [0, 0.64], [1, 0.82], [1, 1]],
              direction: GradientDirection.Bottom,
            })
          .alt($r('app.media.img_placeholder'))
          .height('100%')
          .width('100%')
        // 创建内容行布局
        Row() {
          // 创建文本列布局
          Column() {
            // 显示示例内容副标题
            Text(this.sampleContent?.subTitle)
              .fontSize($r('sys.float.Body_S'))
              .fontColor($r('sys.color.font_on_secondary'))
              .fontWeight(FontWeight.Medium)
              .lightUpEffect(1)
              .margin({ bottom: $r('sys.float.padding_level4') })
            // 显示示例内容标题
            Text(this.sampleContent?.title)
              .fontSize($r('sys.float.Title_M'))
              .fontColor($r('sys.color.font_on_primary'))
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: $r('sys.float.padding_level6') })
            // 显示标签标签组件（深色模式）
            TagLabel({ tags: this.sampleContent?.tags || [], isDark: true })
          }
          // 设置文本列左对齐
          .alignItems(HorizontalAlign.Start)
          // 设置文本列权重为1
          .layoutWeight(1)

          // 创建圆形按钮
          Button({ type: ButtonType.Circle }) {
            // 显示右箭头符号
            SymbolGlyph($r('sys.symbol.chevron_right'))
              .fontColor([$r('sys.color.icon_on_primary')])
              .fontSize($r('sys.float.Title_M'))
          }
          // 设置按钮背景颜色
          .backgroundColor($r('sys.color.comp_background_tertiary'))
          // 设置按钮宽度
          .width($r('app.float.card_button_height'))
          // 设置按钮宽高比为1:1
          .aspectRatio(1)
        }
        // 设置内容行线性渐变背景
        .linearGradient({
          angle: CommonConstants.LINEAR_GRADIENT_ANGLE,
          colors: [[this.bgTopColor, 0], [this.bgBottomColor, 0.75], [this.bgBottomColor, 1]]
        })
        // 设置内容行高度
        .height($r('app.float.card_content_height'))
        // 设置内容行内边距
        .padding($r('sys.float.padding_level8'))
        // 设置内容行宽度为100%
        .width('100%')
        // 设置内容行垂直底部对齐
        .alignItems(VerticalAlign.Bottom)
      }
      // 设置堆叠布局宽度为100%
      .width('100%')
      // 设置堆叠布局高度
      .height($r('app.float.picture_card_height'))
      // 设置堆叠布局边框圆角
      .borderRadius($r('sys.float.corner_radius_level8'))
      // 设置堆叠布局裁剪
      .clip(true)
    }
    // 设置主行点击效果
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置主行裁剪
    .clip(true)
    // 设置主行背景颜色
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    // 设置主行边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
  }
}
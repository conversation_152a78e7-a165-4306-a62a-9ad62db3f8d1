// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入滑动器描述器类型，用于描述滑动器组件配置
import type { SwiperDescriptor } from './SwiperDescriptor';

/**
 * 滑动器属性修改器类
 * 继承自通用属性修改器，用于修改滑动器组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class SwiperAttributeModifier extends CommonAttributeModifier<SwiperDescriptor, SwiperAttribute> {
  /**
   * 应用普通属性到滑动器组件实例
   * 将描述器中的属性值应用到实际的滑动器组件上
   * @param instance 滑动器属性实例，用于设置滑动器组件属性
   */
  applyNormalAttribute(instance: SwiperAttribute): void {
    // 分配指示器属性，将描述器中的indicator值应用到组件实例
    this.assignAttribute((descriptor => descriptor.indicator), (val) => instance.indicator(val));
    // 分配垂直属性，将描述器中的vertical值应用到组件实例
    this.assignAttribute((descriptor => descriptor.vertical), (val) => instance.vertical(val));
    // 分配效果模式属性，将描述器中的effectMode值应用到组件实例
    this.assignAttribute((descriptor => descriptor.effectMode), (val) => instance.effectMode(val));
    // 分配显示箭头属性，将描述器中的displayArrow值应用到组件实例
    this.assignAttribute((descriptor => descriptor.displayArrow), (val) => instance.displayArrow(val));
    // 分配循环属性，将描述器中的loop值应用到组件实例的autoPlay属性
    this.assignAttribute((descriptor => descriptor.loop), (val) => instance.autoPlay(val));
  }
}
// 导入字符串工具类，用于生成模板字符串
import { StringUtil } from '../../../util/StringUtil';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入Grid属性映射数据
import {
  columnsGapMapData,
  columnsTemplateMapData,
  operationModeMapData,
  rowsGapMapData,
  rowsTemplateMapData,
  scrollerMapData,
} from '../entity/GridAttributeMapping';

/**
 * Grid代码生成器类
 * 实现通用代码生成器接口，用于生成Grid布局相关的代码
 */
export class GridCodeGenerator implements CommonCodeGenerator {
  // 滚动器代码字符串，默认使用新建的滚动器
  private scroller: string = scrollerMapData.get('default')!.code;
  // 列间距代码字符串，默认为10
  private columnsGap: string = columnsGapMapData.get('default')!.code;
  // 行间距代码字符串，默认为10
  private rowsGap: string = rowsGapMapData.get('default')!.code;
  // 列模板代码字符串，默认为三列等宽布局
  private columnsTemplate: string = columnsTemplateMapData.get('default')!.code;
  // 行模板代码字符串，默认为三行等高布局
  private rowsTemplate: string = rowsTemplateMapData.get('default')!.code;
  // 操作模式代码字符串，默认为true（支持编辑）
  private operationMode: string = operationModeMapData.get('default')!.code;

  /**
   * 生成Grid布局代码方法
   * 根据传入的属性数组生成完整的Grid组件代码字符串
   * @param attributes 原始属性数组，包含所有Grid布局的配置信息
   * @returns 生成的Grid组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理列间距属性
        case 'columnsGap':
          // 直接使用当前值作为列间距
          this.columnsGap = attribute.currentValue;
          break;
        // 处理行间距属性
        case 'rowsGap':
          // 直接使用当前值作为行间距
          this.rowsGap = attribute.currentValue;
          break;
        // 处理列数量属性
        case 'columnsNum':
          // 根据列数量生成对应的模板字符串
          this.columnsTemplate = StringUtil.getTemplateString(Number(attribute.currentValue));
          break;
        // 处理行数量属性
        case 'rowsNum':
          // 根据行数量生成对应的模板字符串
          this.rowsTemplate = StringUtil.getTemplateString(Number(attribute.currentValue));
          break;
        // 处理操作模式属性
        case 'operationMode':
          // 解析JSON字符串为布尔值
          this.operationMode = JSON.parse(attribute.currentValue);
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });

    // 返回生成的完整Grid组件代码字符串
    return `
// 项目数据类，用于存储Grid项目的显示信息
@Observed
export class ItemData {
  public text: ResourceStr;
  public width: Resource | number;
  public height: Resource | number;

  constructor(text: ResourceStr = '', width: Resource | number = 0, height: Resource | number = 0) {
    this.text = text;
    this.width = width;
    this.height = height;
  }
}

// 像素图构建器，用于创建拖拽时的视觉反馈
@Builder
export function pixelMapBuilder($$: ItemData) {
  Text($$.text)
    .fontSize($r('sys.float.Body_L'))
    .size({ width: $$.width, height: $$.height })
    .fontColor($r('sys.color.icon_emphasize'))
    .textAlign(TextAlign.Center)
    .backgroundColor($r('sys.color.comp_background_primary'))
    .shadow(ShadowStyle.OUTER_DEFAULT_SM)
    .borderRadius($r('sys.float.corner_radius_level4'))
    .border({ width: 1.5, color: $r('sys.color.comp_background_emphasize') })
}

// Grid组件，实现可拖拽的网格布局
@Component
struct GridComponent {
  @State listData: string[] = [];
  @State itemData: ItemData = new ItemData();

  // 组件即将出现时初始化数据
  aboutToAppear(): void {
    for (let i = 1; i <= 30; i++) {
      this.listData.push('item' + i);
    }
  }

  // 构建组件UI
  build() {
    Column() {
      // 创建Grid组件，使用配置的滚动器
      Grid(${this.scroller}) {
        // 遍历数据创建Grid项目
        ForEach(this.listData, (item: string) => {
          GridItem() {
            Text(item)
              .fontSize($r('sys.float.Body_L'))
              .fontColor($r('sys.color.icon_emphasize'))
              .textAlign(TextAlign.Center)
              .margin({ right: $r('sys.float.padding_level2') })
          }
          .backgroundColor($r('sys.color.comp_background_primary'))
          // 监听区域变化，更新拖拽项目尺寸
          .onAreaChange((oldValue: Area, newValue: Area) => {
            this.itemData.width = (newValue.width as number) * 1.1;
            this.itemData.height = (newValue.height as number) * 1.1;
          })
          .borderRadius($r('sys.float.corner_radius_level4'))
          .border({ width: 1.5, color: $r('sys.color.comp_background_emphasize') })
        }, (item: string) => item)
      }
      // 设置编辑模式
      .editMode(${this.operationMode})
      // 拖拽开始事件处理
      .onItemDragStart((_event: ItemDragInfo, itemIndex: number) => {
        this.itemData.text = this.listData[itemIndex];
        return pixelMapBuilder(this.itemData);
      })
      // 拖拽放置事件处理
      .onItemDrop((_event: ItemDragInfo, itemIndex: number, insertIndex: number, isSuccess: boolean): void => {
        if (!isSuccess || insertIndex >= this.listData.length) {
          return;
        }
        const temp: string = this.listData[itemIndex];
        this.listData[itemIndex] = this.listData[insertIndex];
        this.listData[insertIndex] = temp;
      })
      // 设置列间距
      .columnsGap(${this.columnsGap})
      // 设置行间距
      .rowsGap(${this.rowsGap})
      // 设置行模板
      .rowsTemplate('${this.rowsTemplate}')
      // 设置列模板
      .columnsTemplate('${this.columnsTemplate}')
    }
    .height('100%')
    .width('100%')
    .padding($r('sys.float.padding_level3'))
    .align(Alignment.Center)
  }
}`;
  }
}
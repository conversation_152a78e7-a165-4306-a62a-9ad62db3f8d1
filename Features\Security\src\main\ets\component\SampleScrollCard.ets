// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入示例数据模型
import { SampleCardData, SampleContent } from '../model/SampleData';
// 导入标签标签组件
import { TagLabel } from './TagLabel';

// 定义项目高度常量
const ITEM_HEIGHT: number = 340;
// 定义项目宽高比常量
const ITEM_ASPECT: number = 0.74;
// 定义日志最大宽度常量
const LOG_MAX_WIDTH: number = 202;

// 使用Component装饰器定义示例滚动卡片组件
@Component
export struct SampleScrollCard {
  // 使用StorageProp和Watch装饰器获取全局信息模型并监听断点变化
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用Prop装饰器定义示例卡片数据属性
  @Prop sampleCardData: SampleCardData;
  // 定义处理项目点击的可选函数
  handleItemClick?: Function;
  // 使用State装饰器定义示例项目宽度状态
  @State sampleItemWidth: number = ITEM_HEIGHT * ITEM_ASPECT;
  // 使用State装饰器定义水平内边距状态
  @State horizontalPadding: Resource = new BreakpointType({
    sm: $r('sys.float.padding_level8'),
    md: $r('sys.float.padding_level12'),
    lg: $r('sys.float.padding_level16'),
  }).getValue(this.globalInfoModel.currentBreakpoint);
  // 使用State装饰器定义内容偏移状态
  @State contentOffset: number = new BreakpointType({
    sm: CommonConstants.SPACE_16,
    md: CommonConstants.SPACE_24,
    lg: CommonConstants.SPACE_32,
  }).getValue(this.globalInfoModel.currentBreakpoint);

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 处理断点变化
    this.handleBreakPointChange();
  }

  // 定义处理断点变化的方法
  handleBreakPointChange() {
    // 根据断点类型获取侧边栏宽度
    const barWidth =
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? CommonConstants.SIDE_BAR_WIDTH :
      CommonConstants.TAB_BAR_WIDTH;
    // 根据断点类型设置水平内边距
    this.horizontalPadding = new BreakpointType({
      sm: $r('sys.float.padding_level8'),
      md: $r('sys.float.padding_level12'),
      lg: $r('sys.float.padding_level16'),
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 根据断点类型设置内容偏移
    this.contentOffset = new BreakpointType({
      sm: CommonConstants.SPACE_16,
      md: CommonConstants.SPACE_24,
      lg: CommonConstants.SPACE_32,
    }).getValue(this.globalInfoModel.currentBreakpoint);
    // 如果是大屏或超大屏且示例内容数量为3
    if ((this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) &&
      this.sampleCardData.sampleContents.length === 3) {
      // 计算示例项目宽度为三等分
      this.sampleItemWidth = (this.globalInfoModel.deviceWidth - barWidth - 64 - 32) / 3;
    } else if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ||
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.MD) {
      // 如果是小屏或中屏则使用固定宽高比
      this.sampleItemWidth = ITEM_HEIGHT * ITEM_ASPECT;
    } else {
      // 否则计算示例项目宽度为3.5等分
      this.sampleItemWidth = Math.floor((this.globalInfoModel.deviceWidth - barWidth - 64 - 32) / 3.5);
    }
  }

  // 使用Builder装饰器定义示例项目构建器
  @Builder
  SampleItemBuilder(item: SampleContent, index: number) {
    // 创建示例项目列布局
    Column() {
      // 创建图片列布局
      Column() {
        // 显示示例项目图片
        Image($rawfile(item.mediaUrl))
          .draggable(false)
          .alt($r('app.media.img_placeholder'))
          .objectFit(ImageFit.Contain)
          .height('100%')
      }
      // 设置图片列权重为1
      .layoutWeight(1)

      // 创建内容列布局
      Column() {
        // 显示示例项目标题
        Text(item.title)
          .fontSize($r('sys.float.Body_L'))
          .fontWeight(FontWeight.Bold)
          .fontColor($r('sys.color.font_primary'))
        // 显示标签标签组件
        TagLabel({ tags: item.tags, maxWidth: LOG_MAX_WIDTH })
      }
      // 设置内容列宽度为100%
      .width('100%')
      // 设置内容列顶部内边距
      .padding({ top: $r('sys.float.padding_level8') })
      // 设置内容列渲染组
      .renderGroup(true)
    }
    // 设置示例项目列点击效果
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置示例项目列点击事件
    .onClick(() => this.handleItemClick?.(index, this.sampleCardData.sampleContents))
    // 设置示例项目列内边距
    .padding($r('sys.float.padding_level8'))
    // 设置示例项目列背景颜色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置示例项目列边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 设置示例项目列高度
    .height($r('app.float.card_scroll_height'))
    // 设置示例项目列宽度
    .width(this.sampleItemWidth)
  }

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 创建标题列布局
      Column() {
        // 显示卡片标题
        Text(this.sampleCardData.cardTitle)
          .fontSize($r('sys.float.Subtitle_L'))
          .fontColor($r('sys.color.ohos_id_color_foreground'))
          .fontWeight(FontWeight.Bold)
        // 显示卡片副标题
        Text(this.sampleCardData.cardSubTitle)
          .fontSize($r('sys.float.Body_S'))
          .fontColor($r('sys.color.font_secondary'))
          .fontWeight(FontWeight.Regular)
          .margin({
            top: $r('sys.float.padding_level2'),
            bottom: $r('sys.float.padding_level6'),
          })
      }
      // 设置标题列左对齐
      .alignItems(HorizontalAlign.Start)
      // 设置标题列宽度为100%
      .width('100%')
      // 设置标题列水平内边距
      .padding({
        left: this.horizontalPadding,
        right: this.horizontalPadding,
      })

      // 创建水平滚动列表
      List({ space: CommonConstants.SPACE_16 }) {
        // 使用Repeat组件遍历示例内容
        Repeat(this.sampleCardData.sampleContents).each((repeatItem: RepeatItem<SampleContent>) => {
          // 创建列表项
          ListItem() {
            // 调用示例项目构建器
            this.SampleItemBuilder(repeatItem.item, repeatItem.index)
          }
        })
          // 启用虚拟滚动
          .virtualScroll()
          // 设置重复项的键
          .key((item: SampleContent) => item.id.toString())
      }
      // 设置列表缓存数量
      .cachedCount(3)
      // 设置列表内容开始偏移
      .contentStartOffset(this.contentOffset)
      // 设置列表内容结束偏移
      .contentEndOffset(this.contentOffset)
      // 隐藏滚动条
      .scrollBar(BarState.Off)
      // 设置列表方向为水平
      .listDirection(Axis.Horizontal)
      // 设置列表宽度为100%
      .width('100%')
      // 设置列表高度
      .height($r('app.float.card_scroll_height'))
    }
    // 设置主列左对齐
    .alignItems(HorizontalAlign.Start)
  }
}
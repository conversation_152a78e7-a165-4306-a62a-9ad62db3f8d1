// 导入通用业务模块中的基础首页状态
import { BaseHomeState } from '@ohos/commonbusiness';
// 导入组件卡片数据源
import { ComponentCardSource } from './ComponentCardSource';

// 定义组件列表状态类，继承基础首页状态
export class ComponentListState extends BaseHomeState {
  // 定义公共卡片数据源
  public cardSource: ComponentCardSource = new ComponentCardSource()
  // 定义公共瀑布流分段
  public sections: WaterFlowSections = new WaterFlowSections();

  // 定义公共构造函数
  public constructor() {
    // 调用父类构造函数
    super();
  }
}
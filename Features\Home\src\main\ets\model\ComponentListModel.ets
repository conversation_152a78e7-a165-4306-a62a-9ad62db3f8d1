// 导入基础服务工具包中的业务错误类型定义
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的响应数据类型定义
import type { ResponseData } from '@ohos/common';
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入组件库服务
import { ComponentLibraryService } from '../service/ComponentLibraryService';
// 导入组件数据类型定义
import type { ComponentData } from './ComponentData';
// 导入组件详情数据
import { ComponentDetailData } from './ComponentDetailData';
// 导入组件详情配置
import { componentDetailConfig } from '../componentdetailview/ComponentDetailConfig';

// 定义标签常量
const TAG = '[ComponentListModel]';

// 定义组件列表模型类
export class ComponentListModel {
  // 定义私有服务实例
  private service: ComponentLibraryService = new ComponentLibraryService();
  // 定义静态实例变量
  private static instance: ComponentListModel;

  // 定义私有构造函数
  private constructor() {
  }

  // 定义获取实例的静态方法
  public static getInstance(): ComponentListModel {
    // 如果实例不存在则创建新实例
    if (!ComponentListModel.instance) {
      ComponentListModel.instance = new ComponentListModel();
    }
    // 返回实例
    return ComponentListModel.instance;
  }

  // 定义获取组件页面数据的方法
  public getComponentPage(currentPage: number, pageSize: number): Promise<ResponseData<ComponentData>> {
    // 首先尝试从偏好设置获取组件列表数据
    return this.service.getComponentListByPreference(currentPage, pageSize)
      .then((data: ResponseData<ComponentData>) => {
        // 返回获取到的数据
        return data;
      }).catch((err: string) => {
        // 记录错误日志
        Logger.error(TAG,
          `getComponentPage data from network failed! try to get data form preference. ${err}`);
        // 从网络获取组件列表数据
        return this.service.getComponentList(currentPage, pageSize)
          .then((data: ResponseData<ComponentData>) => {
            // 将数据保存到偏好设置
            this.service.setComponentListToPreference(data);
            // 返回数据
            return data;
          });
      });
  }

  // 定义预加载组件数据的方法
  public preloadComponentData(): Promise<void> {
    // 设置组件详情配置到应用存储
    AppStorage.setOrCreate('componentDetailConfig', componentDetailConfig);
    // 获取组件详情列表并保存到偏好设置
    this.service.getComponentDetailList().then((detailList: ComponentDetailData[]) => {
      this.service.setDetailsToPreference(detailList);
    });
    // 获取组件列表数据
    return this.service.getComponentList()
      .then((result: ResponseData<ComponentData>) => {
        // 将结果保存到偏好设置
        this.service.setComponentListToPreference(result);
      }).catch((err: BusinessError) => {
        // 记录错误日志
        Logger.error(TAG,
          `preloadComponentPage data from network failed. ${err.code}, ${err.message}`);
      });
  }
}
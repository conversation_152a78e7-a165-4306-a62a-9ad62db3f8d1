// 导入选择组件属性类型定义
import type { SelectComAttribute } from '../viewmodel/ComponentDetailState';
// 导入选择菜单属性修改器
import { SelectMenuAttributeModifier } from './SelectMenuAttributeModifier';

// 使用Component装饰器定义下拉选择公共组件
@Component
export struct SelectComponent {
  // 使用ObjectLink装饰器链接属性对象
  @ObjectLink attribute: SelectComAttribute;
  // 使用State装饰器定义当前索引状态
  @State currentIndex: number = 0;
  // 定义回调函数
  callback: (name: string, value: string) => void = (name: string, value: string) => {
  };

  // 定义组件即将出现时的回调方法
  aboutToAppear(): void {
    // 遍历选择选项找到当前值对应的索引
    this.attribute.selectOption.forEach((element, index) => {
      // 如果元素值等于当前值则设置当前索引
      if (element.value.toString() === this.attribute.currentValue) {
        this.currentIndex = index;
      }
    });
  }

  // 定义构建方法
  build() {
    // 创建行布局
    Row() {
      // 显示属性显示名称文本
      Text(this.attribute.disPlayName)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Subtitle_M'))
        .fontColor($r('sys.color.font_primary'))
      // 添加空白填充
      Blank()
      // 创建选择组件
      Select(this.attribute.selectOption)
        // 设置ID
        .id('select')
        // 设置选中索引
        .selected(this.currentIndex)
        // 设置当前值
        .value(this.attribute.currentValue)
        // 设置字体样式
        .font({ size: $r('sys.float.Body_M'), weight: FontWeight.Regular })
        // 设置字体颜色
        .fontColor($r('sys.color.font_secondary'))
        // 设置选项字体样式
        .optionFont({ size: $r('sys.float.Subtitle_M'), weight: FontWeight.Medium })
        // 设置选项字体颜色
        .optionFontColor($r('sys.color.font_primary'))
        // 设置选项宽度
        .optionWidth($r('app.float.detail_common_component_option_width'))
        // 设置菜单对齐方式
        .menuAlign(MenuAlignType.END, { dx: 0, dy: 0 } as Offset)
        // 设置菜单项内容修改器
        .menuItemContentModifier(new SelectMenuAttributeModifier())
        // 设置菜单背景模糊样式
        .menuBackgroundBlurStyle(BlurStyle.COMPONENT_ULTRA_THICK)
        // 设置选项背景颜色
        .optionBgColor(Color.White)
        // 设置选择事件回调
        .onSelect((_index: number, value: string) => {
          // 如果当前值不等于新值则更新
          if (this.attribute.currentValue !== value) {
            // 更新当前索引
            this.currentIndex = _index;
            // 更新属性当前值
            this.attribute.currentValue = value;
            // 调用回调函数
            this.callback(this.attribute.name, value);
          }
        })
    }
    // 设置内边距
    .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
    // 设置高度
    .height($r('app.float.common_component_height'))
    // 设置宽度为100%
    .width('100%')
  }
}
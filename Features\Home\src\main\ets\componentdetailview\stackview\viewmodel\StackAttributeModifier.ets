// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入堆叠布局描述器类型，用于描述组件配置
import type { StackDescriptor } from './StackDescriptor';

/**
 * 堆叠布局属性修改器类
 * 继承通用属性修改器，专门用于修改堆叠布局组件的属性
 * 支持对齐内容等属性的动态修改
 */
@Observed
export class StackAttributeModifier extends CommonAttributeModifier<StackDescriptor, StackAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性值应用到堆叠布局组件实例上
   * @param instance 堆叠布局组件属性实例
   */
  applyNormalAttribute(instance: StackAttribute): void {
    // 分配对齐内容属性，设置子组件在堆叠布局中的对齐方式
    this.assignAttribute((descriptor => descriptor.alignContent), (val) => instance.alignContent(val));
  }
}
// 导入通用模块中的加载模型类型
import type { LoadingModel } from '@ohos/common';
// 导入通用模块中的加载更多组件、加载状态和没有更多组件
import { LoadingMore, LoadingStatus, NoMore } from '@ohos/common';

/**
 * 加载更多项构建器函数
 * 用于在列表底部显示加载更多或没有更多数据的状态提示
 * 根据加载模型的状态自动切换显示内容
 * @param loadingModel 加载模型，包含加载状态和分页信息
 */
@Builder
export function LoadingMoreItemBuilder(loadingModel: LoadingModel) {
  // 创建垂直布局的列容器
  Column() {
    // 如果正在加载更多数据
    if (loadingModel.loadingMoreStatus === LoadingStatus.LOADING) {
      // 显示加载更多组件
      LoadingMore()
    } else if (!loadingModel.hasNextPage) {
      // 如果没有下一页数据，显示没有更多组件
      NoMore()
    }
  }
  // 设置列容器的左右内边距
  .padding({ left: $r('sys.float.padding_level8'), right: $r('sys.float.padding_level8') })
}
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承基础描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入操作表属性映射数据，包含各种配置选项的映射关系
import {
  actionSheetInfoMapData,
  autoCancelMapData,
  transitionAppearMapData,
  transitionDisAppearMapData,
  transitionMapData,
} from '../entity/ActionSheetAttributeMapping';

/**
 * 操作表描述器类
 * 继承通用描述器，用于描述操作表组件的属性和行为
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class ActionSheetDescriptor extends CommonDescriptor {
  // 自动取消属性，控制操作表是否可以通过点击外部区域自动取消
  public autoCancel: boolean = autoCancelMapData.get('Default')!.value;
  // 过渡动画属性，控制操作表是否启用过渡动画效果
  public transition: boolean = transitionMapData.get('Default')!.value;
  // 操作表选项信息数组，包含操作表中显示的各个选项
  public sheetInfo: SheetInfo[] = actionSheetInfoMapData.get('Default')!.value;
  // 过渡出现动画效果，可选属性，定义操作表出现时的动画
  public transitionAppear?: TransitionEffect = transitionAppearMapData.get('Default')!.value;
  // 过渡消失动画效果，可选属性，定义操作表消失时的动画
  public transitionDisappear?: TransitionEffect = transitionDisAppearMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为操作表描述器的具体属性值
   * @param attributes 原始属性数组，包含组件的各种配置属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理自动取消属性
        case 'autoCancel':
          // 将字符串值解析为布尔值并赋给自动取消属性
          this.autoCancel = JSON.parse(attribute.currentValue);
          break;
        // 处理过渡动画属性
        case 'transition':
          // 将字符串值解析为布尔值并赋给过渡动画属性
          this.transition = JSON.parse(attribute.currentValue);
          // 根据过渡动画是否启用，设置相应的出现和消失动画
          if (this.transition) {
            // 启用过渡动画时，使用默认的出现和消失动画效果
            this.transitionAppear = transitionAppearMapData.get('Default')!.value;
            this.transitionDisappear = transitionDisAppearMapData.get('Default')!.value;
          } else {
            // 禁用过渡动画时，设置为undefined
            this.transitionAppear = undefined;
            this.transitionDisappear = undefined;
          }
          break;
        // 处理操作表选项信息属性
        case 'sheetInfo':
          // 从映射数据中获取对应的操作表选项，如果不存在则保持原值
          this.sheetInfo = actionSheetInfoMapData.get(attribute.currentValue)?.value ?? this.sheetInfo;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
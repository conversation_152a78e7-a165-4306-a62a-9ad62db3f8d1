// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的图片工具和产品系列枚举
import { ImageUtil, ProductSeriesEnum } from '@ohos/common';
// 导入组件卡片数据和组件内容类型定义
import type { ComponentCardData, ComponentContent } from '../model/ComponentData';
// 导入组件项
import { ComponentItem } from './ComponentItem';

// 使用Reusable和Component装饰器定义可复用的图片列表卡片组件
@Reusable
@Component
export struct PictureListCard {
  // 使用State装饰器定义组件卡片数据状态
  @State componentCardData?: ComponentCardData = undefined;
  // 定义项目点击处理函数
  handleItemClick?: (componentContent: ComponentContent) => void;
  // 使用State装饰器定义按钮颜色状态
  @State buttonColor: ResourceColor = '';

  // 定义组件即将出现时的回调方法
  aboutToAppear(): void {
    // 从图片获取颜色
    this.getColorFromImg();
  }

  // 定义组件即将复用时的回调方法
  aboutToReuse(params: Record<string, Object>): void {
    // 设置组件卡片数据
    this.componentCardData = params.componentCardData as ComponentCardData;
    // 设置项目点击处理函数
    this.handleItemClick = params.handleItemClick as (componentContent: ComponentContent) => void;
    // 从图片获取颜色
    this.getColorFromImg();
  }

  // 定义从图片获取颜色的方法
  getColorFromImg() {
    // 从图片URL获取颜色数组
    ImageUtil.getColorFromImgUrl(this.componentCardData?.cardImage || '', false)
      .then((colorArr: number[]) => {
        // 设置按钮颜色为RGBA格式
        this.buttonColor = `rgba(${colorArr[0]},${colorArr[1]},${colorArr[2]},1)`;
      });
  }

  // 使用Builder装饰器定义文本覆盖层构建器
  @Builder
  textOverlay() {
    // 创建列布局
    Column() {
      // 显示卡片副标题文本
      Text(this.componentCardData?.cardSubTitle)
        .fontSize($r('sys.float.Body_S'))
        .fontColor($r('sys.color.font_on_secondary'))
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: $r('sys.float.padding_level2') })
      // 显示卡片标题文本
      Text(this.componentCardData?.cardTitle)
        .fontSize($r('sys.float.Title_M'))
        .fontColor($r('sys.color.font_on_primary'))
        .fontWeight(FontWeight.Bold)
    }
    // 设置水平对齐方式为开始
    .alignItems(HorizontalAlign.Start)
    // 设置垂直对齐方式为结束
    .justifyContent(FlexAlign.End)
    // 设置内边距
    .padding($r('sys.float.padding_level6'))
    // 设置宽度为100%
    .width('100%')
    // 根据产品系列设置高度
    .height(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('app.float.card_top_height_verde') :
    $r('app.float.card_top_height'))
    // 设置底部外边距
    .margin({ bottom: $r('sys.float.padding_level2') })
  }

  // 定义构建方法
  build() {
    // 创建列布局
    Column() {
      // 创建图片组件
      Image($rawfile(this.componentCardData?.cardImage))
        // 设置宽度为100%
        .width('100%')
        // 根据产品系列设置高度
        .height(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('app.float.card_top_height_verde') :
        $r('app.float.card_top_height'))
        // 设置文本覆盖层
        .overlay(this.textOverlay())
        // 设置图片适配方式为覆盖
        .objectFit(ImageFit.Cover)
        // 设置替代图片
        .alt($r('app.media.ic_placeholder'))
        // 设置底部外边距
        .margin({ bottom: $r('sys.float.padding_level2') })

      // 使用Repeat组件遍历卡片内容
      Repeat(this.componentCardData?.cardContents)
        .each((repeatItem: RepeatItem<ComponentContent>) => {
          // 创建组件项
          ComponentItem({
            // 传递组件内容
            componentContent: repeatItem.item,
            // 设置是否显示分隔线
            showDivider: repeatItem.index !== 0,
            // 传递按钮颜色
            buttonColor: this.buttonColor
          })
            // 设置点击事件
            .onClick(() => {
              this.handleItemClick?.(repeatItem.item);
            })
        })
        // 设置键值
        .key((componentContent: ComponentContent) => componentContent.id.toString())
    }
    // 设置点击效果级别为中等
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 启用裁剪
    .clip(true)
    // 设置宽度为100%
    .width('100%')
    // 设置背景颜色为列表卡片背景色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置底部内边距
    .padding({ bottom: $r('sys.float.padding_level2') })
  }
}
// 导入属性类型枚举
import { AttributeTypeEnum } from './AttributeTypeEnum';

// 使用Observed装饰器定义可观察的抽象属性类
@Observed
export abstract class Attribute {
  // 定义属性名称
  public name: string;
  // 定义显示名称
  public disPlayName: ResourceStr;
  // 定义当前值
  public currentValue: string;
  // 定义属性类型
  public type: AttributeTypeEnum;
  // 定义是否启用
  public enable: boolean;

  // 定义构造函数
  constructor(name: string, disPlayName: ResourceStr, currentValue: string, type: AttributeTypeEnum) {
    // 初始化属性名称
    this.name = name;
    // 初始化显示名称
    this.disPlayName = disPlayName;
    // 初始化当前值
    this.currentValue = currentValue;
    // 初始化属性类型
    this.type = type;
    // 初始化启用状态为true
    this.enable = true;
  }
}

// 定义原始属性类
export class OriginAttribute {
  // 定义属性名称，默认为空字符串
  public name: string = '';
  // 定义显示名称，默认为空字符串
  public displayName: ResourceStr = '';
  // 定义属性类型，默认为选择类型
  public type: AttributeTypeEnum = AttributeTypeEnum.SELECT;
  // 定义值数组，默认为空数组
  public values: string [] = [];
  // 定义左边界范围，默认为0
  public leftRange: number = 0;
  // 定义右边界范围，默认为0
  public rightRange: number = 0;
  // 定义步长，默认为1
  public step: number = 1;
  // 定义当前值，默认为空字符串
  public currentValue: string = '';
}
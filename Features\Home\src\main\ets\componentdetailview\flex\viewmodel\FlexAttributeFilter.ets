// 导入通用模块中的观察数组类，用于响应式数据管理
import { ObservedArray } from '@ohos/common';
// 导入属性类型，用于处理组件属性
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器基类，用于实现属性过滤接口
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * Flex属性过滤器类
 * 实现通用属性过滤器接口，用于根据Flex布局的特性动态启用或禁用相关属性
 */
export class FlexAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据Flex布局的换行设置，动态控制alignSelf和alignContent属性的启用状态
   * @param attributes 观察数组，包含所有Flex布局的属性配置
   */
  public filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性，根据属性名称进行特定的过滤逻辑
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理换行属性
        case 'wrap':
          // 查找alignSelf属性的索引位置
          const alignSelfIndex = attributes.findIndex((item) => item.name === 'alignSelf');
          // 查找alignContent属性的索引位置
          const alignContentIndex = attributes.findIndex((item) => item.name === 'alignContent');
          // 确保两个属性都存在
          if (alignSelfIndex !== -1 && alignContentIndex !== -1) {
            // 如果设置为换行或反向换行
            if (attribute.currentValue === 'Wrap' || attribute.currentValue === 'WrapReverse') {
              // 禁用alignSelf属性（换行时alignSelf不生效）
              attributes[alignSelfIndex].enable = false;
              // 启用alignContent属性（换行时可以设置多行内容对齐）
              attributes[alignContentIndex].enable = true;
            } else if (attribute.currentValue === 'NoWrap') {
              // 如果设置为不换行
              // 启用alignSelf属性（不换行时可以设置单个项目对齐）
              attributes[alignSelfIndex].enable = true;
              // 禁用alignContent属性（不换行时alignContent不生效）
              attributes[alignContentIndex].enable = false;
            }
          }
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
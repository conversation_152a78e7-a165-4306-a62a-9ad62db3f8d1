// 导入通用数字映射类，用于处理数值类型的属性映射
import { CommonNumberMapping } from '../../common/entity/CommonMapData';

/**
 * 对话框对齐方式映射类
 * 用于存储对话框对齐方式的代码字符串和实际枚举值的映射关系
 */
class DialogAlignmentMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 对话框对齐方式枚举值，只读属性
  public readonly value: DialogAlignment;

  /**
   * 构造函数
   * @param code 代码字符串，表示对齐方式的代码形式
   * @param value 对话框对齐方式枚举值
   */
  constructor(code: string, value: DialogAlignment) {
    // 初始化代码字符串
    this.code = code;
    // 初始化对齐方式枚举值
    this.value = value;
  }
}

// 导出警告对话框对齐方式映射数据，包含所有可用的对齐方式选项
export const alertDialogAlignmentMapData: Map<string, DialogAlignmentMapping> = new Map([
  // 默认对齐方式，系统自动选择合适的位置
  ['Default', new DialogAlignmentMapping('DialogAlignment.Default', DialogAlignment.Default)],
  // 顶部对齐，对话框显示在屏幕顶部
  ['Top', new DialogAlignmentMapping('DialogAlignment.Top', DialogAlignment.Top)],
  // 居中对齐，对话框显示在屏幕中央
  ['Center', new DialogAlignmentMapping('DialogAlignment.Center', DialogAlignment.Center)],
  // 底部对齐，对话框显示在屏幕底部
  ['Bottom', new DialogAlignmentMapping('DialogAlignment.Bottom', DialogAlignment.Bottom)],
]);
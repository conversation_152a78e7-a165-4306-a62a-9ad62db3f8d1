// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的产品系列枚举
import { ProductSeriesEnum } from '@ohos/common';
// 导入组件卡片数据和组件内容类型定义
import type { ComponentCardData, ComponentContent } from '../model/ComponentData';

/**
 * 代码实验室卡片组件
 * 可复用的卡片组件，用于显示代码实验室内容
 * 包含背景图片、标题、副标题和内容预览
 */
@Reusable
@Component
export struct CodeLabCard {
  // 组件卡片数据
  @State componentCardData?: ComponentCardData = undefined;
  // 内容数据
  @State content?: ComponentContent = undefined;

  /**
   * 组件即将出现时的生命周期回调
   * 初始化内容数据
   */
  aboutToAppear(): void {
    // 获取卡片内容的第一项
    this.content = this.componentCardData?.cardContents[0];
  }

  /**
   * 组件即将复用时的回调
   * 更新组件数据以支持组件复用
   * @param params 复用参数对象
   */
  aboutToReuse(params: Record<string, Object>): void {
    // 更新组件卡片数据
    this.componentCardData = params.componentCardData as ComponentCardData;
    // 更新内容数据
    this.content = this.componentCardData.cardContents[0];
  }

  /**
   * 构建代码实验室卡片的UI结构
   * 使用堆叠布局，背景图片和内容叠加显示
   */
  build() {
    // 创建底部对齐的堆叠容器
    Stack({ alignContent: Alignment.Bottom }) {
      // 背景图片
      Image($rawfile(this.componentCardData?.cardImage))
        // 设置占位图片
        .alt($r('app.media.ic_placeholder'))
        // 设置图片适配方式为覆盖
        .objectFit(ImageFit.Cover)
        // 设置图片宽度为100%
        .width('100%')
        // 设置图片高度为100%
        .height('100%')
      // 内容区域列布局
      Column() {
        // 卡片标题
        Text(this.componentCardData?.cardTitle)
          // 设置字体大小
          .fontSize($r('sys.float.Body_S'))
          // 设置字体颜色
          .fontColor($r('sys.color.font_on_secondary'))
          // 设置字体粗细
          .fontWeight(FontWeight.Medium)
          // 设置发光效果
          .lightUpEffect(1)
          // 设置边距
          .margin({
            left: $r('sys.float.padding_level6'),
            top: $r('sys.float.padding_level6'),
            bottom: $r('sys.float.padding_level2'),
          })
        // 卡片副标题
        Text(this.componentCardData?.cardSubTitle)
          // 设置字体大小
          .fontSize($r('sys.float.Title_M'))
          // 设置字体颜色
          .fontColor($r('sys.color.font_on_primary'))
          // 设置字体粗细为粗体
          .fontWeight(FontWeight.Bold)
          // 设置边距
          .margin({ left: $r('sys.float.padding_level6'), bottom: $r('sys.float.padding_level2') })
        // 内容预览行布局
        Row() {
          // 内容图片
          Image($rawfile(this.content?.mediaUrl))
            // 禁用拖拽
            .draggable(false)
            // 设置图片宽度
            .width($r('app.float.tip_image_height'))
            // 设置圆角
            .borderRadius($r('sys.float.corner_radius_level5'))
            // 设置宽高比为1:1
            .aspectRatio(1)
          // 文本内容列布局
          Column() {
            // 内容副标题
            Text(this.content?.subTitle)
              // 设置字体大小
              .fontSize($r('sys.float.Subtitle_M'))
              // 设置文本溢出处理
              .textOverflow({ overflow: TextOverflow.Ellipsis })
              // 设置字体颜色
              .fontColor($r('sys.color.font_on_primary'))
              // 设置字体粗细
              .fontWeight(FontWeight.Medium)
              // 设置底部边距
              .margin({ bottom: $r('sys.float.padding_level4') })
            // 内容标题
            Text(this.content?.title)
              // 设置字体大小
              .fontSize($r('sys.float.Body_S'))
              // 设置文本溢出处理
              .textOverflow({ overflow: TextOverflow.Ellipsis })
              // 设置字体颜色
              .fontColor($r('sys.color.font_on_primary'))
              // 设置最大行数
              .maxLines(1)
          }
          // 设置左对齐
          .alignItems(HorizontalAlign.Start)
          // 设置内边距
          .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level8') })
          // 设置布局权重
          .layoutWeight(1)

          // 右箭头按钮
          Button() {
            // 右箭头符号
            SymbolGlyph($r('sys.symbol.chevron_right'))
              // 设置图标颜色
              .fontColor([$r('sys.color.icon_secondary')])
              // 设置图标大小
              .fontSize($r('sys.float.Title_M'))
          }
          // 设置按钮背景颜色
          .backgroundColor($r('sys.color.comp_background_primary'))
          // 设置按钮宽度
          .width($r('app.float.card_button_height'))
          // 设置宽高比为1:1
          .aspectRatio(1)
        }
        // 设置行布局内边距
        .padding($r('sys.float.padding_level6'))
        // 设置行布局宽度
        .width('100%')
        // 设置行布局高度
        .height($r('app.float.codelab_content_height'))
        // 设置行布局背景颜色
        .backgroundColor($r('sys.color.comp_background_secondary'))
      }
      // 启用渲染组
      .renderGroup(true)
      // 设置内容区域高度
      .height($r('app.float.card_content_height'))
      // 设置内容区域宽度
      .width('100%')
      // 设置垂直对齐方式为底部对齐
      .justifyContent(FlexAlign.End)
      // 设置水平对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
    }
    // 设置堆叠容器宽度
    .width('100%')
    // 根据设备产品系列设置不同高度
    .height(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('app.float.codelab_card_height_verde') : $r('app.float.codelab_card_height'))
    // 设置圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 启用裁剪
    .clip(true)
    // 设置点击效果
    .clickEffect({ level: ClickEffectLevel.MIDDLE })
  }
}
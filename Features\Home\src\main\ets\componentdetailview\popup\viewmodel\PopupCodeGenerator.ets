// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入弹窗相关映射数据和样式枚举
import { placementMapData, PopupStyle, popupStyleCodeMapData, popupStyleMapData } from '../entity/PopupMapping';

/**
 * 弹窗代码生成器类
 * 实现通用代码生成器接口，用于生成弹窗组件代码
 */
export class PopupCodeGenerator implements CommonCodeGenerator {
  // 弹窗位置代码，默认为底部位置
  private placement: string = placementMapData.get('Default')!.code;
  // 弹窗样式类型，默认为带按钮样式
  private type: PopupStyle = popupStyleMapData.get('Default')!;
  // 弹窗样式代码，根据样式类型获取对应代码
  private styleCode: string = popupStyleCodeMapData.get(this.type)!;

  /**
   * 生成弹窗代码方法
   * 根据属性配置生成完整的弹窗组件代码
   * @param _attributes 原始属性数组，包含组件配置信息
   * @returns 生成的弹窗代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 宽度配置字符串，用于非文本类型弹窗
    let width: string = '';
    // 遍历所有属性，根据属性名称设置对应的配置值
    _attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'placement':
          // 设置弹窗位置
          this.placement = placementMapData.get(attribute.currentValue)?.code ?? this.placement;
          break;
        case 'type':
          // 设置弹窗样式类型
          this.type = popupStyleMapData.get(attribute.currentValue) ?? this.type;
          // 更新样式代码
          this.styleCode = popupStyleCodeMapData.get(this.type) ?? this.styleCode;
          // 非文本类型弹窗需要设置固定宽度
          if (this.type !== PopupStyle.STYLE_TEXT) {
            width = `
          width: '300vp',`;
          }
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整弹窗代码字符串
    return `// 导入弹窗组件
// 提供弹窗功能，支持多种样式和位置配置
import { Popup } from '@kit.ArkUI';

// 弹窗组件
// 提供按钮触发的弹窗功能
@Component
struct PopupComponent {
  // 弹窗显示状态
  @State handlePopup: boolean = false;

  // 弹窗内容构建器方法
  @Builder
  popupWithButtonBuilder() {
    // 创建行布局容器
    Row() {
      // 插入弹窗样式代码
      ${this.styleCode}
    }
  }

  // 构建组件UI方法
  build() {
    // 创建列布局容器
    Column() {
      // 创建触发弹窗的按钮
      Button('点击弹出气泡')
        // 设置按钮背景颜色为次要背景色
        .backgroundColor($r('sys.color.background_secondary'))
        // 设置按钮字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮点击事件，显示弹窗
        .onClick(() => {
          this.handlePopup = true;
        })
        // 绑定弹窗到按钮
        .bindPopup(this.handlePopup, {
          // 设置弹窗内容构建器
          builder: this.popupWithButtonBuilder(),
          // 设置弹窗位置
          placement: ${this.placement},${width}
          // 设置弹窗状态变化回调
          onStateChange: (e) => {
            // 当弹窗不可见时，更新状态
            if (!e.isVisible) {
              this.handlePopup = false;
            }
          }
        })
    }
    // 设置列布局宽度为100%
    .width('100%')
    // 设置列布局高度为100%
    .height('100%')
    // 设置列布局垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
  }
}`;
  }
}
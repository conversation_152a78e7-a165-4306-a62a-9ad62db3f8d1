// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入操作表属性映射数据，包含各种配置选项的映射关系
import {
  actionSheetInfoMapData,
  autoCancelMapData,
  transitionAppearMapData,
  transitionDisAppearMapData,
  transitionMapData,
} from '../entity/ActionSheetAttributeMapping';

/**
 * 操作表代码生成器类
 * 实现通用代码生成器接口，用于生成操作表组件的代码
 */
export class ActionSheetCodeGenerator implements CommonCodeGenerator {
  // 自动取消属性的代码字符串，默认使用映射数据中的默认值
  private autoCancel: string = autoCancelMapData.get('Default')!.code;
  // 过渡动画属性的代码字符串，默认使用映射数据中的默认值
  private transition: string = transitionMapData.get('Default')!.code;
  // 操作表选项信息的代码字符串，默认使用映射数据中的默认值
  private sheetInfo: string = actionSheetInfoMapData.get('Default')!.code;
  // 过渡出现动画的代码字符串，默认使用映射数据中的默认值
  private transitionAppear: string = transitionAppearMapData.get('Default')!.code;
  // 过渡消失动画的代码字符串，默认使用映射数据中的默认值
  private transitionDisappear: string = transitionDisAppearMapData.get('Default')!.code;

  /**
   * 生成操作表组件代码的方法
   * 根据传入的属性数组生成完整的操作表组件代码字符串
   * @param attributes 原始属性数组，包含组件的各种配置属性
   * @returns 生成的操作表组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理自动取消属性
        case 'autoCancel':
          // 更新自动取消属性的代码字符串
          this.autoCancel = attribute.currentValue;
          break;
        // 处理过渡动画属性
        case 'transition':
          // 更新过渡动画属性的代码字符串
          this.transition = attribute.currentValue;
          // 根据过渡动画是否启用，设置相应的出现和消失动画
          if (this.transition === 'true') {
            // 启用过渡动画时，使用默认的出现和消失动画
            this.transitionAppear = transitionAppearMapData.get('Default')!.code;
            this.transitionDisappear = transitionDisAppearMapData.get('Default')!.code;
          } else {
            // 禁用过渡动画时，设置为undefined
            this.transitionAppear = 'undefined';
            this.transitionDisappear = 'undefined';
          }
          break;
        // 处理操作表选项信息属性
        case 'sheetInfo':
          // 从映射数据中获取对应的操作表选项代码，如果不存在则保持原值
          this.sheetInfo = actionSheetInfoMapData.get(attribute.currentValue)?.code ?? this.sheetInfo;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 返回生成的操作表组件代码字符串，包含完整的组件定义和配置
    return `import { promptAction } from '@kit.ArkUI';

@Component
struct ActionSheetComponent {
  build() {
    Flex({ direction: FlexDirection.Column, alignItems: ItemAlign.Center, justifyContent: FlexAlign.Center }) {
      Button('列表选择器弹窗', { buttonStyle: ButtonStyleMode.NORMAL })
        .buttonStyle(ButtonStyleMode.NORMAL)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))
        .onClick(() => {
          ActionSheet.show({
            title: '标题',
            subtitle: '副标题',
            message: '内容',
            autoCancel: ${this.autoCancel},
            transition: TransitionEffect.asymmetric(
              ${this.transitionAppear},
              ${this.transitionDisappear}
            ),
            confirm: {
              defaultFocus: true,
              value: '确定',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'confirm is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              }
            },
            alignment: DialogAlignment.Center,
            offset: { dx: 0, dy: -10 },
            sheets: ${this.sheetInfo}
          });
        })
    }
    .width('100%')
    .height('100%')
  }
}`;
  }
}
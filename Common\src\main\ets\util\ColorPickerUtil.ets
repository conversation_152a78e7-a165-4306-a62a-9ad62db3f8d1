/**
 * 颜色选择器工具类
 * 提供颜色选择和转换相关的静态方法
 * 支持RGBA颜色设置、颜色块计算和RGB转换功能
 */
export class ColorPickerUtil {
  // 当前选中的颜色字符串
  private static currentColor: string;

  /**
   * 设置RGBA颜色方法
   * 根据红绿蓝和透明度值生成RGBA颜色字符串
   * @param red 红色值（0-255）
   * @param green 绿色值（0-255）
   * @param blue 蓝色值（0-255）
   * @param opacity 透明度值（0-1）
   * @returns RGBA颜色字符串
   */
  public static setRgba(red: number, green: number, blue: number, opacity: number): string {
    // 返回格式化的RGBA颜色字符串
    return `rgba(${red},${green},${blue},${opacity})`;
  }

  /**
   * 获取RGB文本方法
   * 返回当前选中的颜色字符串
   * @returns 当前颜色字符串
   */
  public static getRgbText() {
    // 返回当前颜色
    return ColorPickerUtil.currentColor;
  }

  /**
   * 获取颜色块颜色方法
   * 根据滑块区域的值计算对应的颜色
   * 使用HSV色彩模型的色相环算法
   * @param value 滑块值（0-100）
   * @returns RGBA颜色字符串
   */
  public static getBlockColor(value: number): string {
    // 将滑块值转换为颜色百分比
    const colorPercent = value / 100;
    // 初始化选中的颜色字符串
    let selectedColor: string = '';
    // 初始化颜色区域百分比
    let colorAreaPercent: number = 0;
    // 第一个色相区间：红色到黄色（0-1/6）
    if (colorPercent >= 0 && colorPercent <= 1 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = colorPercent * 6;
      // 红色固定255，绿色递增，蓝色为0
      selectedColor = ColorPickerUtil.setRgba(255, Math.floor(colorAreaPercent * 255), 0, 1.00);
    }
    // 第二个色相区间：黄色到绿色（1/6-2/6）
    else if (colorPercent >= 1 / 6 && colorPercent <= 2 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = (colorPercent - 1 / 6) * 6;
      // 红色递减，绿色固定255，蓝色为0
      selectedColor = ColorPickerUtil.setRgba(Math.floor(((1 - colorAreaPercent) * 255)), 255, 0, 1.00);
    }
    // 第三个色相区间：绿色到青色（2/6-3/6）
    else if (colorPercent >= 2 / 6 && colorPercent <= 3 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = (colorPercent - 2 / 6) * 6;
      // 红色为0，绿色固定255，蓝色递增
      selectedColor = ColorPickerUtil.setRgba(0, 255, Math.floor(colorAreaPercent * 255), 1.00);
    }
    // 第四个色相区间：青色到蓝色（3/6-4/6）
    else if (colorPercent >= 3 / 6 && colorPercent <= 4 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = (colorPercent - 3 / 6) * 6;
      // 红色为0，绿色递减，蓝色固定255
      selectedColor = ColorPickerUtil.setRgba(0, Math.floor(((1 - colorAreaPercent) * 255)), 255, 1.00);
    }
    // 第五个色相区间：蓝色到紫色（4/6-5/6）
    else if (colorPercent >= 4 / 6 && colorPercent <= 5 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = (colorPercent - 4 / 6) * 6;
      // 红色递增，绿色为0，蓝色固定255
      selectedColor = ColorPickerUtil.setRgba(Math.floor(colorAreaPercent * 255), 0, 255, 1.00);
    }
    // 第六个色相区间：紫色到红色（5/6-6/6）
    else if (colorPercent >= 5 / 6 && colorPercent <= 6 / 6) {
      // 计算在当前区间的百分比
      colorAreaPercent = (colorPercent - 5 / 6) * 6;
      // 红色固定255，绿色为0，蓝色递减
      selectedColor = ColorPickerUtil.setRgba(255, 0, Math.floor(((1 - colorAreaPercent) * 255)), 1.00);
    }
    // 提取RGB部分并设置为当前颜色
    ColorPickerUtil.currentColor = `${selectedColor.substring(4, selectedColor.length - 3)})`;
    // 返回完整的RGBA颜色字符串
    return selectedColor;
  }

  /**
   * 获取RGB值方法
   * 从RGBA颜色字符串中解析出RGB数值数组
   * @param rgb RGBA颜色字符串
   * @returns RGB数值数组 [红, 绿, 蓝]
   */
  public static getRgb(rgb: string): number[] {
    // 去除"rgba("和")"，保留中间的数值部分
    rgb = rgb.substring(5, rgb.length - 1);
    // 按逗号分割RGB值
    const rgbArray = rgb.split(',');
    // 解析红色值
    const redArea: number = parseFloat(rgbArray[0]);
    // 解析绿色值
    const greenArea: number = parseFloat(rgbArray[1]);
    // 解析蓝色值
    const blueArea: number = parseFloat(rgbArray[2]);
    // 返回RGB数值数组
    return [redArea, greenArea, blueArea];
  }

  /**
   * 从RGB获取颜色百分比方法
   * 根据RGB值计算在色相环中的位置百分比
   * 逆向计算getBlockColor方法的输入值
   * @param rgb RGBA颜色字符串
   * @returns 颜色在色相环中的百分比（0-100）
   */
  public static getColorFromRgb(rgb: string): number {
    // 获取RGB数值数组
    const rgbArray = ColorPickerUtil.getRgb(rgb);
    // 提取红色值
    const redArea: number = rgbArray[0];
    // 提取绿色值
    const greenArea: number = rgbArray[1];
    // 提取蓝色值
    const blueArea: number = rgbArray[2];
    // 计算总颜色数量（6个色相区间 × 255）
    const allColorCount = 255 * 6;
    // 初始化颜色百分比
    let colorPercent: number = 0.00;
    // 第一个色相区间：红色到黄色（红=255，蓝=0）
    if (redArea === 255 && blueArea === 0) {
      colorPercent = greenArea / allColorCount;
    }
    // 第二个色相区间：黄色到绿色（绿=255，蓝=0）
    else if (greenArea === 255 && blueArea === 0) {
      colorPercent = (redArea + 255) / allColorCount;
    }
    // 第三个色相区间：绿色到青色（红=0，绿=255）
    else if (redArea === 0 && greenArea === 255) {
      colorPercent = (blueArea + 255 * 2) / allColorCount;
    }
    // 第四个色相区间：青色到蓝色（红=0，蓝=255）
    else if (redArea === 0 && blueArea === 255) {
      colorPercent = (greenArea + 255 * 3) / allColorCount;
    }
    // 第五个色相区间：蓝色到紫色（绿=0，蓝=255）
    else if (greenArea === 0 && blueArea === 255) {
      colorPercent = (redArea + 255 * 4) / allColorCount;
    }
    // 第六个色相区间：紫色到红色（红=255，绿=0）
    else if (redArea === 255 && greenArea === 0) {
      colorPercent = (blueArea + 255 * 5) / allColorCount;
    }
    // 返回百分比值（0-100）
    return colorPercent * 100;
  }
}
/**
 * 颜色转换处理工具类
 * 提供RGB和HSB颜色空间之间的转换功能
 * 支持生成沉浸式背景颜色效果
 */
export class ColorUtil {
  /**
   * 获取加深沉浸式颜色方法
   * 为图像生成沉浸式背景颜色，通过调整饱和度和亮度实现
   * @param rRGB 红色值（0-255）
   * @param gRGB 绿色值（0-255）
   * @param bRGB 蓝色值（0-255）
   * @returns RGB颜色数组 [红, 绿, 蓝]
   */
  public static getDeepenImmersionColor(rRGB: number, gRGB: number, bRGB: number): number[] {
    // 将RGB转换为HSB色彩空间
    const hsb: number[] = ColorUtil.rgbToHsb(rRGB, gRGB, bRGB);
    // 获取色相值（保持不变）
    const hHSB = hsb[0];
    // 获取饱和度值
    let sHSB = hsb[1];
    // 获取亮度值
    let bHSB = hsb[2];
    // 增加饱和度，使颜色更鲜艳
    sHSB = sHSB + 0.35;
    // 如果亮度大于0.15
    if (bHSB > 0.15) {
      // 降低亮度，使颜色更深
      bHSB = bHSB - 0.4;
      // 确保亮度不低于最小值0.15
      if (bHSB < 0.15) {
        bHSB = 0.15;
      }
    }
    // 将调整后的HSB转换回RGB并返回
    return ColorUtil.hsbToRgb(hHSB, sHSB, bHSB);
  }

  /**
   * RGB转HSB私有方法
   * 将RGB颜色空间转换为HSB（色相、饱和度、亮度）颜色空间
   * @param rRGB 红色值（0-255）
   * @param gRGB 绿色值（0-255）
   * @param bRGB 蓝色值（0-255）
   * @returns HSB颜色数组 [色相, 饱和度, 亮度]
   */
  private static rgbToHsb(rRGB: number, gRGB: number, bRGB: number): [hHsb: number, sHsb: number, bHsb: number] {
    // 找到RGB中的最大值
    const max = Math.max(rRGB, gRGB, bRGB);
    // 找到RGB中的最小值
    const min = Math.min(rRGB, gRGB, bRGB);
    // 计算饱和度：如果最大值为0则饱和度为0，否则为(最大值-最小值)/最大值
    const sHSB = max === 0 ? 0 : (max - min) / max;
    // 计算亮度：(最大值+10)/255，加10是为了调整亮度效果
    const bHSB = (max + 10) / 255;
    // 初始化色相值
    let hHSB = 0;
    // 当红色为最大值且绿色大于等于蓝色时
    if (max === rRGB && gRGB >= bRGB) {
      // 色相在0-60度区间
      hHSB = 60 * (gRGB - bRGB) / (max - min) + 0;
    }
    // 当红色为最大值且绿色小于蓝色时
    if (max === rRGB && gRGB < bRGB) {
      // 色相在300-360度区间
      hHSB = 60 * (gRGB - bRGB) / (max - min) + 360;
    }
    // 当绿色为最大值时
    if (max === gRGB) {
      // 色相在120-180度区间
      hHSB = 60 * (bRGB - rRGB) / (max - min) + 120;
    }
    // 当蓝色为最大值时
    if (max === bRGB) {
      // 色相在240-300度区间
      hHSB = 60 * (rRGB - gRGB) / (max - min) + 240;
    }
    // 返回HSB颜色数组
    return [hHSB, sHSB, bHSB];
  }

  /**
   * HSB转RGB私有方法
   * 将HSB（色相、饱和度、亮度）颜色空间转换为RGB颜色空间
   * @param hHSB 色相值（0-360度）
   * @param sHSB 饱和度值（0-1）
   * @param bHSB 亮度值（0-1）
   * @returns RGB颜色数组 [红, 绿, 蓝]
   */
  private static hsbToRgb(hHSB: number, sHSB: number, bHSB: number): number[] {
    // 计算色相在哪个60度区间（0-5）
    const i: number = Math.floor((hHSB / 60) % 6);
    // 计算色相在当前区间内的小数部分
    const f = (hHSB / 60) - i;
    // 计算辅助值p：降低饱和度后的亮度
    const p = bHSB * (1 - sHSB);
    // 计算辅助值q：考虑色相位置的降低饱和度后的亮度
    const q = bHSB * (1 - f * sHSB);
    // 计算辅助值t：考虑色相位置的另一种降低饱和度后的亮度
    const t = bHSB * (1 - (1 - f) * sHSB);
    // 初始化RGB值
    let rRGB = bHSB;
    let gRGB = t;
    let bRGB = p;
    // 根据色相所在的60度区间设置RGB值
    switch (i) {
      // 0-60度：红色到黄色
      case 0:
        rRGB = bHSB;  // 红色为最大亮度
        gRGB = t;     // 绿色递增
        bRGB = p;     // 蓝色为最小值
        break;
      // 60-120度：黄色到绿色
      case 1:
        rRGB = q;     // 红色递减
        gRGB = bHSB;  // 绿色为最大亮度
        bRGB = p;     // 蓝色为最小值
        break;
      // 120-180度：绿色到青色
      case 2:
        rRGB = p;     // 红色为最小值
        gRGB = bHSB;  // 绿色为最大亮度
        bRGB = t;     // 蓝色递增
        break;
      // 180-240度：青色到蓝色
      case 3:
        rRGB = p;     // 红色为最小值
        gRGB = q;     // 绿色递减
        bRGB = bHSB;  // 蓝色为最大亮度
        break;
      // 240-300度：蓝色到紫色
      case 4:
        rRGB = t;     // 红色递增
        gRGB = p;     // 绿色为最小值
        bRGB = bHSB;  // 蓝色为最大亮度
        break;
      // 300-360度：紫色到红色
      case 5:
        rRGB = bHSB;  // 红色为最大亮度
        gRGB = p;     // 绿色为最小值
        bRGB = q;     // 蓝色递减
        break;
      default:
        break;
    }
    // 将0-1范围的RGB值转换为0-255范围，并确保不小于0
    return [Math.max(0, Math.floor(rRGB * 255.0)), Math.max(0, Math.floor(gRGB * 255.0)),
      Math.max(0, Math.floor(bRGB * 255.0))];
  }
}
// 导入能力工具包中的能力常量、配置、配置常量、UI能力和Want
import { AbilityConstant, Configuration, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
// 导入ArkUI工具包中的窗口模块
import { window } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的包管理工具、日志工具、页面上下文、窗口工具、动态安装管理器、Web工具
import { BundleManagerUtil, Logger, PageContext, WindowUtil, DynamicInstallManager, WebUtil } from '@ohos/common';
// 导入全局UI能力上下文配置
import GlobalUIAbilityContext from '../util/ContextConfig';

// 日志标签常量
const TAG = '[EntryAbility]';

/**
 * 入口能力类
 * 继承自UIAbility，作为应用的主要入口点
 * 负责应用的生命周期管理、窗口创建、配置更新等
 */
export default class EntryAbility extends UIAbility {
  /**
   * 能力创建时的回调方法
   * 在应用启动时被调用，进行初始化设置
   * @param want 启动意图
   * @param launchParam 启动参数
   */
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 记录能力创建日志
    Logger.info(TAG, 'Ability onCreate');
    // 检查并处理启动参数
    this.checkAndHandleParams(want);
    // 设置系统颜色模式到应用存储
    AppStorage.setOrCreate('systemColorMode', this.context.config.colorMode);
    // 设置应用上下文的颜色模式为未设置状态
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
  }

  /**
   * 新意图回调方法
   * 当应用已经启动但收到新的启动意图时被调用
   * @param want 新的启动意图
   * @param launchParam 启动参数
   */
  onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 记录新意图日志
    Logger.info(TAG, `onNewWant`);
    // 检查并处理新的启动参数
    this.checkAndHandleParams(want);
  }

  /**
   * 检查并处理启动参数
   * 验证Want对象和参数的有效性
   * @param want 启动意图对象
   */
  checkAndHandleParams(want: Want): void {
    // 记录参数检查日志
    Logger.info(TAG, `checkAndHandleParams`);
    try {
      // 检查Want对象和参数是否为空或未定义
      if (want === null || want.parameters === null || want === undefined || want.parameters === undefined) {
        return;
      }
    } catch (err) {
      // 记录参数检查失败的错误日志
      Logger.error(TAG, `checkAndHandleParams failed: ${err}`);
    }
  }

  /**
   * 能力销毁时的回调方法
   * 在应用关闭时被调用，进行资源清理
   */
  onDestroy(): void {
    // 记录能力销毁日志
    Logger.info(TAG, 'Ability onDestroy');
    // 取消订阅动态安装的下载进度
    DynamicInstallManager.unsubscribeDownloadProgress();
  }

  /**
   * 窗口阶段创建时的回调方法
   * 主窗口创建完成后被调用，设置主页面和初始化各种工具
   * @param windowStage 窗口阶段对象
   */
  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 记录窗口阶段创建日志
    Logger.info(TAG, 'Ability onWindowStageCreate');
    // 设置全局UI能力上下文，用于penkit组件
    GlobalUIAbilityContext.setContext(this.context);
    // 请求全屏显示
    WindowUtil.requestFullScreen(windowStage, this.context);
    // 注册断点监听器，用于响应式设计
    WindowUtil.registerBreakPoint(windowStage);
    // 获取包信息
    BundleManagerUtil.getBundleInfo();
    // 创建并设置各种页面上下文到应用存储
    AppStorage.setOrCreate('pageContext', new PageContext());
    AppStorage.setOrCreate('samplePageContext', new PageContext());
    AppStorage.setOrCreate('componentListPageContext', new PageContext());
    AppStorage.setOrCreate('explorationPageContext', new PageContext());
    // 加载启动页面内容
    windowStage.loadContent('page/SplashPage', (err: BusinessError) => {
      // 初始化Web工具
      WebUtil.initialize(windowStage);
      // 检查加载是否出错
      if (err.code) {
        // 记录加载失败的错误日志
        Logger.error(TAG, `Failed to load the content. Cause: ${err.code} ${err.message}`);
        return;
      }
      // 加载成功后隐藏PC/2in1设备的标题栏
      WindowUtil.hideTitleBar(windowStage);
      // 记录加载成功日志
      Logger.info(TAG, 'Succeeded in loading the content.');
    });
  }

  /**
   * 配置更新时的回调方法
   * 当系统配置发生变化时被调用，如颜色模式切换
   * @param newConfig 新的配置对象
   */
  onConfigurationUpdate(newConfig: Configuration): void {
    // 获取新的颜色模式，默认为深色模式
    const newColorMode: ConfigurationConstant.ColorMode =
      newConfig.colorMode || ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    // 获取当前应用存储中的颜色模式
    const currentColorMode = AppStorage.get<ConfigurationConstant.ColorMode>('systemColorMode');
    // 当系统深色/浅色模式与应用颜色模式不同时，修改应用的颜色模式
    if (newColorMode !== currentColorMode) {
      // 更新应用存储中的系统颜色模式
      AppStorage.setOrCreate('systemColorMode', newColorMode);
    }
  }

  /**
   * 窗口阶段销毁时的回调方法
   * 主窗口被销毁时被调用，释放UI相关资源
   */
  onWindowStageDestroy(): void {
    // 记录窗口阶段销毁日志
    Logger.info(TAG, 'Ability onWindowStageDestroy');
  }

  /**
   * 能力前台运行时的回调方法
   * 能力被带到前台时被调用
   */
  onForeground(): void {
    // 记录能力前台运行日志
    Logger.info(TAG, 'Ability onForeground');
  }

  /**
   * 能力后台运行时的回调方法
   * 能力退到后台时被调用
   */
  onBackground(): void {
    // 记录能力后台运行日志
    Logger.info(TAG, 'Ability onBackground');
  }
}
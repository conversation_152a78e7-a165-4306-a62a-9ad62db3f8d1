// 导入方舟UI工具包中的提示操作模块，用于显示提示信息
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器，用于记录日志信息
import { Logger } from '@ohos/common';
// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入通用布尔映射类，用于布尔值的映射配置
import { CommonBoolMapping } from '../../common/entity/CommonMapData';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[ActionSheetBuilder]';

/**
 * 操作表信息映射类
 * 用于存储操作表选项的代码字符串和实际值的映射关系
 */
class ActionSheetInfoMapping {
  // 代码字符串，用于代码生成
  public code: string;
  // 操作表选项数组，包含实际的选项配置
  public value: SheetInfo[];

  /**
   * 构造函数
   * @param code 代码字符串
   * @param value 操作表选项数组
   */
  public constructor(code: string, value: SheetInfo[]) {
    // 初始化代码字符串
    this.code = code;
    // 初始化操作表选项数组
    this.value = value;
  }
}

// 导出自动取消映射数据，用于配置操作表的自动取消行为
export const autoCancelMapData: Map<string, CommonBoolMapping> = new Map([
  // 默认配置，设置为true表示启用自动取消
  ['Default', new CommonBoolMapping('true', true)],
]);

// 导出过渡动画映射数据，用于配置操作表的过渡动画
export const transitionMapData: Map<string, CommonBoolMapping> = new Map([
  // 默认配置，设置为true表示启用过渡动画
  ['Default', new CommonBoolMapping('true', true)],
]);

// 定义第一组操作表选项，包含三个基本选项
const sheetOne: SheetInfo[] = [
  // 第一个选项配置
  {
    // 设置选项标题为item1
    title: $r('app.string.item1'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示item1被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'item1'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
      }
    },
  },
  // 第二个选项配置
  {
    // 设置选项标题为item2
    title: $r('app.string.item2'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示item2被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'item2'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
      }
    },
  },
  // 第三个选项配置
  {
    // 设置选项标题为item3
    title: $r('app.string.item3'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示item3被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'item3'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
      }
    },
  },
];

// 定义第一组操作表选项的代码字符串，用于代码生成和显示
const sheetOneStr: string = `[{
              title: 'item1',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'item1 is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            },
            {
              title: 'item2',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'item2 is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            },
            {
              title: 'item3',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'item3 is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            },]`;

// 定义第二组操作表选项，包含水果相关的选项
const sheetTwo: SheetInfo[] = [
  // 苹果选项配置
  {
    // 设置选项标题为苹果
    title: $r('app.string.apples'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示苹果被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'apples'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}, the message is ${error.message}`);
      }
    },
  },
  // 香蕉选项配置
  {
    // 设置选项标题为香蕉
    title: $r('app.string.bananas'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示香蕉被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'bananas'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}, the message is ${error.message}`);
      }
    },
  },
  // 梨子选项配置
  {
    // 设置选项标题为梨子
    title: $r('app.string.pears'),
    // 设置选项点击事件处理函数
    action: () => {
      // 使用try-catch处理可能的异常
      try {
        // 显示梨子被点击的提示信息
        promptAction.showToast({
          // 设置提示消息内容
          message: $r('app.string.item_click', 'pears'),
          // 设置提示显示时长
          duration: DetailPageConstant.LONG_DURATION,
        });
      } catch (err) {
        // 捕获异常并转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志，包含错误代码和错误消息
        Logger.error(TAG, `Show toast error, the code is ${error.code}, the message is ${error.message}`);
      }
    },
  },
];

// 定义第二组操作表选项的代码字符串，用于代码生成和显示
const sheetTwoStr: string = `[{
              title: 'apples',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'apples is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            },
            {
              title: 'bananas',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'bananas is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            },
            {
              title: 'pears',
              action: () => {
                try {
                  promptAction.showToast({
                    message: 'pears is clicked',
                    duration: 2000,
                  });
                } catch (err) {
                  const error: BusinessError = err as BusinessError;
                  console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
                }
              },
            }]`;

// 导出操作表信息映射数据，包含不同的操作表配置选项
export const actionSheetInfoMapData: Map<string, ActionSheetInfoMapping> = new Map([
  // 默认配置，使用第一组选项
  ['Default', new ActionSheetInfoMapping(sheetOneStr, sheetOne)],
  // 第一个自定义配置，使用第二组选项（水果选项）
  ['sheetInfo1', new ActionSheetInfoMapping(sheetTwoStr, sheetTwo)],
  // 第二个自定义配置，使用第一组选项
  ['sheetInfo2', new ActionSheetInfoMapping(sheetOneStr, sheetOne)],
]);

// 定义过渡出现动画的代码字符串，用于代码生成
const transitionAppearCode: string = `TransitionEffect.OPACITY
                .animation({ duration: 500, curve: Curve.Sharp })
                .combine(TransitionEffect.scale({ x: 1.5, y: 1.5 })
                .animation({ duration: 500, curve: Curve.Sharp }))`;

// 定义过渡消失动画的代码字符串，用于代码生成
const transitionDisappearCode: string = `TransitionEffect.OPACITY
                .animation({ duration: 300, curve: Curve.Smooth })
                .combine(TransitionEffect.scale({ x: 0.5, y: 0.5 })
                .animation({ duration: 300, curve: Curve.Smooth }))`;

/**
 * 过渡动画映射类
 * 用于存储过渡动画的代码字符串和实际效果的映射关系
 */
class TransitionMap {
  // 代码字符串，用于代码生成
  public code: string;
  // 过渡效果对象，包含实际的动画配置
  public value: TransitionEffect;

  /**
   * 构造函数
   * @param code 代码字符串
   * @param value 过渡效果对象
   */
  constructor(code: string, value: TransitionEffect) {
    // 初始化代码字符串
    this.code = code;
    // 初始化过渡效果对象
    this.value = value;
  }
}

// 导出过渡出现动画映射数据，用于配置操作表的出现动画
export const transitionAppearMapData: Map<string, TransitionMap> = new Map([
  // 默认出现动画配置，包含透明度和缩放效果
  ['Default',
    new TransitionMap(transitionAppearCode,
      // 创建透明度动画，设置持续时间和曲线类型
      TransitionEffect.OPACITY.animation({ duration: DetailPageConstant.ANIMATION_DURATION, curve: Curve.Sharp })
        // 组合缩放动画，从1.5倍缩放到正常大小
        .combine(TransitionEffect.scale({ x: 1.5, y: 1.5 })
          // 设置缩放动画的持续时间和曲线类型
          .animation({ duration: DetailPageConstant.ANIMATION_DURATION, curve: Curve.Sharp })))],
]);

// 导出过渡消失动画映射数据，用于配置操作表的消失动画
export const transitionDisAppearMapData: Map<string, TransitionMap> = new Map([
  // 默认消失动画配置，包含透明度和缩放效果
  ['Default',
    new TransitionMap(transitionDisappearCode,
      // 创建透明度动画，设置较短的持续时间和平滑曲线
      TransitionEffect.OPACITY.animation({ duration: DetailPageConstant.ANIMATION_DURATION_SHORT, curve: Curve.Smooth })
        // 组合缩放动画，缩放到0.5倍大小
        .combine(TransitionEffect.scale({ x: 0.5, y: 0.5 })
          // 设置缩放动画的持续时间和曲线类型
          .animation({ duration: DetailPageConstant.ANIMATION_DURATION_SHORT, curve: Curve.Smooth })))],
]);
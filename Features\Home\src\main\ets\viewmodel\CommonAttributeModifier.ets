// 定义通用属性修改器抽象类，实现属性修改器接口
export abstract class CommonAttributeModifier<TDescriptor, TAttribute> implements AttributeModifier<TAttribute> {
  // 定义属性持有者
  public attributeHolder: TDescriptor;

  // 定义构造函数
  constructor(attributeHolder: TDescriptor) {
    // 初始化属性持有者
    this.attributeHolder = attributeHolder;
  }

  // 定义抽象方法应用普通属性
  abstract applyNormalAttribute(instance: TAttribute): void;

  // 定义分配属性的方法
  assignAttribute<TAttributeValue>(
    extractAttributeValue: (attributeHolder: TDescriptor) => TAttributeValue,
    assign: (propertyValue: TAttributeValue) => void,
  ): void {
    // 提取属性值
    const attributeValue = extractAttributeValue(this.attributeHolder);
    // 分配属性值
    assign(attributeValue);
  }
}
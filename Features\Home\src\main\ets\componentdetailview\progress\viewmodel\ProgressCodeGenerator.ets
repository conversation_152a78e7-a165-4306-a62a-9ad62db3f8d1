// 导入详情页常量，包含进度条相关的尺寸配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入进度条相关映射数据
import {
  progressColorMapData,
  progressKindMapData,
  progressStyleMapData,
  progressTypeMapData,
  progressValueMapData,
} from '../entity/ProgressAttributeMapping';

/**
 * 进度条代码生成器类
 * 实现通用代码生成器接口，用于生成进度条组件代码
 */
export class ProgressCodeGenerator implements CommonCodeGenerator {
  // 进度值，默认使用映射数据中的默认值
  private value: string = progressValueMapData.get('Default')!.code;
  // 进度条颜色，默认使用映射数据中的默认颜色
  private color: string = progressColorMapData.get('Default')!.code;
  // 进度条类型，默认使用映射数据中的默认类型
  private type: string = progressTypeMapData.get('Default')!.code;
  // 进度条样式，默认使用映射数据中的默认样式
  private style: string = progressStyleMapData.get('Default')!.code;
  // 进度条类别，默认使用映射数据中的默认类别
  private kind: string = progressKindMapData.get('Default')!.code;
  // 进度条高度，默认为线性进度条高度
  private height: number = DetailPageConstant.PROGRESS_LINE_HEIGHT;

  /**
   * 生成进度条代码方法
   * 根据属性配置生成完整的进度条组件代码
   * @param attributes 原始属性数组，包含组件配置信息
   * @returns 生成的进度条代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称设置对应的配置值
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'value':
          // 设置进度值
          this.value = attribute.currentValue;
          break;
        case 'color':
          // 设置进度条颜色
          this.color = attribute.currentValue;
          break;
        case 'kind':
          // 设置进度条类别
          this.kind = attribute.currentValue;
          break;
        case 'style':
          // 设置进度条类型和样式
          this.type = progressTypeMapData.get(attribute.currentValue)?.code ?? this.type;
          this.style = progressStyleMapData.get(attribute.currentValue)?.code ?? this.style;
          // 根据类型设置高度，胶囊型使用不同高度
          this.height = this.type === 'ProgressType.Capsule' ? DetailPageConstant.PROGRESS_CAPSULE_HEIGHT :
          DetailPageConstant.PROGRESS_LINE_HEIGHT;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 根据进度条类别生成不同的代码字符串
    let codeStr = '';
    if (this.kind === 'Progress') {
      // 生成普通进度条代码
      codeStr = `Progress({ value: ${this.value}, total: 100, type: ${this.type} })
        .color('${this.color}')
        .style(${this.style})
        .height(${this.height})`;
    } else {
      // 生成加载进度条代码
      codeStr = `LoadingProgress()
        .color('${this.color}')
        .size({ width: 64, height: 64 })`;
    }
    // 返回生成的完整进度条组件代码字符串
    return `// 进度条组件
// 提供普通进度条和加载进度条两种类型
@Component
struct ProgressComponent {
  // 构建组件UI方法
  build() {
    // 创建列布局容器
    Column() {
      // 插入进度条代码
      ${codeStr}
    }
    // 设置列布局内边距
    .padding($r('sys.float.padding_level16'))
    // 设置列布局垂直对齐方式为居中
    .justifyContent(FlexAlign.Center)
  }
}`;
  }
}
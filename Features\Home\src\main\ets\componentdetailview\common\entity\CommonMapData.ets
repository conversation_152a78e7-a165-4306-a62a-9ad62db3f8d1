/**
 * 通用颜色映射类
 * 用于存储颜色值的代码字符串和实际颜色值的映射关系
 */
export class CommonColorMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 颜色值字符串，只读属性
  public readonly value: string;

  /**
   * 构造函数
   * @param code 代码字符串，表示颜色的代码形式
   * @param value 颜色值字符串
   */
  constructor(code: string, value: string) {
    // 初始化代码字符串
    this.code = code;
    // 初始化颜色值
    this.value = value;
  }
}

// 导出通用字体颜色映射数据，包含默认的字体颜色配置
export const commonFontColorMap: Map<string, CommonColorMapping> = new Map([
  // 默认字体颜色，使用青色
  ['Default', new CommonColorMapping('rgba(0, 246, 255, 1.0)', 'rgba(0, 246, 255, 1.0)')],
]);

// 导出高亮颜色映射数据，包含默认的高亮颜色配置
export const highlightColorMap: Map<string, CommonColorMapping> = new Map([
  // 默认高亮颜色，使用蓝色
  ['Default', new CommonColorMapping('rgba(0, 85, 255, 1.0)', 'rgba(0, 85, 255, 1.0)')],
]);

/**
 * 通用字体粗细映射类
 * 用于存储字体粗细的代码字符串和实际枚举值的映射关系
 */
export class CommonFontWeightMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 字体粗细枚举值，只读属性
  public readonly value: FontWeight;

  /**
   * 构造函数
   * @param code 代码字符串，表示字体粗细的代码形式
   * @param value 字体粗细枚举值
   */
  constructor(code: string, value: FontWeight) {
    // 初始化代码字符串
    this.code = code;
    // 初始化字体粗细枚举值
    this.value = value;
  }
}

// 导出字体粗细映射数据，包含所有可用的字体粗细选项
export const fontWeightMapData: Map<string, CommonFontWeightMapping> = new Map([
  // 正常粗细
  ['Normal', new CommonFontWeightMapping('FontWeight.Normal', FontWeight.Normal)],
  // 较细
  ['Lighter', new CommonFontWeightMapping('FontWeight.Lighter', FontWeight.Lighter)],
  // 较粗
  ['Bolder', new CommonFontWeightMapping('FontWeight.Bolder', FontWeight.Bolder)],
  // 默认粗细，使用正常粗细
  ['Default', new CommonFontWeightMapping('FontWeight.Normal', FontWeight.Normal)],
]);

/**
 * 通用数字映射类
 * 用于存储数字值的代码字符串和实际数值的映射关系
 */
export class CommonNumberMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 数值，只读属性
  public readonly value: number;

  /**
   * 构造函数
   * @param code 代码字符串，表示数值的代码形式
   * @param value 实际数值
   */
  constructor(code: string, value: number) {
    // 初始化代码字符串
    this.code = code;
    // 初始化数值
    this.value = value;
  }
}

/**
 * 通用字符串映射类
 * 用于存储字符串值的代码字符串和实际字符串值的映射关系
 */
export class CommonStringMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 字符串值，只读属性
  public readonly value: string;

  /**
   * 构造函数
   * @param code 代码字符串，表示字符串的代码形式
   * @param value 实际字符串值
   */
  constructor(code: string, value: string) {
    // 初始化代码字符串
    this.code = code;
    // 初始化字符串值
    this.value = value;
  }
}

/**
 * 通用布尔映射类
 * 用于存储布尔值的代码字符串和实际布尔值的映射关系
 */
export class CommonBoolMapping {
  // 代码字符串，用于代码生成，只读属性
  public readonly code: string;
  // 布尔值，只读属性
  public readonly value: boolean;

  /**
   * 构造函数
   * @param code 代码字符串，表示布尔值的代码形式
   * @param value 实际布尔值
   */
  constructor(code: string, value: boolean) {
    // 初始化代码字符串
    this.code = code;
    // 初始化布尔值
    this.value = value;
  }
}
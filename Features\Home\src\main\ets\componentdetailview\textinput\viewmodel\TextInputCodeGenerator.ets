// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入高亮颜色映射数据，用于颜色属性处理
import { highlightColorMap } from '../../common/entity/CommonMapData';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入文本输入相关的属性映射数据
import { textInputFontMapData, textInputTypeMapData } from '../entity/TextInputAttributeMapping';

/**
 * 文本输入代码生成器类
 * 实现通用代码生成器接口，用于生成文本输入组件的代码
 */
export class TextInputCodeGenerator implements CommonCodeGenerator {
  // 私有类型字符串，默认使用默认值
  private typeStr: string = textInputTypeMapData.get('Default')!.code;
  // 私有字体颜色，默认使用默认值
  private fontColor: string = highlightColorMap.get('Default')!.code;
  // 私有占位符字体，默认使用默认值
  private placeholderFont: string = textInputFontMapData.get('Default')!.code;

  /**
   * 生成文本输入组件代码
   * @param attributes 原始属性数组，包含需要处理的属性
   * @returns 生成的文本输入组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 遍历所有属性进行处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的处理
      switch (attribute.name) {
        // 处理输入类型属性
        case 'type':
          // 从映射数据中获取对应的代码，如果未找到则使用默认值
          this.typeStr =
            textInputTypeMapData.get(attribute.currentValue)?.code ?? textInputTypeMapData.get('Default')!.code;
          break;
        case 'fontColor':
          this.fontColor = attribute.currentValue;
          break;
        case 'placeholderFont':
          this.placeholderFont =
            textInputFontMapData.get(attribute.currentValue)?.code ?? textInputFontMapData.get('Default')!.code;
          break;
        default:
          break;
      }
    });

    return `@Component
struct TextInputComponent {
  build() {
   Column() {
    TextInput({ text: '开发者你好', placeholder: '请输入' })
      .margin({ left: 36, right: 36 })
      .height(48)
      .enterKeyType(EnterKeyType.Done)
      .borderRadius($r('sys.float.corner_radius_level12'))
      .fontSize($r('sys.float.Body_L'))
      .fontWeight(FontWeight.Regular)
      .backgroundColor($r('sys.color.comp_background_tertiary'))
      .caretStyle({ color: $r('sys.color.font_emphasize') })
      .type(${this.typeStr})
      .fontColor('${this.fontColor}')
      .placeholderFont(${this.placeholderFont})\n
    }
  }
}`;
  }
}
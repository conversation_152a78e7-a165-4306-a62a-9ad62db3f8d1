// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入List属性映射数据和类型
import { edgeEffectMapData, LanesStyle, listDirectionMapData } from '../entity/ListAttributeMapping';

/**
 * List描述器类
 * 继承通用描述器，专门用于存储和管理List组件的属性配置
 * 使用@Observed装饰器实现响应式数据绑定
 */
@Observed
export class ListDescriptor extends CommonDescriptor {
  // List滚动方向，默认为垂直方向
  public listDirection: Axis = listDirectionMapData.get('Default')!.value;
  // List多列布局配置，默认为单列无间距
  public lanes: LanesStyle = {
    value: 1,
    gutter: 0,
  };
  // List边缘滚动效果，默认为弹簧效果
  public edgeEffect: EdgeEffect = edgeEffectMapData.get('Default')!.value;
  // 粘性头部状态，默认为true
  public sticky: boolean = true;

  /**
   * 转换属性方法
   * 将原始属性数组转换为List描述器的属性值
   * @param attributes 原始属性数组，包含所有List组件的配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理List滚动方向属性
        case 'listDirection':
          // 从映射数据中获取对应的枚举值，如果不存在则使用默认值
          this.listDirection =
            listDirectionMapData.get(attribute.currentValue)?.value ?? listDirectionMapData.get('Default')!.value;
          break;
        // 处理List列数属性
        case 'lanesNum':
          // 更新列数配置，保持原有的列间距
          this.lanes = {
            value: Number(attribute.currentValue),
            gutter: this.lanes.gutter,
          };
          break;
        // 处理List列间距属性
        case 'gutter':
          // 更新列间距配置，保持原有的列数
          this.lanes = {
            value: this.lanes.value,
            gutter: Number(attribute.currentValue),
          };
          break;
        // 处理List边缘效果属性
        case 'edgeEffect':
          // 从映射数据中获取对应的枚举值，如果不存在则使用默认值
          this.edgeEffect =
            edgeEffectMapData.get(attribute.currentValue)?.value ?? edgeEffectMapData.get('Default')!.value;
          break;
        // 处理粘性头部属性
        case 'sticky':
          // 解析布尔值字符串
          this.sticky = JSON.parse(attribute.currentValue);
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
  }
}
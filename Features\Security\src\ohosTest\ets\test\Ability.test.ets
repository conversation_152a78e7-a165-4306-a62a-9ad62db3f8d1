// 导入性能分析工具包中的日志模块
import { hilog } from '@kit.PerformanceAnalysisKit';
// 导入测试框架相关函数
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

// 导出默认能力测试函数
export default function abilityTest() {
  // 定义能力测试套件
  describe('ActsAbilityTest', () => {
    // 定义在所有测试用例开始前执行的预设操作
    beforeAll(() => {
      // 在测试套件的所有测试用例开始前只执行一次的预设操作
    })
    // 定义在每个单元测试用例开始前执行的预设操作
    beforeEach(() => {
      // 在每个单元测试用例开始前执行的预设操作
      // 执行次数与it定义的测试用例数量相同
    })
    // 定义在每个单元测试用例结束后执行的清理操作
    afterEach(() => {
      // 在每个单元测试用例结束后执行的清理操作
      // 执行次数与it定义的测试用例数量相同
    })
    // 定义在所有测试用例结束后执行的清理操作
    afterAll(() => {
      // 在测试套件的所有测试用例结束后执行的清理操作
    })
    // 定义测试用例
    it('assertContain', 0, () => {
      // 记录测试开始日志
      hilog.info(0x0000, 'testTag', '%{public}s', 'it begin');
      // 定义字符串变量a
      let a = 'abc';
      // 定义字符串变量b
      let b = 'b';
      // 断言a包含b
      expect(a).assertContain(b);
      // 断言a等于a
      expect(a).assertEqual(a);
    })
  })
}
/**
 * 组件详情参数接口
 * 用于传递组件详情页面所需的参数信息
 */
export interface ComponentDetailParams {
  // 组件名称
  componentName: string;
  // 组件唯一标识ID
  componentId: number;
}

/**
 * 示例详情参数接口
 * 用于传递示例详情页面所需的参数信息
 */
export interface SampleDetailParams {
  // 当前选中的索引
  currentIndex: number;
  // 示例卡片唯一标识ID
  sampleCardId: number;
}

/**
 * 文章详情参数接口
 * 用于传递文章详情页面所需的参数信息
 */
export interface ArticleDetailParams {
  // 文章唯一标识ID
  id: number;
  // 是否为文章类型的标识
  isArticle: boolean;
  // 文章标题
  title: string;
  // 详情页面URL地址
  detailsUrl: string;
  // 标签视图类型，可选参数
  tabViewType?: number;
}
// 导入原始属性类型，用于属性处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入字体粗细映射数据，用于字体粗细属性处理
import { fontWeightMapData } from '../../common/entity/CommonMapData';
// 导入文本相关的属性映射数据
import {
  fontColorMapData,
  fontSizeMapData,
  letterSpacingMapData,
  opacityMapData,
  textShadowRadiusMapData,
} from '../entity/TextAttributeMapping';

/**
 * 文本代码生成器类
 * 实现通用代码生成器接口，用于生成文本组件的代码
 */
export class TextCodeGenerator implements CommonCodeGenerator {
  // 私有字体粗细属性，默认使用默认值
  private fontWeight: string = fontWeightMapData.get('Default')!.code;
  // 私有字体大小属性，默认使用默认值
  private fontSize: string = fontSizeMapData.get('Default')!.code;
  // 私有字体颜色属性，默认使用默认值
  private fontColor: string = fontColorMapData.get('Default')!.code;
  private opacity: string = opacityMapData.get('Default')!.code;
  private letterSpacing: string = letterSpacingMapData.get('Default')!.code;
  private textShadowRadius: string = textShadowRadiusMapData.get('Default')!.code;

  public generate(attributes: OriginAttribute[]): string {
    const text = '开发者你好';
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'fontWeight':
          this.fontWeight =
            fontWeightMapData.get(attribute.currentValue)?.code ?? fontWeightMapData.get('Default')!.code;
          break;
        case 'fontColor':
          this.fontColor = attribute.currentValue;
          break;
        case 'fontSize':
          this.fontSize = attribute.currentValue;
          break;
        case 'opacity':
          this.opacity = Number(attribute.currentValue).toString();
          break;
        case 'letterSpacing':
          this.letterSpacing = attribute.currentValue;
          break;
        case 'textShadowRadius':
          this.textShadowRadius = attribute.currentValue;
          break;
        default:
          break;
      }
    });
    return `@Component
struct TextComponent {
  build() {
    Text('${text}')
      .fontSize(${this.fontSize})
      .fontColor('${this.fontColor}')
      .fontWeight(${this.fontWeight})
      .opacity(${this.opacity})
      .letterSpacing(${this.letterSpacing})
      .textShadow({ radius: ${this.textShadowRadius} })
  }
}`;
  }
}
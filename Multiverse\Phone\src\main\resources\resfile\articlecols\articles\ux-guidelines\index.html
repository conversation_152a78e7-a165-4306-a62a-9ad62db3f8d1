<!DOCTYPE html>
<html lang="zh-cn" xml:lang="zh-cn">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0" />
  <title>应用UX体验标准</title>
  <link rel="stylesheet" href="../../common/dist/main.css">
  <link rel="stylesheet" href="../../common/css/article.css">
</head>

<body>
  <div class="nested0" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002212713948"><a name="ZH-CN_TOPIC_0000002212713948"></a><a
      name="ZH-CN_TOPIC_0000002212713948"></a>
    <h1 class="topictitle1"><span class="topictitlenumber1">1</span> 应用UX体验标准</h1>
    <div class="topicbody" id="body0000002195706825"></div>
    <div>
      <ul class="ullinks">
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002247873837">1.1 应用UX体验标准</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002212713952">1.2 应用导航</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002247793769">1.3 页面布局</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002212873764">1.4 人机交互</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002212873760">1.5 视觉风格</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002247873841">1.6 动效</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002247793761">1.7 系统特性</a></strong><br>
        </li>
      </ul>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002247873837"><a
        name="ZH-CN_TOPIC_0000002247873837"></a><a name="ZH-CN_TOPIC_0000002247873837"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.1</span> 应用UX体验标准</h2>
      <div class="topicbody" id="body101mcpsimp">
        <p id="ZH-CN_TOPIC_0000002247873837__p103mcpsimp"><strong
            id="ZH-CN_TOPIC_0000002247873837__b144871317549">「应用UX体验标准」是什么？</strong></p>
        <p id="ZH-CN_TOPIC_0000002247873837__p104mcpsimp">优秀的应用体验来源于良好的UX设计。<a href="article_guildline_1"
            target="_blank"
            rel="noopener noreferrer">「HarmonyOS应用UX体验标准」</a>从影响用户体验的各个维度定义了应用需要达到的准则要求，您的应用需至少满足下述标准要求中的必选条目，才可获准在应用市场上架。请在应用的设计开发过程中仔细检查应用的各个方面是否满足这些要求。
        </p>
        <p class="msonormal" id="ZH-CN_TOPIC_0000002247873837__p105mcpsimp"><img
            id="ZH-CN_TOPIC_0000002247873837__image102" src="ManulImages/1.png"></p>
        <p class="msonormal" id="ZH-CN_TOPIC_0000002247873837__p108mcpsimp"><img
            id="ZH-CN_TOPIC_0000002247873837__image104" src="ManulImages/2.png"></p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002212713952"><a
        name="ZH-CN_TOPIC_0000002212713952"></a><a name="ZH-CN_TOPIC_0000002212713952"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.2</span> 应用导航</h2>
      <div class="topicbody" id="body0000002129600048">
        <p id="ZH-CN_TOPIC_0000002212713952__p1025335615546"><strong style="color:#00B0F0;"
            id="ZH-CN_TOPIC_0000002212713952__b1325465615414"><span style="color:#F79646;">系统返回</span></strong></p>
        <p id="ZH-CN_TOPIC_0000002212713952__ZH-CN_TOPIC_0000002212713952_mMcCpPsS_p8060118">
          所有界面都可以执行系统返回操作。除一级界面外，所有全屏界面均需要提供返回/关闭/取消按钮。(全屏沉浸式场景除外)</p>
        <p id="ZH-CN_TOPIC_0000002212713952__p10762193816296"><img id="ZH-CN_TOPIC_0000002212713952__image1776253882912"
            src="ManulImages/3.png"></p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002247793769"><a
        name="ZH-CN_TOPIC_0000002247793769"></a><a name="ZH-CN_TOPIC_0000002247793769"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.3</span> 页面布局</h2>
      <div class="topicbody" id="body0000002129441892">
        <p id="ZH-CN_TOPIC_0000002247793769__p122144155514"><strong style="color:#0000FF;"
            id="ZH-CN_TOPIC_0000002247793769__b62154195513"><span style="color:#F79646;">布局基础要求</span></strong></p>
        <p id="ZH-CN_TOPIC_0000002247793769__ZH-CN_TOPIC_0000002247793769_mMcCpPsS_p8060118">
          应用支持在不同屏幕尺寸的设备上良好显示，图片、视频等界面元素应避免出现错位/截断/变形等问题。</p>
        <p id="ZH-CN_TOPIC_0000002247793769__p17542616112010"><img id="ZH-CN_TOPIC_0000002247793769__image1542151662014"
            src="ManulImages/4.png"></p>
        <p id="ZH-CN_TOPIC_0000002247793769__p76037464562"><strong style="color:#0000FF;"
            id="ZH-CN_TOPIC_0000002247793769__b1372119193246"><span style="color:#F79646;">挖孔区适配</span></strong></p>
        <ul id="ZH-CN_TOPIC_0000002247793769__ul173151048185610">
          <li id="ZH-CN_TOPIC_0000002247793769__li13635650155618">
            界面布局需要适配摄像头的挖孔区域，若重要信息或交互操作（例如底部页签/顶部页签、工具栏、标题栏、搜索框、输入框、悬浮按钮、横幅通知等）和挖孔区之间有遮挡，则需要局部避开挖孔区显示。</li>
          <li id="ZH-CN_TOPIC_0000002247793769__li8803155120568">
            若重要信息或交互操作和挖孔区无遮挡，则无需避开挖孔区显示；悬浮类控件或功能（例如弹出框、侧边栏等），无需避开挖孔区显示；可以上下滚动的内容，例如列表、卡片等无需避开挖孔区显示。</li>
          <li id="ZH-CN_TOPIC_0000002247793769__li13131825195512">若应用支持横竖屏旋转，则横竖屏的界面布局均需满足以上挖孔适配要求。</li>
        </ul>
        <p id="ZH-CN_TOPIC_0000002247793769__p46514243217"><img id="ZH-CN_TOPIC_0000002247793769__image196517217325"
            src="ManulImages/5.png"></p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002212873764"><a
        name="ZH-CN_TOPIC_0000002212873764"></a><a name="ZH-CN_TOPIC_0000002212873764"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.4</span> 人机交互</h2>
      <div class="topicbody" id="body0000002164839861">
        <p id="ZH-CN_TOPIC_0000002212873764__p3431121589"><strong style="color:#0000FF;"
            id="ZH-CN_TOPIC_0000002212873764__b125619422246"><span style="color:#f79646;">避免与系统手势冲突</span></strong></p>
        <p id="ZH-CN_TOPIC_0000002212873764__p1824531585811">
          应用使用的手势应避免与系统手势冲突。不要设计可能跟系统手势冲突的手势操作，如屏幕边缘手势、指关节手势、三指及以上的手势。</p>
        <p id="ZH-CN_TOPIC_0000002212873764__p922813422328"><img id="ZH-CN_TOPIC_0000002212873764__image15228642113216"
            src="ManulImages/6.png"></p>
        <p id="ZH-CN_TOPIC_0000002212873764__p1253914344251"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002212873764__b06021644182514">典型手势时长设计</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873764__p15715562585">应用使用的典型手势设计需满足相关参数约束：</p>
        <ul id="ZH-CN_TOPIC_0000002212873764__ul6136182613562">
          <li id="ZH-CN_TOPIC_0000002212873764__li12136182665619">长按手势的接触时长应为500ms，时长在400ms-650ms内可调。</li>
          <li id="ZH-CN_TOPIC_0000002212873764__li1964711284567">双击手势的间隔时长应为70-400ms之间。</li>
        </ul>
        <p id="ZH-CN_TOPIC_0000002212873764__p041820520545"><img id="ZH-CN_TOPIC_0000002212873764__image114181252155411"
            src="ManulImages/7.png"></p>
        <p style="color:#F79646;" id="ZH-CN_TOPIC_0000002212873764__p19492450135818"><strong
            id="ZH-CN_TOPIC_0000002212873764__b199306518259">点击热区</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873764__p068225020583">
          点击热区需满足最小尺寸要求：主要交互元素或控件的可点击热区至少为48vp×48vp(推荐)，不得小于40vp×40vp(必须)。</p>
        <p id="ZH-CN_TOPIC_0000002212873764__p182191840193114"><img
            id="ZH-CN_TOPIC_0000002212873764__image13219740133115" src="ManulImages/8.png"></p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002212873760"><a
        name="ZH-CN_TOPIC_0000002212873760"></a><a name="ZH-CN_TOPIC_0000002212873760"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.5</span> 视觉风格</h2>
      <div class="topicbody" id="body0000002164721433">
        <p id="ZH-CN_TOPIC_0000002212873760__p48994554253"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002212873760__b1896910022614">色彩对比度</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p9991437786">应用使用的色彩需满足最小对比度要求：</p>
        <ul id="ZH-CN_TOPIC_0000002212873760__ul614810417818">
          <li id="ZH-CN_TOPIC_0000002212873760__li21481417811">图标或标题文字与背景对比度大于3:1。</li>
          <li id="ZH-CN_TOPIC_0000002212873760__li16216433816">正文文字与背景对比度大于4.5:1。</li>
        </ul>
        <p id="ZH-CN_TOPIC_0000002212873760__p381917191317"><img id="ZH-CN_TOPIC_0000002212873760__image158199195319"
            src="ManulImages/9.png"></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p137151348131016"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002212873760__b8297054269">字体大小</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p15994149131016">应用的文字大小需满足最小字号要求：文本字号不小于12fp(推荐)，最小不得小于8fp(必须)。</p>
        <p id="ZH-CN_TOPIC_0000002212873760__p052513520317"><img id="ZH-CN_TOPIC_0000002212873760__image1752511503119"
            src="ManulImages/10.png"></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p85561714111217"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002212873760__b72421287266">应用图标</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p14955486124">
          应用图标需分层，尺寸需满足规范要求：应用图标需分为前景图和背景图两层，尺寸要求为288px*288px，图标可见区域尺寸默认为192px*192px。</p>
        <p id="ZH-CN_TOPIC_0000002212873760__p493517287410"><img id="ZH-CN_TOPIC_0000002212873760__image119352028441"
            src="ManulImages/11.png"></p>
        <p style="color:#00b0f0;" id="ZH-CN_TOPIC_0000002212873760__p3690115713124"><strong
            id="ZH-CN_TOPIC_0000002212873760__b178744012146"><span style="color:#F79646;">界面图标</span></strong></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p141322596124">应用的界面图标大小需满足最小尺寸要求：图标大小不小于12vp(推荐)，最小不得小于8vp(必须)。</p>
        <p id="ZH-CN_TOPIC_0000002212873760__p5289164463017"><img id="ZH-CN_TOPIC_0000002212873760__image1128916442300"
            src="ManulImages/12.png"></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p1678016247264"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002212873760__b85639372269">图标清晰度</strong></p>
        <p id="ZH-CN_TOPIC_0000002212873760__p165116278143">图标需保证清晰可辩，无明显模糊、拉伸、压缩、锯齿等情况。</p>
        <p id="ZH-CN_TOPIC_0000002212873760__p52605133303"><img id="ZH-CN_TOPIC_0000002212873760__image7260171312303"
            src="ManulImages/13.png"></p>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002247793761"><a
        name="ZH-CN_TOPIC_0000002247793761"></a><a name="ZH-CN_TOPIC_0000002247793761"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.7</span> 系统特性</h2>
      <div class="topicbody" id="body0000002129441896">
        <p style="color:#F79646;" id="ZH-CN_TOPIC_0000002247793761__p6484915142112"><strong
            id="ZH-CN_TOPIC_0000002247793761__b111131724142713">底部导航条适配</strong></p>
        <p id="ZH-CN_TOPIC_0000002247793761__p666017192217">手机、折叠屏、平板等设备屏幕底部有导航条，应用需对底部导航条进行适配：</p>
        <ol id="ZH-CN_TOPIC_0000002247793761__ol119961815936">
          <li id="ZH-CN_TOPIC_0000002247793761__li69961115936">
            应用内的底部固定控件、输入键盘、应用底部的悬浮按钮等均需要进行向上抬高，避免和导航条互相遮挡，也要避免导航条底部背景色与应用内底部背景色不融合，需要为导航条提供沉浸的背景效果。</li>
          <li id="ZH-CN_TOPIC_0000002247793761__li31122191031">应用内的可滚动内容，需要能显示在导航条下方。当滚动到最底部时，要避免导航条遮挡导致最底部功能不可用。</li>
          <li id="ZH-CN_TOPIC_0000002247793761__li11804162411313">应用内的弹出框、半模态等控件，需要向上避让导航条，避免交互误触。</li>
          <li id="ZH-CN_TOPIC_0000002247793761__li60192816316">沉浸式场景，例如游戏、全屏播放视频，导航条可自动隐藏，支持从底部上滑恢复显示导航条。</li>
        </ol>
        <p id="ZH-CN_TOPIC_0000002247793761__p13315193712912"><img id="ZH-CN_TOPIC_0000002247793761__image73151373296"
            src="ManulImages/23.png"></p>
        <p id="ZH-CN_TOPIC_0000002247793761__p444417466212"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002247793761__b5297227172716">多窗口交互</strong></p>
        <ul id="ZH-CN_TOPIC_0000002247793761__ul123167581319">
          <li id="ZH-CN_TOPIC_0000002247793761__li7316458039">应用支持以悬浮窗模式运行，支持悬浮窗等比缩放调节；游戏、视频播放等沉浸式场景适配支持横向悬浮窗。</li>
          <li id="ZH-CN_TOPIC_0000002247793761__li10849180542">应用支持上下分屏和左右分屏。</li>
        </ul>
        <p id="ZH-CN_TOPIC_0000002247793761__p9976175417217">分屏运行时，需确保应用布局显示良好，无元素显示异常问题。</p>
        <p id="ZH-CN_TOPIC_0000002247793761__p55016452216">应用支持分屏比例调节，比例调节时需确保元素无变形挤压的情况出现。</p>
        <p id="ZH-CN_TOPIC_0000002247793761__p206161727145519"><img
            id="ZH-CN_TOPIC_0000002247793761__image146161327115519" src="ManulImages/24.png"></p>
        <p id="ZH-CN_TOPIC_0000002247793761__p862618225716"><strong style="color:#F79646;"
            id="ZH-CN_TOPIC_0000002247793761__b2625194218270">深色模式</strong></p>
        <p id="ZH-CN_TOPIC_0000002247793761__p980433762717">应用需支持深色模式显示，确保系统切换到深色模式后，界面以深色风格呈现，并且界面内没有因未适配导致的识别性问题。</p>
        <p id="ZH-CN_TOPIC_0000002247793761__p6732122942810"><img id="ZH-CN_TOPIC_0000002247793761__image473272914282"
            src="ManulImages/25.png"></p>
      </div>
    </div>
  </div>
</body>
<script type="module"  src="../../common/js/changehref.js"></script>
<script type="module" src="../../common/dist/main.js"></script>

</html>
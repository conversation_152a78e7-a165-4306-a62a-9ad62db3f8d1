/**
 * 元素数量枚举
 * 定义Flex容器中可显示的子元素数量选项
 */
export enum ElementsNums {
  // 显示2个子元素
  TWO = '2',
  // 显示4个子元素
  FOUR = '4',
}

// Flex值类型联合类型，包含所有可能的Flex属性值类型
type FlexValueType = FlexDirection | FlexWrap | FlexAlign | ItemAlign | ElementsNums;

/**
 * Flex字典映射类
 * 用于存储Flex属性的代码字符串和对应的枚举值
 */
class FlexDictionaryMapping {
  // 只读的代码字符串，用于代码生成
  public readonly code: string;
  // 只读的枚举值，用于实际的属性设置
  public readonly value: FlexValueType;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码时的字符串表示
   * @param value 枚举值，用于实际设置组件属性
   */
  constructor(code: string, value: FlexValueType) {
    // 设置代码字符串
    this.code = code;
    // 设置枚举值
    this.value = value;
  }
}

// 导出Flex方向映射数据，用于将字符串键映射到Flex方向枚举和代码字符串
export const flexDirectionMapData: Map<string, FlexDictionaryMapping> = new Map([
  // 默认方向：水平从左到右
  ['Default', new FlexDictionaryMapping('FlexDirection.Row', FlexDirection.Row)],
  // 水平从左到右
  ['Row', new FlexDictionaryMapping('FlexDirection.Row', FlexDirection.Row)],
  // 水平从右到左
  ['RowReverse', new FlexDictionaryMapping('FlexDirection.RowReverse', FlexDirection.RowReverse)],
  // 垂直从上到下
  ['Column', new FlexDictionaryMapping('FlexDirection.Column', FlexDirection.Column)],
  // 垂直从下到上
  ['ColumnReverse', new FlexDictionaryMapping('FlexDirection.ColumnReverse', FlexDirection.ColumnReverse)],
]);

// 导出Flex换行映射数据，用于将字符串键映射到Flex换行枚举和代码字符串
export const flexWrapMapData: Map<string, FlexDictionaryMapping> = new Map([
  // 默认不换行
  ['Default', new FlexDictionaryMapping('FlexWrap.NoWrap', FlexWrap.NoWrap)],
  // 不换行
  ['NoWrap', new FlexDictionaryMapping('FlexWrap.NoWrap', FlexWrap.NoWrap)],
  // 换行
  ['Wrap', new FlexDictionaryMapping('FlexWrap.Wrap', FlexWrap.Wrap)],
  // 反向换行
  ['WrapReverse', new FlexDictionaryMapping('FlexWrap.WrapReverse', FlexWrap.WrapReverse)],
]);

// 导出Flex内容对齐映射数据，用于将字符串键映射到Flex对齐枚举和代码字符串
export const flexContentMapData: Map<string, FlexDictionaryMapping> = new Map([
  // 默认起始对齐
  ['Default', new FlexDictionaryMapping('FlexAlign.Start', FlexAlign.Start)],
  // 起始对齐
  ['Start', new FlexDictionaryMapping('FlexAlign.Start', FlexAlign.Start)],
  // 居中对齐
  ['Center', new FlexDictionaryMapping('FlexAlign.Center', FlexAlign.Center)],
  // 末尾对齐
  ['End', new FlexDictionaryMapping('FlexAlign.End', FlexAlign.End)],
  // 两端对齐，元素间等距
  ['SpaceBetween', new FlexDictionaryMapping('FlexAlign.SpaceBetween', FlexAlign.SpaceBetween)],
  // 环绕对齐，元素周围等距
  ['SpaceAround', new FlexDictionaryMapping('FlexAlign.SpaceAround', FlexAlign.SpaceAround)],
  // 均匀对齐，所有间距相等
  ['SpaceEvenly', new FlexDictionaryMapping('FlexAlign.SpaceEvenly', FlexAlign.SpaceEvenly)],
]);

// 导出Flex项目对齐映射数据，用于将字符串键映射到项目对齐枚举和代码字符串
export const flexAlignItemMapData: Map<string, FlexDictionaryMapping> = new Map([
  // 默认自动对齐
  ['Default', new FlexDictionaryMapping('ItemAlign.Auto', ItemAlign.Auto)],
  // 自动对齐
  ['Auto', new FlexDictionaryMapping('ItemAlign.Auto', ItemAlign.Auto)],
  // 起始对齐
  ['Start', new FlexDictionaryMapping('ItemAlign.Start', ItemAlign.Start)],
  // 居中对齐
  ['Center', new FlexDictionaryMapping('ItemAlign.Center', ItemAlign.Center)],
  // 末尾对齐
  ['End', new FlexDictionaryMapping('ItemAlign.End', ItemAlign.End)],
  // 拉伸对齐
  ['Stretch', new FlexDictionaryMapping('ItemAlign.Stretch', ItemAlign.Stretch)],
  // 基线对齐
  ['Baseline', new FlexDictionaryMapping('ItemAlign.Baseline', ItemAlign.Baseline)],
]);

// 导出元素数量映射数据，用于将字符串键映射到元素数量枚举和代码字符串
export const elementsNumsMapData: Map<string, FlexDictionaryMapping> = new Map([
  // 默认显示2个元素
  ['Default', new FlexDictionaryMapping('2', ElementsNums.TWO)],
]);
// 导入文本测量工具
import { MeasureText } from '@kit.ArkUI';
// 导入通用常量
import { CommonConstants } from '../constant/CommonConstants';
// 导入断点类型枚举和全局信息模型
import { BreakpointTypeEnum, type GlobalInfoModel } from '../model/GlobalInfoModel';
// 导入断点系统工具
import { BreakpointType } from '../util/BreakpointSystem';

// 返回按钮图标宽度常量
const BACK_ICON_WIDTH: number = 40;
// 总内边距常量
const TOTAL_PADDING: number = 40;

/**
 * 顶部导航视图组件
 * 提供标准的顶部导航栏功能，包含返回按钮、标题和菜单区域
 * 支持响应式布局和模糊背景效果
 */
@Component
export struct TopNavigationView {
  // 全局信息模型，监听变化时重新计算标题尺寸
  @StorageProp('GlobalInfoModel') @Watch('calculateTitleSize') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 模糊渲染组标识
  @StorageProp('BlurRenderGroup') blurRenderGroup: boolean = false;
  // 顶部导航数据属性
  @Prop topNavigationData: TopNavigationData = new TopNavigationData();
  // 菜单视图构建器参数
  @BuilderParam menuView?: () => void;
  // 字体大小状态
  @State fontSize: number = 20;
  // 返回按钮背景颜色状态，根据断点类型设置不同颜色
  @State backIconBgColor: ResourceColor =
    this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? Color.Transparent :
    $r('sys.color.comp_background_tertiary');

  /**
   * 组件即将出现时的生命周期方法
   * 如果导航数据中未设置字体大小，则计算合适的标题尺寸
   */
  aboutToAppear(): void {
    // 检查是否需要计算标题尺寸
    if (this.topNavigationData.fontSize === undefined) {
      // 计算合适的标题尺寸
      this.calculateTitleSize();
    }
  }

  /**
   * 计算标题文字尺寸方法
   * 根据设备宽度和标题内容自动调整字体大小，确保标题能够完整显示
   */
  calculateTitleSize(): void {
    // 计算标题可用的最大宽度
    const maxWidth = this.globalInfoModel.deviceWidth - BACK_ICON_WIDTH - TOTAL_PADDING;
    // 当前字体大小
    let currentFontSize: number = this.fontSize;
    // 测量当前字体大小下标题的宽度
    let titleWidth: number = Math.ceil(px2vp(MeasureText.measureText({
      textContent: this.topNavigationData.title,
      fontWeight: FontWeight.Bold,
      fontSize: currentFontSize,
    })));
    // 如果标题宽度超出可用宽度且字体大小大于14，则减小字体
    while (currentFontSize > 14 && titleWidth > maxWidth) {
      // 减小字体大小
      currentFontSize--;
      // 重新测量标题宽度
      titleWidth = Math.ceil(px2vp(MeasureText.measureText({
        textContent: this.topNavigationData.title,
        fontWeight: FontWeight.Bold,
        fontSize: currentFontSize,
      })));
    }
    // 更新字体大小状态
    this.fontSize = currentFontSize;
  }

  /**
   * 构建导航视图的UI结构
   * 包含返回按钮、标题文字和菜单区域的完整导航栏布局
   */
  build() {
    // 创建垂直布局的列容器
    Column() {
      // 创建水平布局的行容器，用于放置导航栏元素
      Row() {
        // 如果设置了返回点击事件，则显示返回按钮
        if (this.topNavigationData.onBackClick) {
          // 创建圆形返回按钮
          Button({ type: ButtonType.Circle }) {
            // 创建向左箭头符号图标
            SymbolGlyph($r('sys.symbol.chevron_backward'))
              // 设置图标颜色为系统主要图标颜色
              .fontColor([$r('sys.color.icon_primary')])
              // 设置图标大小为系统预定义的中等标题字号
              .fontSize($r('sys.float.Title_M'))
          }
          // 设置按钮高度为应用预定义的返回按钮高度
          .height($r('app.float.back_button_height'))
          // 设置按钮宽高比为1:1，保持圆形
          .aspectRatio(1)
          // 设置按钮右边距，与标题保持间距
          .margin({ right: $r('sys.float.padding_level4') })
          // 设置按钮背景颜色
          .backgroundColor(this.backIconBgColor)
          // 设置按钮点击事件处理
          .onClick(() => this.topNavigationData.onBackClick?.())
          // 设置按钮悬停事件处理
          .onHover((isHover: boolean) => {
            // 悬停时改变背景颜色，提供视觉反馈
            this.backIconBgColor = isHover ? $r('sys.color.comp_background_tertiary') : Color.Transparent;
          })
        }

        // 创建标题文字组件
        Text(this.topNavigationData.title)
          // 设置字体大小，优先使用导航数据中的字体大小，否则使用计算得出的字体大小
          .fontSize(this.topNavigationData.fontSize ? this.topNavigationData.fontSize : this.fontSize)
          // 设置文字颜色
          .fontColor(this.topNavigationData.titleColor)
          // 设置文字粗细为粗体
          .fontWeight(FontWeight.Bold)
          // 设置文字对齐方式为左对齐
          .textAlign(TextAlign.Start)
          // 设置最大显示行数为1行
          .maxLines(1)
          // 设置文字溢出处理方式为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置布局权重为1，占据剩余空间
          .layoutWeight(1)

        // 创建菜单区域的行容器
        Row() {
          // 调用菜单视图构建器
          this.menuView?.()
        }
      }
      // 设置行容器子元素在垂直方向居中对齐
      .alignItems(VerticalAlign.Center)
      // 设置行容器子元素在主轴方向两端对齐
      .justifyContent(FlexAlign.SpaceBetween)
      // 设置行容器高度为通用常量定义的导航栏高度
      .height(CommonConstants.NAVIGATION_HEIGHT)
      // 根据不同断点设置不同的内边距
      .padding(new BreakpointType<Padding>({
        // 小屏幕断点的内边距配置
        sm: {
          left: $r('sys.float.padding_level8'),
          right: $r('sys.float.padding_level8'),
        },
        // 中等屏幕断点的内边距配置
        md: {
          left: $r('sys.float.padding_level12'),
          right: $r('sys.float.padding_level12'),
        },
        // 大屏幕断点的内边距配置
        lg: {
          left: $r('sys.float.padding_level16'),
          right: $r('sys.float.padding_level16'),
        },
      }).getValue(this.globalInfoModel.currentBreakpoint))

      // 创建分割线组件
      Divider()
        // 设置分割线颜色为系统组件分割线颜色
        .color($r('sys.color.comp_divider'))
        // 根据是否启用模糊效果控制分割线的可见性
        .visibility(this.topNavigationData.isBlur ? Visibility.Visible : Visibility.Hidden)
    }
    // 根据是否启用模糊效果设置背景模糊样式
    .backgroundBlurStyle(this.topNavigationData.isBlur ? BlurStyle.COMPONENT_THICK : undefined)
    // 设置渲染组，在特定条件下启用渲染优化
    .renderGroup(!this.blurRenderGroup && this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL)
    // 设置顶部内边距，全屏模式下考虑状态栏高度
    .padding({ top: this.topNavigationData.isFullScreen ? this.globalInfoModel.statusBarHeight : 0 })
    // 设置列容器宽度为100%，占满父容器
    .width('100%')
    // 设置列容器背景颜色
    .backgroundColor(this.topNavigationData.bgColor)
  }
}

/**
 * 顶部导航数据类
 * 用于配置顶部导航视图的各种属性和行为
 * 包含标题、样式、背景效果等配置选项
 */
@Observed
export class TopNavigationData {
  // 导航栏标题文字
  public title: ResourceStr = '';
  // 标题字体大小，可选属性
  public fontSize?: ResourceStr;
  // 是否为全屏模式，默认为true
  public isFullScreen?: boolean = true;
  // 标题文字颜色，默认为系统主要字体颜色
  public titleColor?: ResourceStr = $r('sys.color.font_primary');
  // 是否启用模糊背景效果，默认为false
  public isBlur?: boolean = false;
  // 导航栏背景颜色，默认为透明
  public bgColor?: ResourceStr | Color = Color.Transparent;
  // 返回按钮点击事件处理函数，可选
  public onBackClick?: Function;
}
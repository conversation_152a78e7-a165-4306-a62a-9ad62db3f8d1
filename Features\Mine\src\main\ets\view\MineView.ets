// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入断点类型、断点类型枚举、通用常量和顶部导航视图
import { BreakpointType, BreakpointTypeEnum, CommonConstants, TopNavigationView } from '@ohos/common';
// 导入关于页面绑定弹窗事件和我的页面视图模型
import { AboutBindSheetEvent, MinePageVM } from '../viewmodel/MinePageVM';
// 导入我的页面状态
import { MinePageState } from '../viewmodel/MinePageState';
// 导入卡片项目组件
import { CardItem } from '../component/CardItem';

// 使用Component装饰器定义我的页面视图组件
@Component
export struct MineView {
  // 创建视图模型实例
  viewModel: MinePageVM = MinePageVM.getInstance();
  // 使用State装饰器定义我的页面状态
  @State minePageState: MinePageState = this.viewModel.getState();
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

  // 定义节流函数
  throttle(func: Function, interval: number) {
    // 记录上次执行时间
    let lastTime = 0;
    // 返回节流后的函数
    return () => {
      // 获取当前时间
      const nowTime = Date.now();
      // 计算剩余时间
      const remainTime = interval - (nowTime - lastTime);
      // 如果剩余时间小于等于0则执行函数
      if (remainTime <= 0) {
        // 更新上次执行时间
        lastTime = nowTime;
        // 执行函数
        func();
      }
    };
  }

  // 定义构建方法
  build() {
    // 创建主列布局
    Column() {
      // 创建顶部导航视图
      TopNavigationView({ topNavigationData: {
        // 根据断点类型设置字体大小
        fontSize:new BreakpointType({
          sm: $r('sys.float.Title_M'),
          md: $r('sys.float.Title_L'),
          lg: $r('sys.float.Title_L'),
          xl: $r('sys.float.Title_S'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        // 设置背景颜色
        bgColor:$r('sys.color.background_secondary'),
        // 设置标题
        title: $r('app.string.mine_title'),
        // 设置全屏模式
        isFullScreen: true,
      }});

      // 创建列表
      List() {
        // 创建列表项组
        ListItemGroup() {
          // 创建列表项
          ListItem({ style: ListItemStyle.CARD }) {
            // 创建卡片项目
            CardItem({
              // 设置是否显示
              isShow: this.minePageState.aboutViewShow,
              // 设置文本内容
              textContent: $r('app.string.about'),
              // 设置图标资源
              symbolSrc: $r('sys.symbol.info_circle'),
              // 设置点击回调
              onclick: () => {
                // 发送关于页面绑定弹窗事件（显示）
                this.viewModel.sendEvent(new AboutBindSheetEvent(true));
              },
              // 设置关闭回调
              onClose: () => {
                // 发送关于页面绑定弹窗事件（隐藏）
                this.viewModel.sendEvent(new AboutBindSheetEvent(false));
              },
            })
          }
          // 设置列表项高度为自动
          .height(undefined)
          // 设置列表项宽度为100%
          .width(CommonConstants.FULL_PERCENT)
        }
        // 设置分割线样式
        .divider({
          strokeWidth: $r('app.float.mine_divider'),
          color: $r('sys.color.comp_divider'),
          startMargin: $r('sys.float.padding_level24'),
          endMargin: $r('sys.float.padding_level6'),
        })
        // 设置列表项组外边距
        .margin({ top: $r('sys.float.padding_level6') })
        // 设置列表项组内边距
        .padding($r('sys.float.padding_level2'))
        // 设置列表项组背景颜色
        .backgroundColor($r('sys.color.comp_background_primary'))
        // 设置列表项组边框圆角
        .borderRadius($r('sys.float.corner_radius_level8'))
      }
      // 设置列表边缘效果为弹簧效果
      .edgeEffect(EdgeEffect.Spring, { alwaysEnabled: true })
      // 设置列表宽度为100%
      .width(CommonConstants.FULL_PERCENT)
      // 设置列表高度为100%
      .height(CommonConstants.FULL_PERCENT)
      // 根据断点类型设置列表内边距
      .padding(new BreakpointType<Padding>({
        sm: {
          left: $r('sys.float.padding_level8'),
          right: $r('sys.float.padding_level8')
        },
        md: {
          left: $r('sys.float.padding_level12'),
          right: $r('sys.float.padding_level12')
        },
        lg: {
          left: $r('sys.float.padding_level16'),
          right: $r('sys.float.padding_level16')
        },
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 设置列表背景颜色
      .backgroundColor($r('sys.color.background_secondary'))
    }
    // 设置主列宽度为100%
    .width(CommonConstants.FULL_PERCENT)
    // 设置主列高度为100%
    .height(CommonConstants.FULL_PERCENT)
    // 根据断点类型设置主列内边距
    .padding({
      left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? CommonConstants.TAB_BAR_WIDTH : 0,
    })
  }
}
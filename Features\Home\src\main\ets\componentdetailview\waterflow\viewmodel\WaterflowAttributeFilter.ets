// 导入观察数组类型，用于响应式数据处理
import type { ObservedArray } from '@ohos/common';
// 导入属性类型，用于属性过滤处理
import type { Attribute } from '../../../viewmodel/Attribute';
// 导入通用属性过滤器接口，用于实现属性过滤功能
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

/**
 * 瀑布流属性过滤器类
 * 实现通用属性过滤器接口，用于过滤瀑布流组件的属性
 * 根据布局方向动态启用或禁用相关属性
 */
export class WaterFlowAttributeFilter implements CommonAttributeFilter {
  /**
   * 过滤属性方法
   * 根据属性值动态调整其他属性的启用状态
   * @param attributes 观察数组属性，包含需要过滤的属性
   */
  filter(attributes: ObservedArray<Attribute>): void {
    // 遍历所有属性进行过滤处理
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的过滤处理
      switch (attribute.name) {
        // 处理布局方向属性
        case 'layoutDirection':
          // 查找行模板属性的索引
          const rowsTemplateIndex = attributes.findIndex((item) => item.name === 'rowsTemplate');
          // 查找列模板属性的索引
          const columnsTemplateIndex = attributes.findIndex((item) => item.name === 'columnsTemplate');
          // 如果找到了行模板和列模板属性
          if (rowsTemplateIndex !== -1 && columnsTemplateIndex !== -1) {
            // 如果布局方向是列或列反向
            if (attribute.currentValue === 'Column' || attribute.currentValue === 'ColumnReverse') {
              // 禁用行模板属性
              attributes[rowsTemplateIndex].enable = false;
              // 启用列模板属性
              attributes[columnsTemplateIndex].enable = true;
            } else if (attribute.currentValue === 'Row' || attribute.currentValue === 'RowReverse') {
              attributes[rowsTemplateIndex].enable = true;
              attributes[columnsTemplateIndex].enable = false;
            }
          }
          break;
        default:
          break;
      }
    });
  }
}
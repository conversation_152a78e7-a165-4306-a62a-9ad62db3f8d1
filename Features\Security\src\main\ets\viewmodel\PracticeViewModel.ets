// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型和响应数据类型
import type { GlobalInfoModel, ResponseData } from '@ohos/common';
// 导入通用模块中的相关类和工具
import {
  BreakpointTypeEnum,
  LoadingModel,
  LoadingStatus,
  Logger,
  PageContext,
  RequestErrorCode,
  StatusBarColorType,
  WindowUtil,
} from '@ohos/common';
// 导入通用业务模块中的横幅数据类型
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的相关类和类型
import {
  BaseHomeEventParam,
  BaseHomeEventType,
  BaseHomeViewModel,
  SampleDetailParams,
  TAB_CONTENT_STATUSES,
  TabBarType,
} from '@ohos/commonbusiness';
// 导入示例数据相关类型
import type { SampleCardData, SampleCategory, SampleData } from '../model/SampleData';
// 导入示例模型
import { SampleModel } from '../model/SampleModel';
// 导入实践状态
import { PracticeState } from './PracticeState';

// 定义日志标签常量
const TAG = '[PracticeViewModel]';

// 导出实践视图模型类
export class PracticeViewModel extends BaseHomeViewModel<PracticeState> {
  // 定义静态实例私有变量
  private static instance: PracticeViewModel;
  // 定义示例模型私有变量
  private sampleModel: SampleModel = SampleModel.getInstance();

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数
    super(new PracticeState());
    // 设置顶部导航数据标题
    this.state.topNavigationData.title = $r('app.string.security_name');
  }

  // 定义获取实例的静态方法
  static getInstance(): PracticeViewModel {
    // 如果实例不存在
    if (!PracticeViewModel.instance) {
      // 创建新实例
      PracticeViewModel.instance = new PracticeViewModel();
    }
    // 返回实例
    return PracticeViewModel.instance;
  }

  // 定义发送事件的方法
  sendEvent<T>(eventParam: PracticeEventParam<T>): void | boolean {
    // 获取事件类型
    const eventType: PracticeEventType | BaseHomeEventType = eventParam.type;
    // 如果是加载示例页面事件
    if (eventType === PracticeEventType.LOAD_SAMPLE_PAGE) {
      // 调用加载示例页面方法
      return this.loadSamplePage(eventParam.param as LoadSamplePageParam);
    } else if (eventType === PracticeEventType.LOAD_SAMPLE_LIST) {
      // 调用加载示例列表方法
      return this.loadSampleList(eventParam.param as SampleCategory);
    } else if (eventType === PracticeEventType.JUMP_DETAIL_DETAIL) {
      // 调用跳转详情视图方法
      return this.jumpDetailView(eventParam.param as SampleDetailParams);
    } else {
      // 调用父类发送事件方法
      return super.sendEvent(eventParam as BaseHomeEventParam<T>);
    }
  }

  // 定义加载示例页面的受保护方法
  protected loadSamplePage(param: LoadSamplePageParam): void {
    // 判断是否为深色模式
    const isDark: boolean = AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    // 设置加载状态为加载中
    this.state.loadingModel.loadingStatus = LoadingStatus.LOADING;
    // 根据深色模式设置标题颜色
    this.state.topNavigationData.titleColor = isDark ? StatusBarColorType.WHITE : StatusBarColorType.BLACK;
    // 设置顶部导航数据不模糊
    this.state.topNavigationData.isBlur = false;
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(), isDark);
    // 获取示例页面数据
    this.sampleModel.getSamplePage(1, this.pageSize)
      .then((result: ResponseData<SampleData>) => {
        // 如果有横幅信息
        if (result.data.bannerInfos) {
          // 遍历横幅信息
          result.data.bannerInfos.forEach((item: BannerData) => {
            // 设置标签视图类型
            item.tabViewType = TabBarType.SAMPLE;
          });
          // 设置横幅状态数据数组
          this.state.bannerState.bannerResource.setDataArray([...result.data.bannerInfos]);
        }
        // 创建分类列表
        const categoryList: SampleCategory[] = [];
        // 遍历示例分类
        result.data.sampleCategories.forEach((sampleCategory: SampleCategory) => {
          // 设置当前页码
          sampleCategory.currentPage = 1;
          // 创建加载模型
          sampleCategory.loadingModel = new LoadingModel();
          // 根据示例卡片数量设置加载状态
          sampleCategory.loadingModel.loadingStatus =
            sampleCategory.sampleCards?.length === 0 ? LoadingStatus.OFF : LoadingStatus.SUCCESS;
          // 设置没有下一页
          sampleCategory.loadingModel.hasNextPage = false;
          // 添加到分类列表
          categoryList.push(sampleCategory);
        });
        // 设置状态中的示例分类
        this.state.sampleCategories = categoryList;
        // 获取全局信息模型
        const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
        // 如果不是大屏或超大屏断点
        if (globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.LG &&
          globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL) {
          // 设置标题颜色为白色
          this.state.topNavigationData.titleColor = StatusBarColorType.WHITE;
          // 更新状态栏颜色为深色
          WindowUtil.updateStatusBarColor(getContext(), true);
          // 设置标签内容状态
          TAB_CONTENT_STATUSES[TabBarType.SAMPLE] = true;
        }
        // 设置加载状态为成功
        this.state.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
        // 调用回调函数
        param.callback();
      })
      .catch((error: BusinessError) => {
        // 记录错误日志
        Logger.error(TAG, `load PracticePage Failed, ${error.code} ${error.message}`);
        // 更新状态栏颜色
        WindowUtil.updateStatusBarColor(getContext(), isDark);
        // 设置标签内容状态
        TAB_CONTENT_STATUSES[TabBarType.SAMPLE] = isDark;
        // 如果是网络连接失败错误
        if (error.code === RequestErrorCode.ERROR_NETWORK_CONNECT_FAILED) {
          // 设置加载状态为无网络
          this.state.loadingModel.loadingStatus = LoadingStatus.NO_NETWORK;
        } else {
          // 设置加载状态为失败
          this.state.loadingModel.loadingStatus = LoadingStatus.FAILED;
        }
      });
  }

  // 定义加载示例列表的受保护方法
  protected loadSampleList(currentCategory: SampleCategory): void {
    // 创建加载中状态的加载模型
    currentCategory.loadingModel = new LoadingModel(LoadingStatus.LOADING);
    // 更新示例分类
    this.changeSampleCategories(currentCategory);
    // 获取示例列表数据
    this.sampleModel.getSampleList(currentCategory.categoryType, currentCategory.currentPage, this.pageSize)
      .then((result: ResponseData<SampleCardData[]>) => {
        // 设置加载状态为成功
        currentCategory.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
        // 判断是否有下一页
        currentCategory.loadingModel.hasNextPage =
          result.data.length === this.pageSize && (result.totalSize > currentCategory.currentPage * this.pageSize);
        // 合并示例卡片数据
        currentCategory.sampleCards = (currentCategory.sampleCards || []).concat(result.data);
        // 更新示例分类
        this.changeSampleCategories(currentCategory);
      })
      .catch((error: BusinessError) => {
        // 记录错误日志
        Logger.error(TAG, `getSampleList failed,cause ${error.code} ${error.message}`);
        // 如果是网络连接失败错误
        if (error.code === RequestErrorCode.ERROR_NETWORK_CONNECT_FAILED) {
          // 创建无网络状态的加载模型
          currentCategory.loadingModel = new LoadingModel(LoadingStatus.NO_NETWORK);
        } else {
          // 创建失败状态的加载模型
          currentCategory.loadingModel = new LoadingModel(LoadingStatus.FAILED);
        }
        // 更新示例分类
        this.changeSampleCategories(currentCategory);
      });
  }

  // 定义跳转详情视图的受保护方法
  protected jumpDetailView(param: SampleDetailParams): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 根据断点类型获取页面上下文
    const pageContext: PageContext =
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('samplePageContext')! :
        AppStorage.get('pageContext') as PageContext;
    // 打开页面
    pageContext.openPage({
      routerName: 'SampleDetailView',
      param: param,
    }, true);
  }

  // 定义更改示例分类的私有方法
  private changeSampleCategories(currentCategory: SampleCategory): void {
    // 创建分类列表
    const categoryList: SampleCategory[] = [];
    // 遍历状态中的示例分类
    this.state.sampleCategories.forEach((sampleCategory: SampleCategory) => {
      // 如果分类类型匹配
      if (sampleCategory.categoryType === currentCategory.categoryType) {
        // 添加当前分类
        categoryList.push(currentCategory);
      } else {
        // 添加原分类
        categoryList.push(sampleCategory);
      }
    });
    // 更新状态中的示例分类
    this.state.sampleCategories = categoryList;
  }
}

// 导出实践事件类型枚举
export enum PracticeEventType {
  // 跳转详情详情事件
  JUMP_DETAIL_DETAIL = 'jumpDetailView',
  // 加载示例列表事件
  LOAD_SAMPLE_LIST = 'loadSampleList',
  // 加载示例页面事件
  LOAD_SAMPLE_PAGE = 'loadSamplePage',
}

// 导出实践事件参数接口
export interface PracticeEventParam<T> {
  // 事件类型
  type: PracticeEventType | BaseHomeEventType;
  // 事件参数
  param: T;
}

// 导出加载示例页面参数接口
export interface LoadSamplePageParam {
  // 回调函数
  callback: Function;
}
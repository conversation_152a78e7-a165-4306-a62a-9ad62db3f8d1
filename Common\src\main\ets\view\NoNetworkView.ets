// 导入AbilityKit包中的通用类型和Want接口
import type { common, Want } from '@kit.AbilityKit';
// 导入基础服务包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用常量
import { CommonConstants } from '../constant/CommonConstants';
// 导入列枚举
import { ColumnEnum } from '../constant/CommonEnums';
// 导入断点类型枚举
import { BreakpointTypeEnum } from '../model/GlobalInfoModel';
// 导入断点类型工具类
import { BreakpointType } from '../util/BreakpointSystem';
// 导入日志工具
import Logger from '../util/Logger';

// 日志标签常量
const TAG = '[NoNetworkView]';

/**
 * 无网络视图构建函数
 * 显示网络连接失败状态页面，包含失败图标、错误提示和网络设置按钮
 * 支持响应式网格布局和点击重新加载功能
 * @param breakpoint 当前断点类型
 * @param handleReload 重新加载处理函数，可选参数
 */
@Builder
export function NoNetworkView(breakpoint: BreakpointTypeEnum, handleReload?: () => void) {
  // 响应式网格行布局，根据不同断点设置列数
  GridRow({ columns: { sm: ColumnEnum.SM, md: ColumnEnum.MD, lg: ColumnEnum.LG } }) {
    // 网格列，设置跨度和偏移量
    GridCol({
      // 设置不同断点下的跨度
      span: { sm: CommonConstants.SPACE_4, md: CommonConstants.SPAN_6, lg: CommonConstants.SPAN_6 },
      // 设置不同断点下的偏移量
      offset: { sm: 0, md: 1, lg: CommonConstants.SPAN_3 },
    }) {
      // 主要内容垂直布局容器
      Column() {
        // 顶部占位行，用于布局调整
        Row()
          // 设置高度为小尺寸加载大小
          .height($r('app.float.loading_size_sm'))
        // 错误信息垂直布局容器
        Column() {
          // 网络错误图标
          Image($r('app.media.ic_failure'))
            // 禁用拖拽功能
            .draggable(false)
            // 根据断点设置不同的宽度
            .width(new BreakpointType({
              // 小屏幕断点使用小尺寸
              sm: $r('app.float.failure_size_sm'),
              // 中屏幕断点使用中等尺寸
              md: $r('app.float.failure_size_md'),
              // 大屏幕断点使用大尺寸
              lg: $r('app.float.failure_size_lg'),
            }).getValue(breakpoint))
            // 设置宽高比为1:1，保持正方形
            .aspectRatio(1)
          // 网络错误提示文本
          Text($r('app.string.network_error'))
            // 设置字体颜色为系统次要字体颜色
            .fontColor($r('sys.color.font_secondary'))
            // 设置字体大小为Body_M
            .fontSize($r('sys.float.Body_M'))
            // 设置顶部外边距为4级内边距
            .margin({ top: $r('sys.float.padding_level4') })
        }

        // 网络设置按钮
        Button($r('app.string.network_setting'),
          { buttonStyle: ButtonStyleMode.NORMAL, controlSize: ControlSize.NORMAL })
          // 设置按钮宽度为100%
          .width('100%')
          // 设置按钮点击事件
          .onClick(() => {
            // 获取UI能力上下文
            const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
            // 构造启动设置应用的Want对象
            const want: Want = {
              // 设置应用包名
              bundleName: 'com.huawei.hmos.settings',
              // 设置应用能力名
              abilityName: 'com.huawei.hmos.settings.MainAbility',
              // 设置URI为WiFi入口
              uri: 'wifi_entry',
            };
            try {
              // 启动设置应用
              context.startAbility(want).then(() => {
                // 记录启动设置能力成功信息日志
                Logger.info(TAG, `start setting ability succeed. `);
              }).catch((err: BusinessError) => {
                // 记录启动设置能力失败错误日志
                Logger.error(TAG, `start setting ability failed. ${err.code}, ${err.message}.`);
              });
            } catch (err) {
              // 捕获异常并转换为业务错误类型
              const error = err as BusinessError;
              // 记录启动能力失败错误日志
              Logger.error(TAG, `StartAbility failed. code: ${error.code}, message: ${error.message}`);
            }
          })
          // 设置按钮底部外边距
          .margin({ bottom: $r('app.float.loading_size_sm') })
      }
      // 设置整个容器的点击事件，调用重新加载处理函数（如果存在）
      .onClick(() => handleReload?.())
      // 设置容器宽度为100%
      .width('100%')
      // 设置容器高度为100%
      .height('100%')
      // 设置容器内边距为8级内边距
      .padding($r('sys.float.padding_level8'))
      // 设置背景颜色为系统次要背景色
      .backgroundColor($r('sys.color.background_secondary'))
      // 设置子组件水平居中对齐
      .alignItems(HorizontalAlign.Center)
      // 设置子组件垂直间距分布对齐
      .justifyContent(FlexAlign.SpaceBetween)
    }
  }
}
{"module": {"name": "Phone", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "routerMap": "$profile:router_map", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:ic_start_icon_800", "startWindowBackground": "$color:start_window_background", "exported": true, "preferMultiWindowOrientation": "landscape_auto", "minWindowWidth": 1440, "minWindowHeight": 940, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"], "domainVerify": true}]}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config"}]}, {"name": "PhoneFormAbility", "srcEntry": "./ets/phoneformability/PhoneFormAbility.ets", "label": "$string:PhoneFormAbility_label", "description": "$string:PhoneFormAbility_desc", "type": "form", "metadata": [{"name": "ohos.extension.form", "resource": "$profile:form_config"}]}], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:internet_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.GET_NETWORK_INFO", "reason": "$string:network_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.VIBRATE", "reason": "$string:vibrator_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}]}}
// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入行布局描述器类型，用于描述组件配置
import type { RowDescriptor } from './RowDescriptor';

/**
 * 行布局属性修改器类
 * 继承通用属性修改器，专门用于修改行布局组件的属性
 * 支持交叉轴对齐和主轴对齐等属性的动态修改
 */
@Observed
export class RowAttributeModifier extends CommonAttributeModifier<RowDescriptor, RowAttribute> {
  /**
   * 应用普通属性方法
   * 将描述器中的属性值应用到行布局组件实例上
   * @param instance 行布局组件属性实例
   */
  applyNormalAttribute(instance: RowAttribute): void {
    // 分配交叉轴对齐属性，设置子组件在交叉轴上的对齐方式
    this.assignAttribute((descriptor => descriptor.alignItems), (val) => instance.alignItems(val));
    // 分配主轴对齐属性，设置子组件在主轴上的对齐方式
    this.assignAttribute((descriptor => descriptor.flexAlign), (val) => instance.justifyContent(val));
  }
}
// 导入ArkUI工具包中的曲线动画模块
import { curves } from '@kit.ArkUI';
// 导入通用模块中的全局信息模型类型定义
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举、通用常量、Web节点控制器和Web工具
import {
  BreakpointType,
  BreakpointTypeEnum,
  CommonConstants,
  WebNodeController,
  WebUtil,
} from '@ohos/common';
// 导入代码预览组件
import { CodePreviewComponent } from './CodePreviewComponent';
// 导入组件详情配置接口类型定义
import type { ConfigInterface } from '../componentdetailview/ComponentDetailConfig';
// 导入组件详情页面视图模型和顶部导航变更事件
import { ComponentDetailPageVM, TopNavigationChangeEvent } from '../viewmodel/ComponentDetailPageVM';
// 导入组件详情状态类型定义
import type { ComponentDetailState } from '../viewmodel/ComponentDetailState';
// 导入推荐数据类型定义
import type { RecommendData } from '../model/ComponentDetailData';
// 导入组件详情管理器
import { ComponentDetailManager } from '../viewmodel/ComponentDetailManager';
// 导入属性变更区域组件
import { AttributeChangeArea } from './AttributeChangeArea';
// 导入推荐列表项组件
import { RecommendListItem } from './RecommendListItem';
// 导入详情页面常量
import { DetailPageConstant } from '../constant/DetailPageConstant';
// 导入代码预览JavaScript工具
import { CodePreviewJSUtil } from '../util/CodePreviewJSUtil';

// 定义小屏幕预览高度常量
const PREVIEW_HEIGHT_SM: number = DetailPageConstant.PREVIEW_HEIGHT_SM;

// 使用Component装饰器定义详情内容视图组件
@Component
export struct DetailContentView {
  // 使用ObjectLink装饰器定义组件详情状态对象链接
  @ObjectLink componentDetailState: ComponentDetailState;
  // 使用Prop装饰器定义组件名称属性
  @Prop componentName: string;
  // 使用StorageProp装饰器定义全局信息模型，监听断点变化
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义是否显示代码预览状态
  @State isShowCodePreview: boolean = false;
  // 使用State装饰器定义缩放值状态
  @State scaleValue: number = 1;
  // 使用State装饰器定义是否显示分割线状态
  @State showDivider: boolean = false;
  // 使用State装饰器定义是否显示顶部分割线状态
  @State isShowTopDivider: boolean = false;
  // 使用State装饰器定义Web节点控制器状态
  @State webNodeController?: WebNodeController = new WebNodeController();
  // 定义滚动控制器
  scroller: Scroller = new Scroller();
  // 定义私有视图模型，从组件详情管理器获取
  private viewModel?: ComponentDetailPageVM =
    ComponentDetailManager.getInstance().getDetailViewModel(this.componentName);
  // 定义私有顶部内边距
  private paddingToTop: number = 0;
  // 定义私有底部内边距
  private paddingToBottom: number = 36;
  // 定义私有详情配置记录，从应用存储获取
  private detailConfig: Record<string, ConfigInterface> = AppStorage.get('componentDetailConfig')!

  // 使用Builder装饰器定义文本提示构建器
  @Builder
  textTip(text: ResourceStr) {
    // 创建文本组件显示提示文本
    Text(text)
      // 设置字体大小
      .fontSize($r('sys.float.Subtitle_S'))
      // 设置字体颜色
      .fontColor($r('sys.color.font_secondary'))
      // 设置字体粗细
      .fontWeight(FontWeight.Regular)
      // 设置外边距
      .margin({
        top: $r('sys.float.padding_level10'),
        bottom: $r('sys.float.padding_level4'),
      })
  }

  // 定义组件即将出现时的生命周期方法
  aboutToAppear(): void {
    // 获取Web节点控制器
    this.webNodeController = WebUtil.getWebNode(WebUtil.getComponentCodeUrl()) as WebNodeController;
    // 设置底部内边距，如果导航指示器高度为0则使用默认值36
    this.paddingToBottom =
      this.globalInfoModel.naviIndicatorHeight === 0 ? 36 : this.globalInfoModel.naviIndicatorHeight;
    // 在EntryAbility中，设置装饰高度为0
    const decorHeight: number =
      this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL ? this.globalInfoModel.decorHeight : 0;
    // 设置顶部内边距，如果状态栏高度等于装饰高度则为0，否则使用状态栏高度
    this.paddingToTop = this.globalInfoModel.statusBarHeight === decorHeight ? 0 : this.globalInfoModel.statusBarHeight;
  }

  // 定义组件即将消失时的生命周期方法
  aboutToDisappear(): void {
    // 获取Web控制器
    const webController = WebUtil.getWebController(WebUtil.getComponentCodeUrl());
    // 将Web控制器滚动到顶部
    webController?.scrollTo(0, 0);
  }

  // 定义跳转到代码预览视图的方法
  jumpCodePreviewView() {
    // 获取组件详情状态中的代码
    const code = this.componentDetailState.code;
    // 从组件详情管理器获取详情视图模型
    const viewModel: ComponentDetailPageVM | undefined =
      ComponentDetailManager.getInstance().getDetailViewModel(this.componentName);
    // 移除Web节点控制器
    this.webNodeController?.remove();
    // 将Web节点控制器设置为undefined
    this.webNodeController = undefined;
    // 定义切换到全屏的方法名
    const toFullScreenMethod: string = 'toFullScreen()';
    // 调用代码预览JavaScript工具运行JS方法
    CodePreviewJSUtil.codeViewRunJS(toFullScreenMethod);
    // 使用插值弹簧曲线执行动画
    animateTo({ curve: curves.interpolatingSpring(0, 1, 195, 23) }, () => {
      // 调用视图模型的跳转到代码预览方法
      viewModel?.jumpToCodePreview(code, () => {
        // 从代码预览返回时的回调
        this.backFromCodePreview();
      });
    });
  }

  // 定义从代码预览返回的方法
  backFromCodePreview(): void {
    // 重新获取Web节点控制器
    this.webNodeController = WebUtil.getWebNode(WebUtil.getComponentCodeUrl()) as WebNodeController;
    // 添加Web节点控制器
    this.webNodeController.add();
  }

  // 定义处理断点变化的方法
  handleBreakPointChange() {
    // 向视图模型发送顶部导航变更事件
    this.viewModel?.sendEvent(new TopNavigationChangeEvent(this.globalInfoModel.currentBreakpoint ===
    BreakpointTypeEnum.SM ? false : this.isShowTopDivider));
  }

  // 定义组件构建方法
  build() {
    // 创建弹性布局容器，根据断点类型设置方向
    Flex({
      direction: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? FlexDirection.Column :
      FlexDirection.Row,
    }) {
      // 创建垂直排列的列容器用于预览区域
      Column() {
        // 调用文本提示构建器显示预览标题
        this.textTip($r('app.string.preview'))
        // 创建垂直排列的列容器用于预览内容
        Column() {
          // 调用详情配置中的预览组件构建器
          (this.detailConfig[this.componentName] as ConfigInterface).previewComponentBuilder.builder({
            descriptor: this.componentDetailState.descriptor,
          })
        }
        // 设置顶部外边距，根据断点类型判断
        .margin({
          top: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? 0 : $r('sys.float.padding_level4'),
        })
        // 设置背景颜色
        .backgroundColor($r('sys.color.comp_background_primary'))
        // 设置内容对齐方式为居中
        .justifyContent(FlexAlign.Center)
        // 设置宽度为100%
        .width('100%')
        // 根据断点类型设置高度
        .height(new BreakpointType({
          sm: DetailPageConstant.PREVIEW_SUB_HEIGHT_SM,
          md: (this.globalInfoModel.deviceHeight - (CommonConstants.NAVIGATION_HEIGHT + this.paddingToTop +
          this.paddingToBottom) - DetailPageConstant.TEXT_TIP_HEIGHT),
          lg: (this.globalInfoModel.deviceHeight - (CommonConstants.NAVIGATION_HEIGHT + this.paddingToTop +
          this.paddingToBottom) - DetailPageConstant.TEXT_TIP_HEIGHT),
        }).getValue(this.globalInfoModel.currentBreakpoint))
        // 设置边框圆角
        .borderRadius($r('sys.float.corner_radius_level8'))
      }
      // 设置内边距
      .padding({
        top: CommonConstants.NAVIGATION_HEIGHT + this.globalInfoModel.statusBarHeight + DetailPageConstant.SPACE_NORMAL,
        bottom: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? $r('sys.float.padding_level6') : 0,
        left: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level16'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        right: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level4'),
          lg: $r('sys.float.padding_level6'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
      })
      // 根据断点类型设置大小
      .size(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? { width: '100%' } : { width: '50%' })
      // 设置水平对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)

      // 判断是否为小屏幕且显示分割线
      if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM && this.showDivider) {
        // 创建分割线组件
        Divider()
          // 设置宽度为100%
          .width('100%')
      }
      // 创建滚动容器
      Scroll(this.scroller) {
        // 创建垂直排列的列容器，设置间距
        Column({ space: DetailPageConstant.SPACE_NORMAL }) {
          // 判断是否有属性需要显示
          if (this.componentDetailState.attributes.length !== 0) {
            // 调用文本提示构建器显示更改属性标题
            this.textTip($r('app.string.changeAttributes'))
            // 创建属性变更区域组件
            AttributeChangeArea({
              attributes: this.componentDetailState.attributes,
              componentName: this.componentName,
            })
          }
          // 调用文本提示构建器显示代码标题
          this.textTip($r('app.string.code'))
          // 创建代码预览组件
          CodePreviewComponent({
            webNodeController: this.webNodeController,
            code: this.componentDetailState.code,
            componentName: this.componentName,
            pageContainer: false,
            pushPage: () => {
              this.jumpCodePreviewView();
            },
          })
            // 设置宽度为100%
            .width('100%')
            // 设置高度
            .height($r('app.float.code_preview_height'))
            // 设置几何变换
            .geometryTransition(CommonConstants.CODE_PREVIEW_GEOMETRY_ID, { follow: true })
            // 设置边框圆角
            .borderRadius($r('sys.float.corner_radius_level8'))
            // 设置裁剪
            .clip(true)
            // 设置顶部外边距
            .margin({
              top: (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ||
                this.componentDetailState.attributes.length !== 0) ? 0 : $r('sys.float.padding_level4'),
            })
            // 设置点击事件处理
            .onClick(() => {
              // 跳转到代码预览视图
              this.jumpCodePreviewView();
            })
          // 判断是否有推荐内容需要显示
          if (this.componentDetailState.recommends.length > 0) {
            // 调用文本提示构建器显示推荐标题
            this.textTip($r('app.string.recommend'))
            // 创建垂直排列的列容器用于推荐列表
            Column() {
              // 遍历推荐数据数组
              ForEach(this.componentDetailState.recommends, (item: RecommendData, index: number) => {
                // 判断是否不是第一项，添加分割线
                if (index !== 0) {
                  // 创建分割线组件
                  Divider()
                    // 设置分割线颜色
                    .color($r('sys.color.comp_divider'))
                    // 设置宽度为100%
                    .width('100%')
                    // 设置左右内边距
                    .padding({ left: $r('sys.float.padding_level6'), right: $r('sys.float.padding_level6') })
                }
                // 创建推荐列表项组件
                RecommendListItem({ itemData: item })
                  // 设置高度
                  .height(DetailPageConstant.ATTRIBUTE_ITEM_HEIGHT)
              }, (_item: RecommendData) => _item.id?.toString())
            }
            // 设置背景颜色
            .backgroundColor($r('sys.color.comp_background_primary'))
            // 设置宽度为100%
            .width('100%')
            // 设置边框圆角
            .borderRadius($r('sys.float.corner_radius_level8'))
          }
        }
        // 设置顶部外边距
        .margin({
          top: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? 0 :
          $r('sys.float.padding_level4'),
        })
        // 设置水平对齐方式为左对齐
        .alignItems(HorizontalAlign.Start)
        // 设置内边距
        .padding({
          top: new BreakpointType({
            sm: 0,
            md: CommonConstants.NAVIGATION_HEIGHT + this.globalInfoModel.statusBarHeight,
            lg: CommonConstants.NAVIGATION_HEIGHT + this.globalInfoModel.statusBarHeight,
          }).getValue(this.globalInfoModel.currentBreakpoint),
          bottom: this.paddingToBottom,
        })
      }
      // 设置对齐方式为顶部对齐
      .align(Alignment.Top)
      // 设置滚动条状态为关闭
      .scrollBar(BarState.Off)
      // 设置边缘效果为弹簧效果
      .edgeEffect(EdgeEffect.Spring)
      // 设置滚动事件处理
      .onDidScroll(() => {
        // 判断滚动偏移量是否大于阈值
        if (this.scroller.currentOffset().yOffset > DetailPageConstant.SCROLL_OFFSET_Y) {
          // 如果未显示分割线则显示
          if (!this.showDivider) {
            this.showDivider = true;
          }
          // 根据断点类型设置是否显示顶部分割线
          this.isShowTopDivider = this.globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.SM ? true : false;
          // 向视图模型发送顶部导航变更事件
          this.viewModel?.sendEvent(new TopNavigationChangeEvent(this.isShowTopDivider));
        } else if (this.scroller.currentOffset().yOffset <= DetailPageConstant.SCROLL_OFFSET_Y) {
          // 隐藏分割线
          this.showDivider = false;
          // 隐藏顶部分割线
          this.isShowTopDivider = false;
          // 向视图模型发送顶部导航变更事件
          this.viewModel?.sendEvent(new TopNavigationChangeEvent(this.isShowTopDivider));
        }
      })
      // 设置嵌套滚动模式
      .nestedScroll({
        scrollForward: NestedScrollMode.SELF_ONLY,
        scrollBackward: NestedScrollMode.SELF_ONLY,
      })
      // 根据断点类型设置大小
      .size(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
        {
          width: '100%',
          height: (this.globalInfoModel.deviceHeight - PREVIEW_HEIGHT_SM -
            (CommonConstants.NAVIGATION_HEIGHT + this.paddingToTop)),
        } :
        {
          width: '50%',
          height: this.globalInfoModel.deviceHeight,
        })
      // 设置内边距
      .padding({
        left: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level4'),
          lg: $r('sys.float.padding_level6'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
        right: new BreakpointType({
          sm: $r('sys.float.padding_level8'),
          md: $r('sys.float.padding_level12'),
          lg: $r('sys.float.padding_level16'),
        }).getValue(this.globalInfoModel.currentBreakpoint),
      })
    }
    // 设置宽度为100%
    .width('100%')
    // 设置高度为100%
    .height('100%')
  }
}
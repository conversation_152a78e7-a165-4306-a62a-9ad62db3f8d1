/**
 * 加载状态枚举
 * 定义应用中各种加载状态的枚举值
 */
export enum LoadingStatus {
  // 空闲状态
  IDLE = 'idle',
  // 关闭状态
  OFF = 'off',
  // 加载中状态
  LOADING = 'loading',
  // 加载成功状态
  SUCCESS = 'success',
  // 加载失败状态
  FAILED = 'failed',
  // 无网络状态
  NO_NETWORK = 'no_network',
}

/**
 * 网格行列数枚举
 * 定义不同断点下的网格列数配置
 */
export enum ColumnEnum {
  // 小屏幕4列
  SM = 4,
  // 中等屏幕8列
  MD = 8,
  // 大屏幕12列
  LG = 12,
}

/**
 * 模块名称枚举
 * 定义应用中各个模块的标识名称
 */
export enum ModuleNameEnum {
  // 组件列表视图模块
  COMPONENT_LIST = 'componentListView',
  // 组件详情视图模块
  COMPONENT_DETAIL = 'componentDetailView',
  // 代码预览模块
  CODE_PREVIEW = 'codePreview',
  // 文章详情模块
  ARTICLE_DETAIL = 'articleDetail',
}

/**
 * Web组件滚动方向枚举
 * 定义Web组件中滚动的方向类型
 */
export enum ScrollDirectionEnum {
  // 向上滚动
  UP = 'up',
  // 向下滚动
  DOWN = 'down'
}

/**
 * 产品系列枚举
 * 定义不同的产品系列类型
 */
export enum ProductSeriesEnum {
  // 可折叠PC产品系列
  HPR = 'HPR',
  // VDE产品系列
  VDE = 'VDE',
}
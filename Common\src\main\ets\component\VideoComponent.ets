/**
 * 视频播放组件
 * 提供完整的视频播放功能，包含自动播放、循环播放、音量控制等特性
 * 支持可见区域检测和交互控制
 */
@Component
export struct VideoComponent {
  // 视频媒体资源路径
  @Prop mediaSrc: ResourceStr;
  // 是否自动播放，默认为true
  @Prop autoPlay: boolean = true;
  // 是否循环播放，默认为true
  @Prop loopPlay: boolean = true;
  // 是否支持点击暂停，默认为false
  @Prop clickPause: boolean = false;
  // 开始可见的比例阈值，默认为0.5
  @Prop startVisibleRatio: number = 0.5;
  // 音量控制状态，true为静音，false为有声
  @State voiceControl: boolean = true;
  // 视频暂停显示状态
  @State videoPauseShow: boolean = false;
  // 音量按钮显示状态
  @State voiceShow: boolean = false;
  // 触发值替换，用于动画效果
  @State triggerValueReplace: number = 0;
  // 曝光比例数组，用于可见区域检测
  private exposureRatio: number[] = [0.0, this.startVisibleRatio, 1.0];
  // 视频控制器实例
  private videoController: VideoController = new VideoController();
  // 定时器ID，用于控制音量按钮的显示时间
  private timeoutID = setTimeout(() => {
    this.voiceShow = false;
  }, 3000);

  /**
   * 可见区域判断方法
   * 根据当前可见比例决定视频的播放状态
   * @param currentRatio 当前可见比例
   */
  private visibleAreaJudge(currentRatio: number) {
    // 如果当前可见比例大于等于开始可见阈值
    if (currentRatio >= this.startVisibleRatio) {
      // 如果启用自动播放
      if (this.autoPlay) {
        // 显示音量按钮
        this.showVoiceButton();
        // 隐藏暂停按钮
        this.videoPauseShow = false;
        // 开始播放视频
        this.videoController.start();
      }
    } else {
      // 显示音量按钮
      this.voiceShow = true;
      // 显示暂停按钮
      this.videoPauseShow = true;
      // 暂停视频播放
      this.videoController.pause();
    }
  }

  /**
   * 显示音量按钮方法
   * 显示音量按钮并设置3秒后自动隐藏
   */
  private showVoiceButton() {
    // 显示音量按钮
    this.voiceShow = true;
    // 清除之前的定时器
    clearTimeout(this.timeoutID);
    // 设置新的定时器，3秒后隐藏音量按钮
    this.timeoutID = setTimeout(() => {
      this.voiceShow = false;
    }, 3000);
  }

  /**
   * 构建视频组件的UI结构
   * 包含视频播放器、音量控制按钮和播放控制按钮
   */
  build() {
    // 创建居中对齐的堆叠容器，用于放置播放控制按钮
    Stack({ alignContent: Alignment.Center }) {
      // 创建左上角对齐的堆叠容器，用于放置视频和音量按钮
      Stack({ alignContent: Alignment.TopStart }) {
        // 创建视频播放组件
        Video({ src: this.mediaSrc, controller: this.videoController })
          // 设置静音状态
          .muted(this.voiceControl)
          // 设置视频适配方式为包含，保持宽高比
          .objectFit(ImageFit.Contain)
          // 隐藏默认控制条
          .controls(false)
          // 设置视频播放完成事件处理
          .onFinish(() => {
            // 如果启用循环播放
            if (this.loopPlay) {
              // 重新开始播放
              this.videoController.start();
            } else {
              // 显示暂停按钮
              this.videoPauseShow = true;
              // 暂停视频
              this.videoController.pause();
            }
          })
          // 设置触摸事件处理
          .onTouch(() => {
            // 显示音量按钮
            this.showVoiceButton();
          })
          // 设置点击事件处理
          .onClick(() => {
            // 如果启用点击暂停功能
            if (this.clickPause) {
              // 显示暂停按钮
              this.videoPauseShow = true;
              // 暂停视频播放
              this.videoController.pause();
            }
          })
          // 设置可见区域变化事件处理
          .onVisibleAreaChange(this.exposureRatio, (isVisible: boolean, currentRatio: number) => {
            // 调用可见区域判断方法
            this.visibleAreaJudge(currentRatio);
          })
        // 如果需要显示音量按钮
        if (this.voiceShow) {
          // 创建音量控制符号图标，根据静音状态显示不同图标
          SymbolGlyph(this.voiceControl ? $r('sys.symbol.speaker_slash_fill') : $r('sys.symbol.speaker_fill'))
            // 设置图标字体大小
            .fontSize($r('app.float.voice_font_size'))
            // 设置图标颜色为白色
            .fontColor([Color.White])
            // 设置弹跳动画效果
            .symbolEffect(new BounceSymbolEffect(EffectScope.WHOLE, EffectDirection.UP), this.triggerValueReplace)
            // 设置点击事件处理
            .onClick(() => {
              // 增加触发值，用于动画效果
              this.triggerValueReplace++;
              // 切换音量控制状态
              this.voiceControl = !this.voiceControl;
            })
        }
      }

      // 如果需要显示视频暂停按钮
      if (this.videoPauseShow) {
        // 创建播放按钮符号图标
        SymbolGlyph($r('sys.symbol.play_circle'))
          // 设置图标字体大小
          .fontSize($r('app.float.video_pause_font_size'))
          // 设置图标颜色为白色
          .fontColor([Color.White])
          // 设置弹跳动画效果
          .symbolEffect(new BounceSymbolEffect(EffectScope.WHOLE, EffectDirection.UP), this.triggerValueReplace)
          // 设置点击事件处理
          .onClick(() => {
            // 增加触发值，用于动画效果
            this.triggerValueReplace++;
            // 隐藏暂停按钮
            this.videoPauseShow = false;
            // 开始播放视频
            this.videoController.start();
          })
      }
    }
    // 设置堆叠容器宽度为100%
    .width('100%')
    // 设置堆叠容器高度为100%
    .height('100%')
  }
}
// 导入按钮描述器类型，用于获取按钮的配置信息
import type { ButtonDescriptor } from './ButtonDescriptor';
// 导入通用属性修改器基类，用于继承属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';

/**
 * 按钮属性修改器类
 * 继承通用属性修改器，用于动态修改按钮组件的属性
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class ButtonAttributeModifier extends CommonAttributeModifier<ButtonDescriptor, ButtonAttribute> {
  /**
   * 应用普通属性的方法
   * 将按钮描述器中的属性值应用到按钮实例上
   * @param instance 按钮属性实例，用于设置按钮的各种属性
   */
  public applyNormalAttribute(instance: ButtonAttribute): void {
    // 分配按钮样式属性，从描述器获取样式值并应用到按钮实例
    this.assignAttribute((descriptor => descriptor.buttonStyle), (val) => instance.buttonStyle(val));
    // 分配控件尺寸属性，从描述器获取尺寸值并应用到按钮实例
    this.assignAttribute((descriptor => descriptor.controlSize), (val) => instance.controlSize(val));
    // 分配按钮类型属性，从描述器获取类型值并应用到按钮实例
    this.assignAttribute((descriptor => descriptor.buttonType), (val) => instance.type(val));
    // 分配背景颜色属性，从描述器获取颜色值并应用到按钮实例
    this.assignAttribute((descriptor => descriptor.backgroundColor), (val) => instance.backgroundColor(val));
  }
}
// 导入通用业务模块中的横幅数据类型定义
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的卡片样式类型枚举、卡片类型枚举和媒体类型枚举
import { CardStyleTypeEnum, CardTypeEnum, MediaTypeEnum } from '@ohos/commonbusiness';

// 使用Observed装饰器标记类为可观察对象，支持数据绑定
@Observed
// 导出组件内容数据类
export class ComponentContent {
  // 组件内容ID，唯一标识符
  public id: number = 0;
  // 卡片类型，默认为未知类型
  public type: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 媒体类型，默认为图片类型
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  // 媒体资源URL，存储图片或视频的路径
  public mediaUrl: string = '';
  // 标题，显示组件名称
  public title: string = '';
  // 副标题，显示组件的简短描述
  public subTitle: string = '';
  // 描述信息，详细说明组件功能
  public desc: string = '';
}

// 使用Observed装饰器标记类为可观察对象，支持数据绑定
@Observed
// 导出组件卡片数据类
export class ComponentCardData {
  // 卡片ID，唯一标识符
  public id: number = 0;
  // 卡片标题，显示卡片名称
  public cardTitle: string = '';
  // 卡片副标题，显示卡片的简短描述
  public cardSubTitle: string = '';
  // 卡片类型，默认为未知类型
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN;
  // 卡片样式类型，默认为列表样式
  public cardStyleType: CardStyleTypeEnum = CardStyleTypeEnum.LIST;
  // 卡片图片URL，存储卡片封面图片路径
  public cardImage: string = '';
  // 版本信息，记录卡片版本号
  public version: string = '';
  // 卡片内容列表，包含多个组件内容
  public cardContents: ComponentContent[] = [];
}

// 导出组件数据类
export class ComponentData {
  // 横幅信息列表，可选属性，存储页面顶部横幅数据
  public bannerInfos?: BannerData[];
  // 卡片数据列表，存储页面中的所有卡片数据
  public cardData: ComponentCardData[] = [];
}
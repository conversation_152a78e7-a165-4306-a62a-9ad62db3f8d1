/**
 * 没有更多数据组件
 * 用于在列表底部显示"没有更多"提示，表示数据已全部加载完成
 * 包含左右分割线和中间提示文字的布局设计
 */
@Builder
export function NoMore() {
  // 创建水平排列的行容器，用于放置分割线和提示文字
  Row() {
    // 创建左侧分割线
    Divider()
      // 设置分割线颜色为系统文本字段副背景色
      .color($r('sys.color.ohos_id_color_text_field_sub_bg'))
      // 设置分割线高度为1像素
      .height(1)
      // 设置布局权重为1，使分割线占据剩余空间
      .layoutWeight(1)
    // 创建"没有更多"提示文字
    Text($r('app.string.no_more'))
      // 设置文字颜色为系统次要字体颜色
      .fontColor($r('sys.color.font_secondary'))
      // 设置文字大小为系统预定义的中等说明文字字号
      .fontSize($r('sys.float.Caption_M'))
      // 设置文字四周边距，与分割线保持适当间距
      .margin($r('sys.float.padding_level6'))
    // 创建右侧分割线
    Divider()
      // 设置分割线颜色为系统文本字段副背景色
      .color($r('sys.color.ohos_id_color_text_field_sub_bg'))
      // 设置分割线高度为1像素
      .height(1)
      // 设置布局权重为1，使分割线占据剩余空间
      .layoutWeight(1)
  }
  // 设置子元素在垂直方向居中对齐
  .alignItems(VerticalAlign.Center)
  // 设置行容器宽度为100%，占满父容器
  .width('100%')
}
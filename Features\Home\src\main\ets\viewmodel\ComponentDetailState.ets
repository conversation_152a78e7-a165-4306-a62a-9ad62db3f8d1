// 导入ArkUI工具包中的项目限制和分段按钮文本项
import { ItemRestriction, SegmentButtonTextItem } from '@kit.ArkUI';
// 导入通用模块中的顶部导航数据类型定义
import type { TopNavigationData } from '@ohos/common';
// 导入通用模块中的基础状态、加载状态和可观察数组
import { BaseState, LoadingStatus, ObservedArray } from '@ohos/common';
// 导入通用描述符类型定义
import type { CommonDescriptor } from './CommonDescriptor';
// 导入组件详情数据相关类型定义
import type { AttributeData, RecommendData } from '../model/ComponentDetailData';
// 导入属性类
import { Attribute } from './Attribute';
// 导入属性类型枚举
import { AttributeTypeEnum } from './AttributeTypeEnum';

// 使用Observed装饰器定义可观察的组件详情状态类
@Observed
export class ComponentDetailState extends BaseState {
  // 定义公共描述符
  public descriptor: CommonDescriptor;
  // 定义公共属性可观察数组
  public attributes: ObservedArray<Attribute>;
  // 定义公共代码字符串
  public code: string;
  // 定义公共推荐数据数组
  public recommends: RecommendData[];
  // 定义公共加载状态
  public loadingStatus: LoadingStatus;
  // 定义公共顶部导航数据
  public topNavigationData: TopNavigationData;

  // 定义构造函数
  constructor(
    descriptor: CommonDescriptor,
    attributes: ObservedArray<Attribute>,
    code: string,
    recommends: RecommendData[],
    loadingStatus: LoadingStatus,
    topNavigationData: TopNavigationData,
  ) {
    // 调用父类构造函数
    super();
    // 初始化描述符
    this.descriptor = descriptor;
    // 初始化属性
    this.attributes = attributes;
    // 初始化代码
    this.code = code;
    // 初始化推荐列表
    this.recommends = recommends;
    // 初始化加载状态
    this.loadingStatus = loadingStatus;
    // 初始化顶部导航数据
    this.topNavigationData = topNavigationData;
  }
}

// 使用Observed装饰器定义可观察的选择组件属性类
@Observed
export class SelectComAttribute extends Attribute {
  // 定义公共选择选项数组
  public selectOption: SelectOption[] = [];
}

// 使用Observed装饰器定义可观察的切换按钮属性类
@Observed
export class ToggleButtonAttribute extends Attribute {
  // 定义公共选择选项项目限制
  public selectOption: ItemRestriction<SegmentButtonTextItem> =
    [{} as SegmentButtonTextItem, {} as SegmentButtonTextItem];
}

// 使用Observed装饰器定义可观察的滑块组件属性类
@Observed
export class SliderComAttribute extends Attribute {
  // 定义公共左范围
  public leftRange: number = 0;
  // 定义公共右范围
  public rightRange: number = 0;
  // 定义公共步长
  public step: number = 1;
}

// 使用Observed装饰器定义可观察的颜色选择器属性类
@Observed
export class ColorPickerAttribute extends Attribute {
}

// 使用Observed装饰器定义可观察的透明度选择器属性类
@Observed
export class OpacityPickerAttribute extends Attribute {
  // 定义公共左范围
  public leftRange: number = 0;
  // 定义公共右范围
  public rightRange: number = 0;
  // 定义公共步长
  public step: number = 1;
}

// 使用Observed装饰器定义可观察的切换组件属性类
@Observed
export class ToggleComAttribute extends Attribute {
}

// 定义根据属性数据显示类型获取枚举类型的函数
export function getEnumType(attributeData: AttributeData): AttributeTypeEnum {
  // 根据显示类型进行切换
  switch (attributeData.displayType) {
    case 'enum':
      // 如果是枚举类型，根据属性值长度返回选择或切换按钮类型
      return JSON.parse(attributeData.propertyValues).length > 2 ? AttributeTypeEnum.SELECT :
      AttributeTypeEnum.TOGGLE_BUTTON;
    case 'boolean':
      // 如果是布尔类型，返回切换类型
      return AttributeTypeEnum.TOGGLE;
    case 'number':
      // 如果是数字类型，返回滑块类型
      return AttributeTypeEnum.SLIDER;
    case 'color':
      // 如果是颜色类型，返回颜色类型
      return AttributeTypeEnum.COLOR;
    case 'opacity':
      // 如果是透明度类型，返回透明度类型
      return AttributeTypeEnum.OPACITY;
  }
  // 默认返回选择类型
  return AttributeTypeEnum.SELECT;
}
// 导入横幅数据模型类型
import type { BannerData } from '../model/BannerData';

/**
 * 横幅数据源类
 * 实现IDataSource接口，用于为LazyForEach提供横幅数据
 * 管理横幅数据数组和数据变化监听器
 */
export class BannerSource implements IDataSource {
  // 横幅数据数组，私有属性
  private splashArray: BannerData[] = [];
  // 数据变化监听器数组，私有属性
  private listeners: DataChangeListener[] = [];

  /**
   * 设置数据数组
   * 更新内部的横幅数据数组
   * @param dataArray 横幅数据数组
   */
  public setDataArray(dataArray: BannerData[]): void {
    this.splashArray = dataArray;
  }

  /**
   * 获取数据总数
   * 返回横幅数据数组的长度
   * @returns 数据总数
   */
  public totalCount(): number {
    return this.splashArray.length;
  }

  /**
   * 根据索引获取数据
   * 返回指定索引位置的横幅数据
   * @param index 数据索引
   * @returns 横幅数据对象
   */
  public getData(index: number): BannerData {
    return this.splashArray[index];
  }

  /**
   * 注册数据变化监听器
   * 添加监听器到监听器数组中，避免重复添加
   * @param listener 数据变化监听器
   */
  public registerDataChangeListener(listener: DataChangeListener): void {
    if (this.listeners.indexOf(listener) < 0) {
      this.listeners.push(listener);
    }
  }

  /**
   * 注销数据变化监听器
   * 从监听器数组中移除指定的监听器
   * @param listener 要移除的数据变化监听器
   */
  public unregisterDataChangeListener(listener: DataChangeListener): void {
    const pos: number = this.listeners.indexOf(listener);
    if (pos >= 0) {
      this.listeners.splice(pos, 1);
    }
  }
}
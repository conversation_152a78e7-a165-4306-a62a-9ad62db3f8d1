// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入发现服务
import { DiscoverService } from '../service/DiscoverService';
// 导入发现数据类型
import type { DiscoverData } from './DiscoverData';

// 定义标签常量
const TAG = '[DiscoverModel]';

// 导出发现模型类
export class DiscoverModel {
  // 定义私有服务属性
  private service: DiscoverService = new DiscoverService();
  // 定义静态实例属性
  private static instance: DiscoverModel;

  // 定义私有构造函数
  private constructor() {
  }

  // 定义获取实例的静态方法
  public static getInstance(): DiscoverModel {
    // 如果实例不存在
    if (!DiscoverModel.instance) {
      // 创建新实例
      DiscoverModel.instance = new DiscoverModel();
    }
    // 返回实例
    return DiscoverModel.instance;
  }

  // 定义获取发现页面的方法
  getDiscoveryPage(): Promise<DiscoverData> {
    // 调用服务获取偏好设置中的发现页面数据
    return this.service.getDiscoveryPageByPreference()
      // 处理成功响应
      .then((data: DiscoverData) => {
        // 返回数据
        return data;
      })
      // 处理错误响应
      .catch((err: string) => {
        // 记录错误日志
        Logger.error(TAG,
          `Call getDiscoveryPage data from network failed! try to get data form preference. ${err}`);
        // 从网络获取发现页面数据
        return this.service.getDiscoverPage()
          // 处理成功响应
          .then((data: DiscoverData) => {
            // 将数据设置到偏好设置
            this.service.setDiscoveryPageToPreference(data);
            // 返回数据
            return data;
          });
      });
  }

  // 定义预加载发现数据的方法
  preloadDiscoveryData(): Promise<void> {
    // 调用服务获取发现页面数据
    return this.service.getDiscoverPage()
      // 处理成功响应
      .then((result: DiscoverData) => {
        // 将结果设置到偏好设置
        this.service.setDiscoveryPageToPreference(result);
      // 处理错误响应
      }).catch((err: BusinessError) => {
        // 记录错误日志
        Logger.error(TAG,
          `Call preloadDiscoveryData data from network failed. ${err.code}, ${err.message}`);
      });
  }
}
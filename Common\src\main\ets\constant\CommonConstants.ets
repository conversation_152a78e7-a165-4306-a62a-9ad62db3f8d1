/**
 * 通用常量类
 * 定义应用中使用的各种常量值，包括尺寸、时间、比例等
 * 提供统一的常量管理和配置
 */
export class CommonConstants {
  // 横幅宽高比 - 小屏幕断点
  public static BANNER_ASPECT_SM: number = 1.28;
  // 横幅宽高比 - 中等屏幕断点
  public static BANNER_ASPECT_MD: number = 0.53;
  // 横幅宽高比 - 大屏幕断点
  public static BANNER_ASPECT_LG: number = 0.53;
  // 横幅宽高比 - 超大屏幕断点
  public static BANNER_ASPECT_XL: number = 0.56;
  // 横幅宽高比 - Verde版本
  public static BANNER_ASPECT_VERDE: number = 0.58;
  // 导航栏高度
  public static NAVIGATION_HEIGHT: number = 56;
  // 标签栏高度
  public static TAB_BAR_HEIGHT: number = 48;
  // 标签栏宽度
  public static TAB_BAR_WIDTH: number = 96;
  // 侧边栏宽度
  public static SIDE_BAR_WIDTH: number = 240;
  // 百分比 - 100%完整百分比
  public static readonly FULL_PERCENT: string = '100%';
  // 间距 - 2像素间距
  public static readonly SPACE_2: number = 2;
  // 间距 - 4像素间距
  public static readonly SPACE_4: number = 4;
  // 间距 - 6像素间距
  public static readonly SPACE_6: number = 6;
  // 间距 - 8像素间距
  public static readonly SPACE_8: number = 8;
  // 间距 - 10像素间距
  public static readonly SPACE_10: number = 10;
  // 间距 - 12像素间距
  public static readonly SPACE_12: number = 12;
  // 间距 - 16像素间距
  public static readonly SPACE_16: number = 16;
  // 间距 - 24像素间距
  public static readonly SPACE_24: number = 24;
  // 间距 - 32像素间距
  public static readonly SPACE_32: number = 32;
  // 轮播图切换持续时间（毫秒）
  public static readonly SWIPER_DURATION: number = 3000;
  // 预加载持续时间（毫秒）
  public static readonly PRELOAD_DURATION: number = 1200;
  // 移除动画持续时间（毫秒）
  public static readonly REMOVE_DURATION: number = 600;
  // 动画延迟时间（毫秒）
  public static readonly ANIMATION_DELAY: number = 200;
  // 动画持续时间（毫秒）
  public static readonly ANIMATION_DURATION: number = 300;
  // 过渡动画持续时间（毫秒）
  public static readonly TRANSITION_DURATION: number = 100;
  // 反馈底部间距 - 小屏幕
  public static readonly FEEDBACK_BOTTOM_SM: number = 16;
  // 网格跨度 - 3列
  public static readonly SPAN_3: number = 3;
  // 网格跨度 - 4列
  public static readonly SPAN_4: number = 4;
  // 网格跨度 - 6列
  public static readonly SPAN_6: number = 6;
  // 网格跨度 - 8列
  public static readonly SPAN_8: number = 8;
  // 网格跨度 - 12列
  public static readonly SPAN_12: number = 12;
  // 车道数 - 小屏幕1车道
  public static readonly LANE_SM: number = 1;
  // 车道数 - 中等屏幕2车道
  public static readonly LANE_MD: number = 2;
  // 车道数 - 大屏幕3车道
  public static readonly LANE_LG: number = 3;
  // 线性渐变角度
  public static readonly LINEAR_GRADIENT_ANGLE: number = 180;
  // 动态安装事件名称
  public static readonly DYNAMIC_INSTALL_EVENT: string = 'DynamicInstallEvent';
  // Promise等待函数，用于创建延迟Promise
  public static readonly PROMISE_WAIT = (delay: number) => new Promise<void>((resolve) => setTimeout(resolve, delay));
  // 横幅几何标识
  public static readonly BANNER_GEOMETRY: string = 'hmos_world_banner_geometry';
  // 代码预览几何标识
  public static readonly CODE_PREVIEW_GEOMETRY_ID: string = 'hmos_world_preview_geometry_id';
  // 超大屏幕弹窗宽度
  public static readonly SHEET_WIDTH_XL: number = 800;
  // 超大屏幕弹窗高度比例
  public static readonly SHEET_HEIGHT_RATIO_XL: number = 0.9;
  // 窗口比例
  public static readonly WINDOW_RATIO: number = 0.9;
  // 最小窗口宽度
  public static readonly MIN_WINDOW_WIDTH: number = 1440;
  // 最小窗口高度
  public static readonly MIN_WINDOW_HEIGHT: number = 940;
}
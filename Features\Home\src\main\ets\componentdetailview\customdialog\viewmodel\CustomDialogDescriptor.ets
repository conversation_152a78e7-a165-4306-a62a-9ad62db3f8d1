// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于继承基础描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入自定义对话框样式枚举和样式映射数据
import { CustomDialogStyle, dialogStyleMapData } from '../entity/CustomDialogAttributeMapping';

/**
 * 自定义对话框描述器类
 * 继承通用描述器，用于描述自定义对话框组件的属性和行为
 * 使用@Observed装饰器实现数据观察和响应式更新
 */
@Observed
export class CustomDialogDescriptor extends CommonDescriptor {
  // 对话框样式属性，控制对话框的显示样式（图文弹窗或进度弹窗）
  public style: CustomDialogStyle = dialogStyleMapData.get('Default')!;

  /**
   * 转换属性方法
   * 将原始属性数组转换为自定义对话框描述器的具体属性值
   * @param attributes 原始属性数组，包含对话框的各种配置属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性，根据属性名称更新对应的描述器属性
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理样式属性
        case 'style':
          // 从映射数据中获取对应的对话框样式枚举值，如果不存在则保持原值
          this.style = dialogStyleMapData.get(attribute.currentValue) ?? this.style;
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    })
  }
}
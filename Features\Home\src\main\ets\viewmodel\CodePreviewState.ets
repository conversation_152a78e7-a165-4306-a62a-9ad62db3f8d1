// 导入通用模块中的基础状态类
import { BaseState } from '@ohos/common';

// 使用Observed装饰器定义可观察的代码预览状态类
@Observed
export class CodePreviewState extends BaseState {
  // 定义顶部平移Y值
  public topTranslateY: number;
  // 定义底部平移Y值
  public bottomTranslateY: number;
  // 定义导航透明度
  public navigationOpacity: number;

  // 定义构造函数
  constructor(topTranslateY: number, bottomTranslateY: number, navigationOpacity: number) {
    // 调用父类构造函数
    super();
    // 初始化顶部平移Y值
    this.topTranslateY = topTranslateY;
    // 初始化底部平移Y值
    this.bottomTranslateY = bottomTranslateY;
    // 初始化导航透明度
    this.navigationOpacity = navigationOpacity;
  }
}
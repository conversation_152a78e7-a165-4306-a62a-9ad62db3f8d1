// 导入通用模块中的应用包信息数据类型
import type { BundleInfoData } from '@ohos/common';
// 导入关于页面状态类型
import type { AboutState } from '../viewmodel/AboutState';
// 导入关于页面视图模型和事件类
import { AboutVM, CheckVersionEvent, UpdateVersionEvent } from '../viewmodel/AboutVM';

// 使用Component装饰器定义关于页面项目卡片组件
@Component
export struct AboutItemCard {
  // 创建视图模型实例
  viewModel: AboutVM = AboutVM.getInstance();
  // 使用State装饰器定义关于页面状态
  @State aboutState: AboutState = this.viewModel.getState();

  // 组件即将出现时的生命周期方法
  aboutToAppear(): void {
    // 发送检查版本事件
    this.viewModel.sendEvent(new CheckVersionEvent());
  }

  // 定义构建方法
  build() {
    // 创建行布局
    Row() {
      // 创建左侧列布局
      Column() {
        // 如果存在更新版本
        if (this.aboutState.laterVersionExist) {
          // 创建带徽章的版本信息
          Badge({
            value: '',
            style: { badgeSize: 6, badgeColor: $r('app.color.about_badge_color') },
            position: BadgePosition.Right,
          }) {
            // 显示版本信息文本
            Text($r('app.string.version_information'))
              .fontWeight(FontWeight.Medium)
              .fontSize($r('sys.float.Body_L'))
              .fontColor($r('sys.color.font_primary'))
              .margin({ right: $r('sys.float.padding_level6') })
          }
        } else {
          // 显示普通版本信息文本
          Text($r('app.string.version_information'))
            .fontWeight(FontWeight.Medium)
            .fontSize($r('sys.float.Body_L'))
            .fontColor($r('sys.color.font_primary'))
        }

        // 显示当前版本号
        Text(AppStorage.get<BundleInfoData>('BundleInfoData')?.versionName as string)
          .margin({ top: $r('sys.float.padding_level2') })
          .fontWeight(FontWeight.Regular)
          .fontSize($r('sys.float.Subtitle_S'))
          .fontColor($r('sys.color.font_secondary'))
      }
      // 设置列的水平对齐方式为开始
      .alignItems(HorizontalAlign.Start)
      // 设置列的垂直对齐方式为居中
      .justifyContent(FlexAlign.Center)

      // 创建右侧行布局
      Row() {
        // 如果不在加载更新状态
        if (!this.aboutState.isLoadingUpdate) {
          // 显示版本状态文本
          Text(this.aboutState.laterVersionExist ? $r('app.string.updated_version') : $r('app.string.latest_version'))
            .fontWeight(FontWeight.Regular)
            .fontSize($r('sys.float.Subtitle_S'))
            .fontColor($r('sys.color.font_secondary'))
            .margin({ right: $r('sys.float.padding_level2') })
          // 显示右箭头图标
          SymbolGlyph($r('sys.symbol.chevron_right'))
            .fontSize($r('sys.float.Title_S'))
            .fontColor([$r('sys.color.icon_fourth')])
        } else {
          // 显示加载进度条
          LoadingProgress()
            .width($r('app.float.about_loadingProgress_size'))
            .height($r('app.float.about_loadingProgress_size'))
        }
      }
      // 设置点击事件
      .onClick(() => {
        // 如果存在更新版本则发送更新事件
        if (this.aboutState.laterVersionExist) {
          this.viewModel.sendEvent(new UpdateVersionEvent());
        }
      })
    }
    // 设置行宽度为100%
    .width('100%')
    // 设置行高度
    .height($r('app.float.about_item_card_height'))
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level7'))
    // 设置背景颜色
    .backgroundColor($r('sys.color.comp_background_list_card'))
    // 设置主轴对齐方式为两端对齐
    .justifyContent(FlexAlign.SpaceBetween)
    // 设置交叉轴对齐方式为居中
    .alignItems(VerticalAlign.Center)
    // 设置内边距
    .padding({
      left: $r('sys.float.padding_level6'),
      top: $r('sys.float.padding_level7'),
      bottom: $r('sys.float.padding_level7'),
      right: $r('sys.float.padding_level6'),
    })
  }
}
// 导入ArkTS工具包中的util模块
import { util } from '@kit.ArkTS';
// 导入基础服务包中的业务错误类型
import { BusinessError } from '@kit.BasicServicesKit';
// 导入日志工具
import Logger from '../util/Logger';

// 日志标签常量
const TAG = '[MockRequest]';

/**
 * 模拟数据请求类
 * 用于从本地资源文件中读取模拟数据
 * 提供异步数据获取功能，模拟真实的网络请求
 */
class MockRequest {
  /**
   * 调用模拟数据请求方法
   * 根据触发器名称读取对应的JSON模拟数据文件
   * @param trigger 触发器名称，用于指定要读取的模拟数据文件名
   * @returns Promise<T> 返回泛型类型的Promise，包含解析后的数据
   */
  public call<T>(trigger: string): Promise<T> {
    // 返回一个新的Promise对象
    return new Promise((resolve: (value: T | PromiseLike<T>) => void,
      reject: ((reason?: BusinessError) => void)) => {
      try {
        // 获取当前应用上下文
        const context: Context = getContext();
        // 同步读取原始文件内容，从mockdata目录下读取指定的JSON文件
        const result: Uint8Array = context.resourceManager.getRawFileContentSync(`mockdata/${trigger}.json`);
        // 创建UTF-8文本解码器，忽略BOM标记
        const textDecoder = util.TextDecoder.create('utf-8', { ignoreBOM: true });
        // 将字节数组解码为字符串
        const content: string = textDecoder.decodeToString(result, { stream: false });
        // 将JSON字符串解析为结果数据对象
        const jsonContent: ResultData<T> = JSON.parse(content) as ResultData<T>;
        // 记录信息日志（注意：这里的日志信息可能有误，应该是成功信息）
        Logger.info(TAG, `GetRawFileContent failed, cause: no ${trigger} json is configured.`);
        // 解析成功，返回数据部分
        resolve(jsonContent.data);
      } catch (error) {
        // 捕获异常，记录错误日志
        Logger.error(TAG, `GetRawFileContent failed, error code: ${error.code}, message: ${error.message}.`);
        // 拒绝Promise，返回错误
        reject(error);
      }
    });
  }
}

/**
 * 结果数据接口
 * 定义模拟数据文件的标准格式
 * @template T 数据类型泛型
 */
interface ResultData<T> {
  // 响应代码
  code: number;
  // 实际数据内容
  data: T;
  // 响应消息
  message: string;
}

// 创建模拟请求实例
const mockRequest = new MockRequest();

// 导出模拟请求实例作为默认导出
export default mockRequest;
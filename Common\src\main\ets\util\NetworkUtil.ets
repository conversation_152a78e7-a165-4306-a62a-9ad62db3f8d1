// 导入网络工具包中的连接模块
import { connection } from '@kit.NetworkKit';
// 导入日志工具
import Logger from './Logger';

// 日志标签常量
const TAG: string = '[NetworkUtil]';

/**
 * 网络工具类
 * 提供网络连接状态检查功能
 * 用于判断设备是否具有可用的默认网络连接
 */
export class NetworkUtil {
  /**
   * 检查是否有默认网络连接方法
   * 同步检查设备是否具有可用的默认网络连接
   * @returns 是否有默认网络连接
   */
  public static hasDefaultNet(): boolean {
    try {
      // 同步检查是否有默认网络连接
      return connection.hasDefaultNetSync();
    } catch (err) {
      // 记录检查默认网络失败错误日志
      Logger.error(TAG, `checkDefaultNet Failed. cause: ${err.code}`);
      // 发生异常时返回false
      return false;
    }
  }
}
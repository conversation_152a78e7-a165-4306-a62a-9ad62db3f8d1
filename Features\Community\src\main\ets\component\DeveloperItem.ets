// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';

// 使用Component装饰器定义开发者项目组件
@Component
export struct DeveloperItem {
  // 使用Prop装饰器定义发现内容属性
  @Prop discoverContent: DiscoverContent;

  // 定义构建方法
  build() {
    // 创建堆栈布局，内容对齐到底部
    Stack({ alignContent: Alignment.Bottom }) {
      // 创建图片组件
      Image($rawfile(this.discoverContent.mediaUrl))
        // 禁用拖拽
        .draggable(false)
        // 设置占位图片
        .alt($r('app.media.img_placeholder'))
        // 设置宽度为100%
        .width('100%')
        // 设置高度为100%
        .height('100%')
      // 创建列布局
      Column() {
        // 创建行布局
        Row() {
          // 创建标题文本
          Text(this.discoverContent.title)
            // 设置字体颜色为主要字体色
            .fontColor($r('sys.color.font_primary'))
            // 设置字体大小为Body_L
            .fontSize($r('sys.float.Body_L'))
            // 设置字体粗细为粗体
            .fontWeight(FontWeight.Bold)
          // 创建副标题文本
          Text(this.discoverContent.subTitle)
            // 设置底部边距
            .margin({ bottom: $r('sys.float.padding_level1') })
            // 设置字体颜色为主要字体色
            .fontColor($r('sys.color.font_primary'))
            // 设置字体大小为Caption_M
            .fontSize($r('sys.float.Caption_M'))
            // 设置字体粗细为常规
            .fontWeight(FontWeight.Regular)
        }
        // 设置主轴对齐方式为两端对齐
        .justifyContent(FlexAlign.SpaceBetween)
        // 设置交叉轴对齐方式为底部对齐
        .alignItems(VerticalAlign.Bottom)
        // 设置宽度为100%
        .width('100%')

        // 创建描述文本
        Text(this.discoverContent.desc)
          // 设置顶部边距
          .margin({ top: $r('sys.float.padding_level2') })
          // 设置字体颜色为次要字体色
          .fontColor($r('sys.color.font_secondary'))
          // 设置字体大小为Body_S
          .fontSize($r('sys.float.Body_S'))
          // 设置宽度为100%
          .width('100%')
          // 设置最大行数为2
          .maxLines(2)
          // 设置文本溢出处理为省略号
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          // 设置字体粗细为常规
          .fontWeight(FontWeight.Regular)
      }
      // 设置项目对齐方式为左对齐
      .alignItems(HorizontalAlign.Start)
      // 设置背景颜色为模糊背景色
      .backgroundColor($r('app.color.blur_bg'))
      // 启用渲染组
      .renderGroup(true)
      // 设置内边距
      .padding({
        top: $r('sys.float.padding_level6'),
        right: $r('sys.float.padding_level8'),
        bottom: $r('sys.float.padding_level8'),
        left: $r('sys.float.padding_level8'),
      })
    }
    // 设置点击效果为重度
    .clickEffect({ level: ClickEffectLevel.HEAVY })
    // 设置边框圆角
    .borderRadius($r('sys.float.corner_radius_level8'))
    // 启用裁剪
    .clip(true)
    // 设置背景颜色为列表卡片背景色
    .backgroundColor($r('sys.color.comp_background_list_card'))
  }
}
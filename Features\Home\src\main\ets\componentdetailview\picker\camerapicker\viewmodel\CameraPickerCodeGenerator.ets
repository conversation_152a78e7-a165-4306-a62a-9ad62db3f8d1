// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../../viewmodel/Attribute';
// 导入通用代码生成器接口，用于实现代码生成功能
import { CommonCodeGenerator } from '../../../../viewmodel/CommonCodeGenerator';
// 导入相机选择器媒体类型映射数据
import { pickerMediaType } from '../entity/CameraPickerMapping';

/**
 * 相机选择器代码生成器类
 * 实现通用代码生成器接口，用于生成相机选择器组件代码
 */
export class CameraPickerCodeGenerator implements CommonCodeGenerator {
  // 媒体类型代码字符串，默认使用默认媒体类型
  private mediaTypes: string = pickerMediaType.get('Default')!.code;

  /**
   * 生成相机选择器代码方法
   * 根据属性配置生成完整的相机选择器组件代码
   * @param _attributes 原始属性数组，包含组件配置信息
   * @returns 生成的相机选择器代码字符串
   */
  public generate(_attributes: OriginAttribute[]): string {
    // 遍历所有属性，根据属性名称设置对应的配置值
    _attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'mediaTypes':
          // 设置媒体类型
          this.mediaTypes = pickerMediaType.get(attribute.currentValue)?.code ?? this.mediaTypes;
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
    // 返回生成的完整相机选择器代码字符串
    return `// 相机选择器组件代码
// 导入相机和相机选择器模块
import { camera, cameraPicker } from '@kit.CameraKit';
// 导入提示操作模块
import { promptAction } from '@kit.ArkUI';
// 导入业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';

// 相机选择器组件
@Component
export struct CameraPickerComponent {
  // 媒体资源URI状态
  @State uri?: ResourceStr = undefined;
  // 媒体类型状态，默认为照片类型
  @State mediaType: cameraPicker.PickerMediaType = cameraPicker.PickerMediaType.PHOTO;
  // 是否已填充内容状态
  @State isFilled: boolean = false;
  // 相机数量状态
  @State cameraNum: number = 0;
  // 视频控制器
  private controller: VideoController = new VideoController();

  // 组件即将出现时的生命周期方法
  aboutToAppear() {
    // 获取应用上下文
    const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
    let cameraManager: camera.CameraManager;
    try {
      // 获取相机管理器实例
      cameraManager = camera.getCameraManager(context);
    } catch (err) {
      // 捕获并记录获取相机管理器失败的错误
      const error: BusinessError = err as BusinessError;
      console.error('GetCameraManager error');
      return;
    }
    // 获取支持的相机设备数组
    const cameraArray: camera.CameraDevice[] = this.getSupportedCameras(cameraManager);
    // 设置相机数量
    this.cameraNum = cameraArray.length;
  }

  // 构建组件UI方法
  build() {
    // 创建堆叠布局，内容居中显示
    Stack({ alignContent: Alignment.Center }) {
      // 根据媒体类型显示不同的预览组件
      if (this.mediaType === cameraPicker.PickerMediaType.VIDEO) {
        // 显示视频预览组件
        Video({ src: this.uri, controller: this.controller })
          // 设置视频尺寸
          .width('100%')
          .height('100%')
          // 设置视频适应方式为包含
          .objectFit(ImageFit.Contain)
          // 设置圆角边框
          .borderRadius($r('sys.float.corner_radius_level8'))
          // 视频准备完成时的回调
          .onPrepared(() => {
            this.controller.setCurrentTime(0.5, SeekMode.Accurate)
          })
      } else {
        // 显示图片预览组件
        Image(this.uri)
          // 设置图片尺寸
          .width('100%')
          .height('100%')
          // 设置图片适应方式为包含
          .objectFit(ImageFit.Contain)
          // 设置圆角边框
          .borderRadius($r('sys.float.corner_radius_level8'))
      }
      // 创建相机拍摄按钮
      Button('安全使用相机', { buttonStyle: ButtonStyleMode.NORMAL })
        // 根据是否已填充内容设置背景模糊样式
        .backgroundBlurStyle(this.isFilled ? BlurStyle.COMPONENT_REGULAR : BlurStyle.NONE,
          { adaptiveColor: AdaptiveColor.AVERAGE })
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为系统大号字体
        .fontSize($r('sys.float.Body_L'))
        // 根据是否已填充内容设置字体颜色
        .fontColor(this.isFilled ? $r('sys.color.font_on_primary') : $r('sys.color.font_emphasize'))
        // 设置按钮底部边距
        .margin({ bottom: $r('sys.float.padding_level10') })
        // 设置点击事件处理，启动相机选择器
        .onClick(async () => {
          // 检查设备是否有相机
          if (this.cameraNum === 0) {
            try {
              // 显示设备无相机的提示消息
              promptAction.showToast({ message: '该设备没有摄像头' });
            } catch (err) {
              // 捕获并记录显示Toast失败的错误
              const error: BusinessError = err as BusinessError;
              console.error(\`Show toast error, the code is \${error.code}, the message is \${error.message}\`);
            }
            return;
          }
          try {
            // 创建相机选择器配置，使用后置摄像头
            const pickerProfile: cameraPicker.PickerProfile = {
              cameraPosition: camera.CameraPosition.CAMERA_POSITION_BACK,
            };
            // 调用相机选择器进行拍摄
            const pickerResult: cameraPicker.PickerResult = await cameraPicker.pick(getContext(),
              ${this.mediaTypes}, pickerProfile);
              // 记录拍摄结果信息
              console.info(\`the pick pickerResult is: \${JSON.stringify(pickerResult)}\`);
            // 检查拍摄结果
            if (pickerResult.resultCode === 0) {
              // 拍摄成功，更新状态
              this.isFilled = true;
              this.uri = pickerResult.resultUri;
              this.mediaType = pickerResult.mediaType;
            } else {
              // 拍摄失败，记录错误日志
              console.error("the pick pickerResult:error");
            }
          } catch (error) {
            // 捕获并记录拍摄过程中的错误
            const err = error as BusinessError;
            console.error(\`\${err.code},\${err.message}\`);
          }
        })
    }
  }

  // 获取支持的相机设备方法
  getSupportedCameras(cameraManager: camera.CameraManager): camera.CameraDevice[] {
    // 初始化相机设备数组
    let cameras: camera.CameraDevice[] = [];
    try {
      // 获取支持的相机设备列表
      cameras = cameraManager.getSupportedCameras();
    } catch (error) {
      // 捕获并记录获取相机设备失败的错误
      const err = error as BusinessError;
      console.error('The getSupportedCameras call failed.');
    }
    return cameras;
  }
}`;
  }
}
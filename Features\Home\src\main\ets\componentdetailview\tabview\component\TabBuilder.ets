// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入标签页属性修改器，用于修改标签页组件属性
import { TabAttributeModifier } from '../viewmodel/TabAttributeModifier';
// 导入标签页描述器类型，用于描述标签页组件配置
import type { TabDescriptor } from '../viewmodel/TabDescriptor';

/**
 * 标签页组件构建器函数
 * 用于构建标签页组件，包含多个标签页内容
 * @param $$ 描述器包装对象，包含标签页组件配置信息
 */
@Builder
export function TabBuilder($$: DescriptorWrapper) {
  // 创建标签页组件实例
  TabComponent({ preview: $$.descriptor as TabDescriptor })
}

/**
 * 标签页组件结构体
 * 包含多个几何图形的标签页展示
 */
@Component
struct TabComponent {
  // 标签页描述器属性，包含组件配置信息
  @Prop preview: TabDescriptor;
  // 当前选中的标签页索引状态
  @State currentIndex: number = 0;

  @Builder
  tabBuilder(title: ResourceStr, targetIndex: number) {
    Column() {
      Text(title)
        .fontColor(this.currentIndex === targetIndex ? $r('sys.color.font_emphasize') : $r('sys.color.font_primary'))
        .fontWeight(FontWeight.Regular)
        .fontSize($r('sys.float.Caption_M'))
    }
    .width($r('app.float.tab_bar_width'))
    .height($r('app.float.tab_bar_height'))
    .justifyContent(FlexAlign.Center)
  }

  @Builder
  contentBuilder(text: string) {
    Column() {
      if (text === 'circle') {
        Circle()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.one_hundred_size') })
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'square') {
        Rect()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.one_hundred_size') })
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'triangle') {
        Polygon()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.one_hundred_size') })
          .points([[0, 100], [50, 0], [100, 100]])
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'rectangle') {
        Rect()
          .size({ width: $r('app.float.one_hundred_twenty_size'), height: $r('app.float.eighty_size') })
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'elliptical') {
        Ellipse()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.eighty_size') })
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'trapezium') {
        Polygon()
          .size({ width: $r('app.float.one_hundred_twenty_size'), height: $r('app.float.eighty_size') })
          .points([[0, 80], [20, 0], [100, 0], [120, 80]])
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'lozenge') {
        Polygon()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.one_hundred_size') })
          .points([[0, 50], [50, 0], [100, 50], [50, 100]])
          .fill($r('sys.color.icon_emphasize'))
      }
      if (text === 'hexagon') {
        Polygon()
          .size({ width: $r('app.float.one_hundred_size'), height: $r('app.float.one_hundred_size') })
          .points([[50, 0], [93.3, 25], [93.3, 75], [50, 100], [6.7, 75], [6.7, 25]])
          .fill($r('sys.color.icon_emphasize'))
      }
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .borderRadius($r('sys.float.corner_radius_level4'))
  }

  build() {
    Column() {
      Tabs({ controller: this.preview.controller, barPosition: this.preview.barPosition }) {
        TabContent() {
          this.contentBuilder('circle')
        }.tabBar(this.tabBuilder($r('app.string.circle'), 0))

        TabContent() {
          this.contentBuilder('square')
        }.tabBar(this.tabBuilder($r('app.string.square'), 1))

        TabContent() {
          this.contentBuilder('triangle')
        }.tabBar(this.tabBuilder($r('app.string.triangle'), 2))

        TabContent() {
          this.contentBuilder('rectangle')
        }.tabBar(this.tabBuilder($r('app.string.rectangle'), 3))

        TabContent() {
          this.contentBuilder('elliptical')
        }.tabBar(this.tabBuilder($r('app.string.elliptical'), 4))

        TabContent() {
          this.contentBuilder('trapezium')
        }.tabBar(this.tabBuilder($r('app.string.trapezium'), 5))

        TabContent() {
          this.contentBuilder('lozenge')
        }.tabBar(this.tabBuilder($r('app.string.lozenge'), 6))

        TabContent() {
          this.contentBuilder('hexagon')
        }.tabBar(this.tabBuilder($r('app.string.hexagon'), 7))
      }
      .attributeModifier(new TabAttributeModifier(this.preview))
      .backgroundColor($r('sys.color.comp_background_secondary'))
      .barMode(BarMode.Scrollable)
      .divider({ strokeWidth: '1px', color: $r('sys.color.comp_divider') })
      .onChange((index: number) => {
        this.currentIndex = index;
      })
    }
    .padding($r('sys.float.padding_level8'))
    .justifyContent(FlexAlign.Center)
    .backgroundImage($r('app.media.image_background'))
    .backgroundImageSize({ width: '100%', height: '100%' })
    .borderRadius($r('sys.float.corner_radius_level8'))
    .height('100%')
    .width('100%')
  }
}
<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>设计与开发应用介绍</title>
  <link rel="stylesheet" href="../common/css/banner.css">
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
  <link rel="preload" as="image" href="./image/banner.png">
</head>

<body>
  <div class="main-content">
    <!-- header -->
    <div class="header">
      <div class="title">设计与开发应用介绍</div>
      <div class="content image-cover">
        <img class="banner-img" src="./image/banner.png" alt="">
        <div class="image-words">
          <div>帮助开发者打造高端精致、简单易用、</div>
          <div>极致流畅、纯净安全的应用</div>
        </div>
      </div>
    </div>

    <!-- body -->
    <div class="chapter">
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">开发准备</span>
          </div>
        </div>
        <div class="develop-container">
          <div class="develop-title">准备与学习</div>
          <div class="develop-subtitle mg-t-16">了解ArkTS和ArkUI</div>
          <div class="develop-list">
            <div class="develop-list-item">
              <img class="develop-icon"
                src="./image/kaifa-03.png" />
              <div class="develop-text">
                <div class="list-title">ArkTS</div>
                <div class="list-body">鸿蒙生态的应用开发语言。</div>
              </div>
            </div>
            <div class="develop-list-item">
              <img class="develop-icon"
                src="./image/kaifa-04.png" />
              <div class="develop-text">
                <div class="list-title">ArkUI</div>
                <div class="list-body">
                  构建分布式应用界面的声明式UI开发框架。
                </div>
              </div>
            </div>
          </div>
          <div class="develop-subtitle">开发套件-DevEco Studio</div>
          <div class="develop-list">
            <div class="develop-list-item">
              <img class="develop-icon"
                src="./image/kaifa-01.png" />
              <div class="develop-text">
                <div class="list-title">DevEco Studio</div>
                <div class="list-body">
                  一站式的HarmonyOS应用及元服务开发平台。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">赋能套件</span>
          </div>
        </div>
        <div class="develop-container">
          <div class="develop-title">赋能套件</div>
          <div class="step-container">
            <div class="step-container">
              <div class="step">
                <div class="step-title">
                  <div>感知</div>
                  <div>阶段</div>
                </div>
                <div class="dot">
                  <div class="path"></div>
                </div>
                <div class="step-text">
                  HarmonyOS白皮书、HarmonyOS应用开发知识地图
                </div>
              </div>
              <div class="step">
                <div class="step-title">
                  <div>学习</div>
                  <div>阶段</div>
                </div>
                <div class="dot">
                  <div class="path"></div>
                </div>
                <div class="step-text">
                  HarmonyOS第一课、HarmonyOS应用开发快速入门、Codelabs
                </div>
              </div>
              <div class="step">
                <div class="step-title">
                  <div>开发</div>
                  <div>阶段</div>
                </div>
                <div class="dot">
                  <div class="path"></div>
                </div>
                <div class="step-text">
                  开发指南、API参考、示例代码、最佳实践
                </div>
              </div>
              <div class="step">
                <div class="step-title">
                  <div>学习</div>
                  <div>阶段</div>
                </div>
                <div class="dot"></div>
                <div class="step-text">开发者社区&amp;FAQ</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">应用体验设计</span>
          </div>
        </div>
        <div class="swiper">
          <div class="card-wrap" style="margin-left: 0px">
            <div class="card-item">
              <img class="card-icon"
                src="./image/swiperItem1.png"
                alt="" draggable="false" />
              <div class="card-title">新控件</div>
              <div class="card-desc">
                提供基于 HarmonyOS 版本风格的控件样式及设计规范，帮助你快速了解控件设计和基础能力。
              </div>
            </div>
            <div class="card-item">
              <img class="card-icon"
                src="./image/swiperItem2.png"
                alt="" draggable="false" />
              <div class="card-title">实况窗设计</div>
              <div class="card-desc">
                实况窗设计包含了卡片态、胶囊态、沉浸态，帮助用户聚焦进行中任务、方便快速查看和即时处理。
              </div>
            </div>
            <div class="card-item">
              <img class="card-icon"
              src="./image/swiperItem4.png"
              alt="" draggable="false" />
              <div class="card-title">一键登录</div>
              <div class="card-desc">
                华为账号提供登录设计规范，保障 HarmonyOS
                应用拥有简单易用、高效一致、快速安全的登录体验。
              </div>
            </div>
            <div class="card-item">
              <img class="card-icon"
              src="./image/swiperItem5.png"
              alt="" draggable="false" />
              <div class="card-title">元服务设计</div>
              <div class="card-desc">
                轻量高效、即点即用，多形态的组件构成样式为用户提供丰富便捷的应用服务。
              </div>
            </div>
            <div class="card-item">
              <img class="card-icon"
              src="./image/swiperItem6.png"
              alt="" draggable="false" />
              <div class="card-title">多窗</div>
              <div class="card-desc">
                包含悬浮窗、分屏不同的窗口形态，为你提供灵活高效的多任务并行体验。
              </div>
            </div>
            <div class="card-item">
              <img class="card-icon"
              src="./image/swiperItem8.png"
              alt="" draggable="false" />
              <div class="card-title">分享</div>
              <div class="card-desc">
                为各场景的内容分享体验提供设计规范，帮助你了解系统分享能力。
              </div>
            </div>
          </div>
          <div class="swiper-pagination">
            <span class="swiper-point swiper-point-selected" style="color-scheme: light dark;"></span>
            <span class="swiper-point" style="color-scheme: light dark;"></span>
            <span class="swiper-point" style="color-scheme: light dark;"></span>
            <span class="swiper-point" style="color-scheme: light dark;"></span>
            <span class="swiper-point" style="color-scheme: light dark;"></span>
            <span class="swiper-point" style="color-scheme: light dark;"></span>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">应用架构</span>
          </div>
        </div>
        <div class="section-container">
          <div class="bg-framework">
            <img class="container-image"
              src="./image/framework.png"
              alt="" />
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">功能开发</span>
          </div>
        </div>
        <div class="list-card">
          <div class="list-card-item">
            <div class="item-word">
              <span class="item-title">文档</span>
              <span class="item-desc">探索最新操作文档，掌握开发、上架、审核全流程。</span>
            </div>
            <span class="detail-box" href="article_design_1">详情</span>
          </div>
          <div class="list-card-item">
            <div class="item-word">
              <span class="item-title">学堂</span>
              <span class="item-desc">学、练、考、证全流程服务，让你快速成为 HarmonyOS 人才。</span>
            </div>
            <span class="detail-box" href="article_design_2">详情</span>
          </div>
          <div class="list-card-item">
            <div class="item-word">
              <span class="item-title">社区</span>
              <span class="item-desc">提出你的问题，与开发者深入交流，同步探索热门话题。</span>
            </div>
            <span class="detail-box" href="article_design_3">详情</span>
          </div>
          <div class="list-card-item">
            <div class="item-word">
              <span class="item-title">活动</span>
              <span class="item-desc">与专家深度交流，结识行业大咖，了解一手资讯。</span>
            </div>
            <span class="detail-box" href="article_design_4">详情</span>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title title">
          <div class="title-text">
            <span class="gradient gradient-blue">应用测试</span>
          </div>
        </div>
        <div class="testing-card">
          <img class="card-icon"
            src="./image/kf-testing.png"
            alt="" />
          <div class="card-title">DevEco Testing</div>
          <div class="card-desc">
            DevEco Testing
            是一站式的应用测试服务平台。为你提供自动化测试框架，及稳定性、性能等专项测试服务，覆盖应用测试全周期，助力打造高品质应用。
          </div>
        </div>
      </div>
    </div>

    <!-- footer -->
    <div class="footer">
      <img class="footerImg"
        src="../common/image/f_icon.png"
        alt="" />
    </div>
  </div>
</body>
<script src="../common/js/banner.js"></script>
<script type="module" src="./index.js"></script>

</html>
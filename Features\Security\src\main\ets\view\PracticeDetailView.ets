// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入方舟UI工具包中的提示操作
import { promptAction } from '@kit.ArkUI';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的通用常量、加载状态、顶部导航视图和窗口工具
import { CommonConstants, LoadingStatus, TopNavigationView, WindowUtil } from '@ohos/common';
// 导入通用业务模块中的基础详情组件和示例详情参数
import { BaseDetailComponent, SampleDetailParams } from '@ohos/commonbusiness';
// 导入示例组件
import { SampleComponent } from '../component/SampleComponent';
// 导入示例详情数据和示例详情状态类型
import type { SampleDetailData, SampleDetailState } from '../viewmodel/SampleDetailState';
// 导入示例详情页面视图模型和相关事件
import {
  InitSampleDetailEvent,
  PopEvent,
  SampleDetailPageVM,
  SetIndexEvent,
} from '../viewmodel/SampleDetailPageVM';

// 使用Builder装饰器定义示例详情视图构建器函数
@Builder
export function SampleDetailViewBuilder() {
  // 创建示例详情视图
  SampleDetailView()
}

// 使用Component装饰器定义示例详情视图组件
@Component
export struct SampleDetailView {
  // 使用StorageProp和Watch装饰器获取系统颜色模式并监听变化
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 定义视图模型实例
  viewModel: SampleDetailPageVM = SampleDetailPageVM.getInstance();
  // 使用State装饰器定义示例详情状态
  @State sampleDetailState: SampleDetailState = this.viewModel.getState();
  // 使用State装饰器定义加载状态
  @State loadingStatus: LoadingStatus = LoadingStatus.IDLE;
  // 使用Provide装饰器定义当前索引
  @Provide currentIndex: number = -1;
  // 定义示例卡片ID私有变量
  private sampleCardId: number = -1;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 处理颜色模式变化
    this.handleColorModeChange();
  }

  // 定义处理颜色模式变化的方法
  handleColorModeChange() {
    // 判断是否为系统深色模式
    const isSystemDark: boolean = (this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this), isSystemDark);
  }

  // 定义返回操作的私有方法
  private onBack(): void {
    // 如果没有按下返回键
    if (!this.sampleDetailState.isBackPressed) {
      // 发送弹出事件
      this.viewModel.sendEvent(new PopEvent());
    }
  }

  // 使用Builder装饰器定义示例详情构建器
  @Builder
  SampleDetailBuilder() {
    // 创建滑动容器
    Swiper() {
      // 遍历示例详情数据
      ForEach(this.sampleDetailState.sampleDatas, (item: SampleDetailData, index: number) => {
        // 创建示例组件
        SampleComponent({
          singleSampleData: item,
          sampleIndex: index,
          showIndicator: this.sampleDetailState.sampleCount > 1,
        })
      }, (item: SampleDetailData) => item.id.toString())
    }
    // 设置滑动容器权重为1
    .layoutWeight(1)
    // 设置滑动容器宽度为100%
    .width('100%')
    // 设置滑动容器顶部内边距
    .padding({ top: (this.globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT) })
    // 根据示例数量设置指示器显示
    .indicator(this.sampleDetailState.sampleCount > 1)
    // 设置滑动容器当前索引
    .index(this.currentIndex)
    // 根据条件禁用滑动
    .disableSwipe(this.sampleDetailState.sampleCount === 1 || this.sampleDetailState.installingStatus ||
    this.sampleDetailState.downloadingStatus)
    // 设置滑动容器索引变化事件
    .onChange((index: number) => {
      // 发送设置索引事件
      this.viewModel.sendEvent(new SetIndexEvent(index));
      // 更新当前索引
      this.currentIndex = index;
    })
    // 设置滑动手势
    .gesture(
      SwipeGesture({ direction: SwipeDirection.Horizontal })
        .onAction(() => {
          // 如果当前示例正在下载且示例数量大于1
          if (this.sampleDetailState.sampleDatas[this.currentIndex].sampleCard.downloadProgress >= 0 &&
            this.sampleDetailState.sampleCount > 1) {
            // 显示滑动手势提示
            promptAction.showToast({ message: $r('app.string.swiper_gesture') });
          }
        })
    )
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建顶部导航视图
    TopNavigationView({
      topNavigationData: {
        title: $r('app.string.sample_title'),
        onBackClick: () => {
          // 调用返回操作方法
          this.onBack();
        }
      },
    })
  }

  // 定义构建方法
  build() {
    // 创建导航目标
    NavDestination() {
      // 创建基础详情组件
      BaseDetailComponent({
        detailContentView: () => {
          // 调用示例详情构建器
          this.SampleDetailBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        loadingStatus: this.sampleDetailState.loadingStatus,
      })
    }
    // 设置导航目标准备就绪事件
    .onReady((cxt: NavDestinationContext) => {
      // 获取导航参数
      const params = cxt.pathInfo.param as SampleDetailParams;
      // 设置示例卡片ID
      this.sampleCardId = params.sampleCardId;
      // 设置当前索引
      this.currentIndex = params.currentIndex;
      // 发送初始化示例详情事件
      this.viewModel.sendEvent(new InitSampleDetailEvent(this.sampleCardId, params.currentIndex));
    })
    // 设置导航目标返回按下事件
    .onBackPressed(() => {
      // 调用返回操作方法
      this.onBack();
      // 返回true表示已处理
      return true;
    })
    // 设置导航目标即将显示事件
    .onWillShow(() => {
      // 重置返回按下状态
      this.sampleDetailState.isBackPressed = false;
    })
    // 设置导航目标即将隐藏事件
    .onWillHide(() => {
      // 重置返回按下状态
      this.sampleDetailState.isBackPressed = false;
    })
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置导航目标内边距
    .padding({ bottom: this.globalInfoModel.naviIndicatorHeight, top: this.globalInfoModel.statusBarHeight })
    // 设置导航目标宽度为100%
    .width('100%')
    // 设置导航目标高度为100%
    .height('100%')
    // 设置导航目标背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }
}
// 导入通用颜色映射、数字映射和字符串映射类
import { CommonColorMapping, CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

/**
 * 样式映射类
 * 用于定义进度条样式的代码字符串和实际配置值
 */
class StyleMapping {
  // 样式代码字符串，用于代码生成
  public readonly code: string;
  // 样式配置值，用于实际设置
  public readonly value: CommonProgressStyleOptions;

  /**
   * 构造函数
   * @param code 样式代码字符串
   * @param value 样式配置值
   */
  constructor(code: string, value: CommonProgressStyleOptions) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 进度条样式映射数据
 * 将样式名称映射到具体的样式配置
 */
export const progressStyleMapData: Map<string, StyleMapping> = new Map([
  // 线性样式：线宽4，启用平滑效果
  ['LinearStyle', new StyleMapping('{ strokeWidth: 4, enableSmoothEffect: true }',
    { strokeWidth: 4, enableSmoothEffect: true } as LinearStyleOptions)],
  // 环形进度样式：线宽4，刻度数20，刻度宽度4
  ['ProgressStyle', new StyleMapping('{ strokeWidth: 4, scaleCount: 20, scaleWidth: 4 }',
    { strokeWidth: 4, scaleCount: 20, scaleWidth: 4 } as ProgressStyleOptions)],
  // 环形样式1：线宽4，启用扫描效果
  ['RingStyle1', new StyleMapping('{ strokeWidth: 4, enableScanEffect: true }',
    { strokeWidth: 4, enableScanEffect: true } as ProgressStyleOptions)],
  // 环形样式2：线宽4，启用阴影效果
  ['RingStyle2',
    new StyleMapping('{ strokeWidth: 4, shadow: true }', { strokeWidth: 4, shadow: true } as RingStyleOptions)],
  // 椭圆样式：线宽4，启用平滑效果
  ['EclipseStyle', new StyleMapping('{ strokeWidth: 4, enableSmoothEffect: true }',
    { strokeWidth: 4, enableSmoothEffect: true } as EclipseStyleOptions)],
  // 刻度环样式：线宽4，刻度数15，刻度宽度50
  ['ScaleRingStyle', new StyleMapping('{ strokeWidth: 4, scaleCount: 15, scaleWidth: 50 }',
    { strokeWidth: 4, scaleCount: 15, scaleWidth: 50 } as ScaleRingStyleOptions)],
  // 胶囊样式：边框宽度1，禁用扫描效果，灰色字体，显示默认百分比
  ['CapsuleStyle', new StyleMapping(`{
          borderWidth: 1,
          enableScanEffect: false,
          fontColor: Color.Gray,
          showDefaultPercentage: true,
        }`, {
    borderWidth: 1,
    enableScanEffect: false,
    fontColor: Color.Gray,
    showDefaultPercentage: true,
  } as CapsuleStyleOptions)],
  // 默认样式：线性样式
  ['Default', new StyleMapping('{ strokeWidth: 4, enableSmoothEffect: true }',
    { strokeWidth: 4, enableSmoothEffect: true } as LinearStyleOptions)],
]);

/**
 * 进度条类型映射类
 * 用于定义进度条类型的代码字符串和实际枚举值
 */
class ProgressTypeMap {
  // 类型代码字符串，用于代码生成
  public readonly code: string;
  // 类型枚举值，用于实际设置
  public readonly value: ProgressType;

  /**
   * 构造函数
   * @param code 类型代码字符串
   * @param value 类型枚举值
   */
  constructor(code: string, value: ProgressType) {
    this.code = code;
    this.value = value;
  }
}

/**
 * 进度条类型映射数据
 * 将样式名称映射到具体的进度条类型
 */
export const progressTypeMapData: Map<string, ProgressTypeMap> = new Map([
  // 线性样式对应线性进度条类型
  ['LinearStyle', new ProgressTypeMap('ProgressType.Linear', ProgressType.Linear)],
  // 进度样式对应环形进度条类型
  ['ProgressStyle', new ProgressTypeMap('ProgressType.Ring', ProgressType.Ring)],
  // 环形样式1对应环形进度条类型
  ['RingStyle1', new ProgressTypeMap('ProgressType.Ring', ProgressType.Ring)],
  // 环形样式2对应环形进度条类型
  ['RingStyle2', new ProgressTypeMap('ProgressType.Ring', ProgressType.Ring)],
  // 椭圆样式对应椭圆进度条类型
  ['EclipseStyle', new ProgressTypeMap('ProgressType.Eclipse', ProgressType.Eclipse)],
  // 刻度环样式对应刻度环进度条类型
  ['ScaleRingStyle', new ProgressTypeMap('ProgressType.ScaleRing', ProgressType.ScaleRing)],
  // 胶囊样式对应胶囊进度条类型
  ['CapsuleStyle', new ProgressTypeMap('ProgressType.Capsule', ProgressType.Capsule)],
  // 默认类型为线性进度条
  ['Default', new ProgressTypeMap('ProgressType.Linear', ProgressType.Linear)],
]);

/**
 * 进度条数值映射数据
 * 定义进度条的默认数值配置
 */
export const progressValueMapData: Map<string, CommonNumberMapping> = new Map([
  // 默认进度值为10
  ['Default', new CommonNumberMapping('10', 10)],
]);

/**
 * 进度条颜色映射数据
 * 定义进度条的默认颜色配置
 */
export const progressColorMapData: Map<string, CommonColorMapping> = new Map([
  // 默认颜色为蓝色
  ['Default', new CommonColorMapping('rgba(0,85,255,1.00)', 'rgba(0,85,255,1.00)')],
]);

/**
 * 进度条类别映射数据
 * 定义进度条的类别类型（普通进度条或加载进度条）
 */
export const progressKindMapData: Map<string, CommonStringMapping> = new Map([
  // 普通进度条类别
  ['Progress', new CommonStringMapping('Progress', 'Progress')],
  // 加载进度条类别
  ['LoadingProgress', new CommonStringMapping('LoadingProgress', 'LoadingProgress')],
  // 默认为加载进度条类别
  ['Default', new CommonStringMapping('LoadingProgress', 'LoadingProgress')],
]);
// 导入断点类型枚举
import { BreakpointTypeEnum } from '../model/GlobalInfoModel';
// 导入断点类型工具类
import { BreakpointType } from '../util/BreakpointSystem';

/**
 * 加载失败视图构建函数
 * 显示加载失败状态页面，包含失败图标和错误提示文本
 * 支持响应式设计和点击重新加载功能
 * @param breakpoint 当前断点类型
 * @param handleReload 重新加载处理函数，可选参数
 */
@Builder
export function LoadingFailedView(breakpoint: BreakpointTypeEnum, handleReload?: () => void) {
  // 水平布局容器
  Row() {
    // 垂直布局容器，包含失败图标和错误文本
    Column() {
      // 失败状态图标
      Image($r('app.media.ic_failure'))
        // 禁用拖拽功能
        .draggable(false)
        // 根据断点设置不同的宽度
        .width(new BreakpointType({
          // 小屏幕断点使用小尺寸
          sm: $r('app.float.failure_size_sm'),
          // 中屏幕断点使用中等尺寸
          md: $r('app.float.failure_size_md'),
          // 大屏幕断点使用中等尺寸
          lg: $r('app.float.failure_size_md'),
        }).getValue(breakpoint))
        // 设置宽高比为1:1，保持正方形
        .aspectRatio(1)
      // 服务器错误提示文本
      Text($r('app.string.server_error'))
        // 设置字体颜色为系统三级字体颜色
        .fontColor($r('sys.color.font_tertiary'))
        // 设置字体大小为Body_M
        .fontSize($r('sys.float.Body_M'))
        // 设置顶部外边距为4级内边距
        .margin({ top: $r('sys.float.padding_level4') })
    }
  }
  // 设置点击事件，调用重新加载处理函数（如果存在）
  .onClick(() => handleReload?.())
  // 设置宽度为100%
  .width('100%')
  // 设置高度为100%
  .height('100%')
  // 设置背景颜色为系统次要背景色
  .backgroundColor($r('sys.color.background_secondary'))
  // 设置子组件居中对齐
  .justifyContent(FlexAlign.Center)
}
// 导入示例类型枚举
import { SampleTypeEnum } from '../common/SampleConstant';

// 导出单个示例数据类
export class SingleSampleData {
  // 定义单个示例数据ID
  public id: number = 0;
  // 定义单个示例数据标题
  public title: string = '';
  // 定义单个示例数据描述
  public desc: string = '';
  // 定义单个示例数据是否预安装
  public preInstalled: boolean = false;
  // 定义单个示例数据示例类型
  public sampleType: SampleTypeEnum = SampleTypeEnum.COMMON_SAMPLE;
  // 定义单个示例数据是否收藏
  public isFavorite: boolean = false;
  // 定义单个示例数据媒体类型
  public mediaType: number = 0;
  // 定义单个示例数据媒体URL
  public mediaUrl: string = '';
  // 定义单个示例数据原始URL
  public originalUrl: string = '';
  // 定义单个示例数据模块名称
  public moduleName: string = '';
  // 定义单个示例数据能力名称
  public abilityName: string = '';
}

// 导出示例卡片详情类
export class SampleCardDetail {
  // 定义示例卡片详情ID
  public id: number = 0;
  // 定义示例卡片详情分类类型
  public categoryType: number = 0;
  // 定义示例卡片详情示例详情数组
  public sampleDetail: SingleSampleData[] = [];
}
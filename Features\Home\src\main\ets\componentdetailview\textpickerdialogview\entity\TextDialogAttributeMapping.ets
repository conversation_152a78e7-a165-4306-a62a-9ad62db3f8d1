// 导入通用布尔映射和通用数字映射，用于文本选择器对话框属性配置
import { CommonBoolMapping, CommonNumberMapping } from '../../common/entity/CommonMapData';

// 导出项目高度映射数据，用于配置选择器项目的高度
export const itemHeightMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('56', 56)],
])

// 导出循环映射数据，用于配置选择器是否支持循环滚动
export const canLoopMapData: Map<string, CommonBoolMapping> = new Map([
  ['Default', new CommonBoolMapping('true', true)],
])

// 导出选择器数据数组，包含水果选项
export const pickerData: string[] = ['apple', 'orange', 'peach', 'grape', 'banana'];

// 导出选择器数据代码字符串，用于代码生成
export const pickerDataCode: string = `['apple', 'orange', 'peach', 'grape', 'banana']`;
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的相关类型和工具
import {
  BreakpointTypeEnum,
  LoadingStatus,
  Logger,
  PageContext,
  RequestErrorCode,
  StatusBarColorType,
  WindowUtil,
} from '@ohos/common';
// 导入通用业务模块中的横幅数据类型
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的相关类型和组件
import {
  ArticleDetailParams,
  BaseHomeEventParam,
  BaseHomeEventType,
  BaseHomeViewModel,
  TabBarType,
  TAB_CONTENT_STATUSES,
} from '@ohos/commonbusiness';
// 导入发现内容和发现数据类型
import type { DiscoverContent, DiscoverData } from '../model/DiscoverData';
// 导入发现模型
import { DiscoverModel } from '../model/DiscoverModel';
// 导入探索状态
import { ExplorationState } from './ExplorationState';

// 定义标签常量
const TAG = '[ExplorationViewModel]';

// 导出探索视图模型类，继承基础首页视图模型
export class ExplorationViewModel extends BaseHomeViewModel<ExplorationState> {
  // 定义私有静态实例
  private static instance: ExplorationViewModel;
  // 定义私有模型
  private model: DiscoverModel = DiscoverModel.getInstance();

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数
    super(new ExplorationState());
    // 设置顶部导航数据标题
    this.state.topNavigationData.title = $r('app.string.community_name');
  }

  // 定义获取实例的公共静态方法
  public static getInstance(): ExplorationViewModel {
    // 如果实例不存在
    if (!ExplorationViewModel.instance) {
      // 创建新实例
      ExplorationViewModel.instance = new ExplorationViewModel();
    }
    // 返回实例
    return ExplorationViewModel.instance;
  }

  // 定义发送事件的方法
  sendEvent<T>(eventParam: ExplorationEventParam<T>): void | boolean {
    // 获取事件类型
    const eventType: ExplorationEventType | BaseHomeEventType = eventParam.type;
    // 如果事件类型为加载发现页面
    if (eventType === ExplorationEventType.LOAD_DISCOVERY_PAGE) {
      // 返回加载发现列表方法
      return this.loadDiscoverList();
    // 如果事件类型为跳转详情
    } else if (eventType === ExplorationEventType.JUMP_DETAIL_DETAIL) {
      // 返回跳转详情视图方法
      return this.jumpDetailView(eventParam.param as DiscoverContent);
    } else {
      // 调用父类发送事件方法
      return super.sendEvent(eventParam as BaseHomeEventParam<T>);
    }
  }

  // 定义受保护的加载发现列表方法
  protected loadDiscoverList(): void {
    // 判断是否为深色模式
    const isDark: boolean = AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    // 设置加载状态为加载中
    this.state.loadingModel.loadingStatus = LoadingStatus.LOADING;
    // 设置顶部导航数据标题颜色
    this.state.topNavigationData.titleColor = isDark ? StatusBarColorType.WHITE : StatusBarColorType.BLACK;
    // 设置顶部导航数据模糊效果为false
    this.state.topNavigationData.isBlur = false;
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(), isDark);
    // 获取发现页面数据
    this.model.getDiscoveryPage()
      // 处理成功响应
      .then((result: DiscoverData) => {
        // 遍历横幅信息
        result.bannerInfos?.forEach((item: BannerData) => {
          // 设置标签视图类型
          item.tabViewType = TabBarType.PRACTICE;
        });
        // 设置横幅状态数据数组
        this.state.bannerState.bannerResource.setDataArray([...(result.bannerInfos || [])]);
        // 设置发现数据
        this.state.discoveryData = result.discoveryData;
        // 获取全局信息模型
        const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
        // 如果当前断点不是LG或XL
        if (globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.LG &&
          globalInfoModel.currentBreakpoint !== BreakpointTypeEnum.XL) {
          // 设置顶部导航数据标题颜色为白色
          this.state.topNavigationData.titleColor = StatusBarColorType.WHITE;
          // 更新状态栏颜色为深色
          WindowUtil.updateStatusBarColor(getContext(), true);
          // 设置标签内容状态
          TAB_CONTENT_STATUSES[TabBarType.PRACTICE] = true;
        }
        // 设置加载状态为成功
        this.state.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
        // 记录信息日志
        Logger.info(TAG, `Request DiscoveryPage Success`);
      })
      // 处理错误响应
      .catch((error: BusinessError) => {
        // 更新状态栏颜色
        WindowUtil.updateStatusBarColor(getContext(), isDark);
        // 设置标签内容状态
        TAB_CONTENT_STATUSES[TabBarType.PRACTICE] = isDark;
        // 如果错误代码为网络连接失败
        if (error.code === RequestErrorCode.ERROR_NETWORK_CONNECT_FAILED) {
          // 设置加载状态为无网络
          this.state.loadingModel.loadingStatus = LoadingStatus.NO_NETWORK;
        } else {
          // 设置加载状态为失败
          this.state.loadingModel.loadingStatus = LoadingStatus.FAILED;
        }
      });
  }

  // 定义受保护的跳转详情视图方法
  protected jumpDetailView(content: DiscoverContent): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 定义路径栈
    const pathStack: PageContext =
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('explorationPageContext')! :
        AppStorage.get('pageContext') as PageContext;
    // 定义文章参数
    const articleParam: ArticleDetailParams = {
      id: content.id,
      isArticle: true,
      title: content.title,
      detailsUrl: content.detailsUrl,
    };
    // 打开页面
    pathStack.openPage({
      routerName: 'ArticleDetailView',
      param: articleParam,
    }, true);
  }
}

// 导出探索事件类型枚举
export enum ExplorationEventType {
  // 跳转详情
  JUMP_DETAIL_DETAIL = 'jumpDetailView',
  // 加载发现页面
  LOAD_DISCOVERY_PAGE = 'loadDiscoverList',
}

// 导出探索事件参数接口
export interface ExplorationEventParam<T> {
  // 事件类型
  type: ExplorationEventType | BaseHomeEventType;
  // 参数
  param: T;
}
// 导入通用常量
import { CommonConstants } from '../constant/CommonConstants';

/**
 * 空内容视图构建函数
 * 用于显示空状态页面，包含图片和描述文本
 * @param imgSrc 空状态图片资源
 * @param description 描述文本资源字符串
 */
@Builder
export function EmptyContentView(imgSrc: Resource, description: ResourceStr) {
  // 垂直布局容器，设置子组件间距
  Column({ space: CommonConstants.SPACE_8 }) {
    // 空状态图片
    Image(imgSrc)
      // 禁用拖拽功能
      .draggable(false)
      // 设置图片尺寸为空内容图片大小
      .size({ width: $r('app.float.empty_content_image_size'), height: $r('app.float.empty_content_image_size') })
    // 描述文本
    Text(description)
      // 设置字体颜色为系统次要文本颜色
      .fontColor($r('sys.color.ohos_id_color_text_secondary'))
      // 设置字体大小为Body_M
      .fontSize($r('sys.float.Body_M'))
  }
  // 设置子组件水平居中对齐
  .alignItems(HorizontalAlign.Center)
  // 设置子组件垂直居中对齐
  .justifyContent(FlexAlign.Center)
  // 设置背景颜色为系统次要背景色
  .backgroundColor($r('sys.color.background_secondary'))
  // 设置宽度为100%
  .width('100%')
  // 设置布局权重为1，占满剩余空间
  .layoutWeight(1)
}
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入ArkUI工具包中的曲线和窗口模块
import { curves, window } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误和设置模块
import { BusinessError, settings } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型、日志记录器和通用常量
import { type GlobalInfoModel, Logger, CommonConstants } from '@ohos/common';
// 导入通用模块中的断点类型枚举、屏幕方向、Web节点控制器、Web工具和窗口工具
import { BreakpointTypeEnum, ScreenOrientation, WebNodeController, WebUtil, WindowUtil } from '@ohos/common';
// 导入代码预览组件
import { CodePreviewComponent } from '../component/CodePreviewComponent';
// 导入代码预览JavaScript工具
import { CodePreviewJSUtil } from '../util/CodePreviewJSUtil';
// 导入代码预览事件和代码预览页面视图模型
import { CodePreviewEvent, CodePreviewPageVM } from '../viewmodel/CodePreviewPageVM';
// 导入代码预览状态类型定义
import type { CodePreviewState } from '../viewmodel/CodePreviewState';
// 导入代码视图参数
import { CodeViewParams } from '../viewmodel/ComponentDetailPageVM';

// 定义标签常量
const TAG: string = '[CodePreview]';
// 定义方向锁定开启常量
const ORIENTATION_LOCK_ON: string = '0';

// 使用Component装饰器定义代码预览组件
@Component
export struct CodePreview {
  // 使用StorageLink装饰器链接全局信息模型
  @StorageLink('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp装饰器获取系统颜色模式
  @StorageProp('systemColorMode') systemColorMode: ConfigurationConstant.ColorMode = AppStorage.get('systemColorMode')!;
  // 使用State装饰器定义Web节点控制器状态
  @State webNodeController?: WebNodeController = undefined;
  // 使用State装饰器定义焦点状态
  @State isFocus: boolean = false;
  // 使用State装饰器定义返回状态
  @State isBack: boolean = false;
  // 使用State装饰器定义方向锁定改变状态
  @State isOrientationLockChange: boolean = false;
  // 定义私有视图模型
  private viewModel?: CodePreviewPageVM;
  // 使用State装饰器定义代码预览状态
  @State codePreviewState?: CodePreviewState = this.viewModel?.getState();
  // 定义私有开始屏幕方向
  private beginScreenOrientation: string = ScreenOrientation.PORTRAIT;
  // 定义私有开始断点
  private beginBreakpoint: BreakpointTypeEnum = this.globalInfoModel.currentBreakpoint;
  // 定义私有开始方向锁定状态
  private beginOrientationLockState?: string;
  // 定义私有代码字符串
  private code: string = '';
  // 定义私有组件名称
  private componentName: string = '';

  // 定义返回页面方法
  onBackPage(): void {
    // 设置返回状态为true
    this.isBack = true;
    // 更新状态栏颜色
    WindowUtil.updateStatusBarColor(getContext(this),
      this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
    // 运行JavaScript
    this.javascriptRun();
    // 移除Web节点控制器
    this.webNodeController?.remove();
    // 重置Web节点控制器
    this.webNodeController = undefined;
    // 重置屏幕方向
    this.resetScreenOrientation();
    // 预完成动画
    this.preFinishAnimation();
    // 执行动画并弹出页面
    animateTo({ curve: curves.interpolatingSpring(0, 1, 342, 38) }, () => {
      this.viewModel?.pop(false);
    });
  }

  // 定义JavaScript运行方法
  javascriptRun(): void {
    // 定义切换到小屏方法名
    const toSmallScreenMethod: string = 'toSmallScreen()';
    // 定义改变颜色模式方法名
    const changeColorModeMethod: string = 'changeColorMode(%param)';
    // 将系统颜色模式转换为JSON字符串
    const changeColorModeParams: string = JSON.stringify(this.systemColorMode);
    // 运行切换到小屏的JavaScript方法
    CodePreviewJSUtil.codeViewRunJS(toSmallScreenMethod);
    // 运行改变颜色模式的JavaScript方法
    CodePreviewJSUtil.codeViewRunJS(changeColorModeMethod, changeColorModeParams);
  }

  // 定义组件即将出现时的回调方法
  aboutToAppear(): void {
    // 获取代码预览页面视图模型实例
    this.viewModel = CodePreviewPageVM.getInstance();
    // 初始化方向状态
    this.initOrientationState();
    // 发送初始化事件
    this.viewModel.sendEvent(CodePreviewEvent.INIT);
  }

  // 定义组件即将消失时的回调方法
  aboutToDisappear(): void {
    // 设置主窗口方向为未指定
    WindowUtil.setMainWindowOrientation(getContext(), window.Orientation.UNSPECIFIED);
    // 检查是否支持设置核心系统能力
    if (canIUse('SystemCapability.Applications.Settings.Core')) {
      // 取消注册加速度计旋转状态的键观察者
      settings.unregisterKeyObserver(getContext(), settings.general.ACCELEROMETER_ROTATION_STATUS,
        settings.domainName.DEVICE_SHARED);
    }
  }

  // 定义私有方法用于初始化方向状态
  private initOrientationState(): void {
    // 判断设备宽度是否大于高度
    if (this.globalInfoModel.deviceWidth > this.globalInfoModel.deviceHeight) {
      // 设置开始屏幕方向为横屏
      this.beginScreenOrientation = ScreenOrientation.LANDSCAPE;
    } else {
      // 设置开始屏幕方向为竖屏
      this.beginScreenOrientation = ScreenOrientation.PORTRAIT;
    }
    // 检查是否支持设置核心系统能力
    if (canIUse('SystemCapability.Applications.Settings.Core')) {
      // 同步获取加速度计旋转状态的值
      this.beginOrientationLockState =
        settings.getValueSync(getContext(), settings.general.ACCELEROMETER_ROTATION_STATUS,
          settings.domainName.DEVICE_SHARED);
      // 注册键观察者
      this.registerKeyObserver();
    }
  }

  // 定义私有方法用于注册键观察者
  private registerKeyObserver(): boolean {
    // 检查是否支持设置核心系统能力
    if (canIUse('SystemCapability.Applications.Settings.Core')) {
      // 使用try-catch处理可能的异常
      try {
        // 注册加速度计旋转状态的键观察者
        return settings.registerKeyObserver(getContext(), settings.general.ACCELEROMETER_ROTATION_STATUS,
          settings.domainName.DEVICE_SHARED, () => {
            // 设置方向锁定改变状态为true
            this.isOrientationLockChange = true;
          });
      } catch (error) {
        // 将错误转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录错误日志
        Logger.error(TAG,
          `RegisterKeyObserver ${settings.general.ACCELEROMETER_ROTATION_STATUS} error: ${err.code}, ${err.message}`);
        // 返回false表示注册失败
        return false;
      }
    }
    // 返回false表示不支持该系统能力
    return false;
  }

  // 定义私有方法用于重置屏幕方向
  private resetScreenOrientation(): void {
    // 检查方向锁定是否未改变且开始方向锁定状态为开启
    if (!this.isOrientationLockChange && this.beginOrientationLockState === ORIENTATION_LOCK_ON) {
      // 检查开始断点是否为大屏或中屏
      if (this.beginBreakpoint === BreakpointTypeEnum.LG || this.beginBreakpoint === BreakpointTypeEnum.MD) {
        // 检查开始屏幕方向是否为横屏
        if (this.beginScreenOrientation === ScreenOrientation.LANDSCAPE) {
          // 设置主窗口方向为横屏
          WindowUtil.setMainWindowOrientation(getContext(), window.Orientation.LANDSCAPE);
        } else {
          // 设置主窗口方向为竖屏
          WindowUtil.setMainWindowOrientation(getContext(), window.Orientation.PORTRAIT);
        }
      }
    }
  }

  // 定义构建方法
  build() {
    // 创建导航目标组件
    NavDestination() {
      // 创建列布局
      Column() {
        // 创建代码预览组件
        CodePreviewComponent({
          // 设置返回页面回调
          onBackPage: () => {
            this.onBackPage();
          },
          // 传递Web节点控制器
          webNodeController: this.webNodeController,
          // 传递代码字符串
          code: this.code,
          // 传递组件名称
          componentName: this.componentName,
          // 设置页面容器为true
          pageContainer: true,
          // 传递顶部平移Y值
          topTranslateY: this.codePreviewState?.topTranslateY,
          // 传递底部平移Y值
          bottomTranslateY: this.codePreviewState?.bottomTranslateY,
          // 传递导航透明度
          navigationOpacity: this.codePreviewState?.navigationOpacity,
          // 传递焦点状态
          isFocus: this.isFocus,
          // 传递返回状态
          isBack: this.isBack,
        })
      }
      // 设置几何过渡动画
      .geometryTransition(CommonConstants.CODE_PREVIEW_GEOMETRY_ID)
      // 设置宽度为100%
      .width('100%')
      // 设置高度为100%
      .height('100%')
    }
    // 设置宽度为100%
    .width('100%')
    // 设置高度为100%
    .height('100%')
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置准备就绪回调
    .onReady((ctx: NavDestinationContext) => {
      // 获取导航参数
      const params: CodeViewParams = ctx.pathInfo.param as CodeViewParams;
      // 设置代码字符串
      this.code = params.code as string;
      // 设置组件名称
      this.componentName = params.componentName as string;
      // 获取Web节点控制器
      this.webNodeController = WebUtil.getWebNode(WebUtil.getComponentCodeUrl()) as WebNodeController;
      // 设置预完成动画回调
      this.preFinishAnimation = params.preFinishAnimation as () => void;
      // 获取代码预览状态
      this.codePreviewState = this.viewModel!.getState();
    })
    // 设置获得焦点回调
    .onFocus(() => {
      this.isFocus = true;
    })
    // 设置失去焦点回调
    .onBlur(() => {
      this.isFocus = false;
    })
    // 设置显示回调
    .onShown(() => {
      this.isBack = false;
    })
    // 设置返回按键回调
    .onBackPressed(() => {
      this.onBackPage();
      return true;
    })
  }

  // 定义私有预完成动画回调函数
  private preFinishAnimation: () => void = () => {
  };
}

// 使用Builder装饰器定义代码预览构建器函数
@Builder
export function CodePreviewBuilder() {
  // 创建代码预览组件
  CodePreview()
}
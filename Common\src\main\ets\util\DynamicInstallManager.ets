// 导入AbilityKit包中的通用类型和启动选项
import type { common, StartOptions } from '@kit.AbilityKit';
// 导入ArkUI包中的显示模块
import { display } from '@kit.ArkUI';
// 导入基础服务包中的业务错误和事件发射器
import { BusinessError, emitter } from '@kit.BasicServicesKit';
// 导入StoreKit包中的模块安装管理器
import { moduleInstallManager } from '@kit.StoreKit';
// 导入日志工具
import Logger from './Logger';
// 导入通用常量
import { CommonConstants } from '../constant/CommonConstants';

// 下载超时限制常量（秒）
const DOWNLOAD_TIMEOUT_LIMIT: number = 1800;
// 日志标签常量
const TAG: string = '[DynamicInstallManager]';

/**
 * 动态安装管理器类
 * 提供模块动态安装、下载进度监听和模块加载功能
 * 支持音视频能力模块的管理和窗口属性设置
 */
export class DynamicInstallManager {
  // 音视频能力模块列表
  private static aVAbilityList: string[] = [
    'KnocksharesampleAbility',      // 敲击分享示例能力
    'VideocastsampleAbility',       // 视频投屏示例能力
    'AudiointeractionsampleAbility', // 音频交互示例能力
  ];

  /**
   * 获取模块状态方法
   * 查询指定模块的安装状态
   * @param moduleName 模块名称
   * @returns 模块安装状态
   */
  public static getModuleStatus(moduleName: string): moduleInstallManager.InstallStatus {
    // 获取已安装模块信息
    const result: moduleInstallManager.InstalledModule = moduleInstallManager.getInstalledModule(moduleName);
    // 记录模块状态信息日志
    Logger.info(TAG, `getModuleStatus moduleName: ${result.moduleName}, installStatus: ${result.installStatus}`);
    // 返回安装状态
    return result.installStatus;
  }

  /**
   * 获取模块方法
   * 动态下载并安装指定模块
   * @param context UI能力上下文
   * @param moduleName 模块名称
   * @returns Promise<模块安装会话状态>
   */
  public static fetchModule(context: common.UIAbilityContext,
    moduleName: string): Promise<moduleInstallManager.ModuleInstallSessionState> {
    // 返回Promise对象
    return new Promise((resolve: (value: moduleInstallManager.ModuleInstallSessionState) => void,
      reject: (reason?: object) => void) => {
      try {
        // 记录获取模块信息日志
        Logger.info(TAG, `fetchModule moduleName: ${moduleName}`);
        // 创建模块安装提供者
        const myModuleInstallProvider: moduleInstallManager.ModuleInstallProvider =
          new moduleInstallManager.ModuleInstallProvider();
        // 创建模块安装请求
        const moduleInstallRequest: moduleInstallManager.ModuleInstallRequest =
          myModuleInstallProvider.createModuleInstallRequest(context);
        // 添加要安装的模块
        moduleInstallRequest.addModule(moduleName);
        // 执行模块获取操作
        moduleInstallManager.fetchModules(moduleInstallRequest)
          .then((data: moduleInstallManager.ModuleInstallSessionState) => {
            // 记录获取模块成功调试日志
            Logger.debug(TAG, `fetchModule success, result: ${JSON.stringify(data)}`);
            // 解析Promise并返回结果
            resolve(data);
          });
      } catch (error) {
        // 捕获异常并转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录请求安装模块失败错误日志
        Logger.error(TAG, `request installing module failed, error: ${err.code} ${err.message}`);
        // 拒绝Promise并返回错误
        reject(error);
      }
    });
  }

  /**
   * 取消下载任务方法
   * 取消指定的模块下载任务
   * @param taskId 任务ID，可选参数
   */
  public static cancelDownloadTask(taskId?: string): void {
    try {
      // 取消下载任务并获取返回码
      const rtnCode: moduleInstallManager.ReturnCode = moduleInstallManager.cancelTask(taskId);
      // 记录取消任务成功信息日志
      Logger.info(TAG, `Succeeded in getting result: ${rtnCode}`);
    } catch (error) {
      // 记录取消任务失败错误日志
      Logger.error(TAG, `cancelTask error code is ${error.code}, message is ${error.message}`);
    }
  }

  /**
   * 订阅下载进度方法
   * 监听模块安装状态变化并发送事件通知
   */
  public static subscribeDownloadProgress(): void {
    try {
      // 监听模块安装状态事件
      moduleInstallManager.on('moduleInstallStatus', (downloadData: moduleInstallManager.ModuleInstallSessionState) => {
        // 记录下载进度信息日志
        Logger.info(TAG,
          `subscribeDownloadProgress downloadsize: ${downloadData.downloadedSize}, totalsize: ${downloadData.totalSize}`);
        // 构造事件数据
        const eventData: emitter.EventData = {
          data: {
            // 任务状态
            'taskStatus': downloadData.taskStatus,
            // 已下载大小
            'downloadedSize': downloadData.downloadedSize,
            // 总大小
            'totalSize': downloadData.totalSize
          }
        };
        // 发送动态安装事件
        emitter.emit(CommonConstants.DYNAMIC_INSTALL_EVENT, eventData);
      }, DOWNLOAD_TIMEOUT_LIMIT);
      // 记录订阅下载进度成功信息日志
      Logger.info(TAG, 'subscribe download progress success');
    } catch (error) {
      // 记录订阅下载进度失败错误日志
      Logger.error(TAG, `subscribeDownloadProgress failed, error: ${error.code}, ${error.message}`);
    }
  }

  /**
   * 取消订阅下载进度方法
   * 停止监听模块安装状态变化
   */
  public static unsubscribeDownloadProgress(): void {
    try {
      // 取消监听模块安装状态事件
      moduleInstallManager.off('moduleInstallStatus');
      // 记录取消订阅下载进度成功信息日志
      Logger.info(TAG, 'unsubscribe download progress success');
    } catch (error) {
      // 记录取消监听失败错误日志
      Logger.error(TAG, `onListening error code is ${error.code}, message is ${error.message}`);
    }
  }

  /**
   * 加载模块方法
   * 启动指定的模块能力，处理音视频能力的特殊逻辑
   * @param context UI能力上下文
   * @param moduleAbility 模块能力名称
   * @returns Promise<void>
   */
  public static loadModule(context: common.UIAbilityContext, moduleAbility: string): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: (value: void) => void, reject: (reason?: BusinessError) => void) => {
      try {
        // 检查是否为音视频能力模块
        const isAVAbility: boolean = DynamicInstallManager.aVAbilityList.includes(moduleAbility);
        // 检查是否为相同的能力模块
        const isSameAbility: boolean = (moduleAbility === AppStorage.get('AVAbilityModule'));
        // 获取当前音视频能力上下文
        const aVAbilityContext: common.UIAbilityContext =
          AppStorage.get<common.UIAbilityContext>('AVAbilityContext') as common.UIAbilityContext;
        // 如果是音视频能力且不是相同能力且存在上下文
        if (isAVAbility && !isSameAbility && aVAbilityContext) {
          // 终止当前音视频能力
          aVAbilityContext.terminateSelf();
          // 删除音视频能力上下文
          AppStorage.delete('AVAbilityContext');
        }
        // 设置启动能力属性
        const option: StartOptions = DynamicInstallManager.setStartAbilityProperty();
        // 启动指定能力
        context.startAbility({ bundleName: context.abilityInfo.bundleName, abilityName: moduleAbility }, option)
          .then(() => {
            // 如果是音视频能力
            if (isAVAbility) {
              // 存储当前音视频能力模块名称
              AppStorage.setOrCreate('AVAbilityModule', moduleAbility);
            }
            // 记录启动能力成功信息日志
            Logger.info(TAG, `start ${moduleAbility} success}`);
            // 解析Promise
            resolve();
          })
          .catch((error: BusinessError) => {
            // 记录启动能力失败错误日志
            Logger.error(TAG,
              `start ${moduleAbility} failed, error code is ${error.code}, message is ${error.message}`);
            // 拒绝Promise并返回错误
            reject(error);
          });
      } catch (error) {
        // 捕获异常并转换为业务错误类型
        const err: BusinessError = error as BusinessError;
        // 记录启动能力失败错误日志
        Logger.error(TAG, `startAbility failed, error code is ${err.code}, message is ${err.message}`);
      }
    });
  }

  /**
   * 设置启动能力属性方法
   * 根据屏幕尺寸计算窗口属性
   * @returns 启动选项配置
   */
  public static setStartAbilityProperty(): StartOptions {
    // 获取默认显示器数据
    const displayData = display.getDefaultDisplaySync();
    // 计算窗口宽度（可用宽度 × 窗口比例）
    const windowWidth = displayData.availableWidth * CommonConstants.WINDOW_RATIO;
    // 计算窗口高度（可用高度 × 窗口比例）
    const windowHeight = displayData.availableHeight * CommonConstants.WINDOW_RATIO;
    // 计算窗口左边距（居中显示）
    const windowLeft = (displayData.availableWidth - windowWidth) / 2.0;
    // 计算窗口顶边距（居中显示）
    const windowTop = (displayData.availableHeight - windowHeight) / 2.0;
    // 构造启动选项配置
    const option: StartOptions = {
      // 最小窗口宽度（像素转vp，与最小宽度常量比较取较小值）
      minWindowWidth: Math.min(px2vp(windowWidth), CommonConstants.MIN_WINDOW_WIDTH),
      // 最小窗口高度（像素转vp，与最小高度常量比较取较小值）
      minWindowHeight: Math.min(px2vp(windowHeight), CommonConstants.MIN_WINDOW_HEIGHT),
      // 窗口左边距
      windowLeft: windowLeft,
      // 窗口顶边距
      windowTop: windowTop,
      // 窗口宽度
      windowWidth: windowWidth,
      // 窗口高度
      windowHeight: windowHeight,
    };
    // 返回启动选项配置
    return option;
  }
}
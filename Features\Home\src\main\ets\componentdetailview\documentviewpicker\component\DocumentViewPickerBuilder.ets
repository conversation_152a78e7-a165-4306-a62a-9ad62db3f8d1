// 导入能力工具包中的通用类型，用于获取应用上下文
import type { common } from '@kit.AbilityKit';
// 导入核心文件工具包中的选择器，用于文档选择功能
import { picker } from '@kit.CoreFileKit';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器
import { Logger } from '@ohos/common';
// 导入详情页面常量，用于获取对话框配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';

// 日志标签，用于标识日志来源
const TAG: string = '[DocumentViewPickerComponent]';

/**
 * 文档视图选择器构建器函数
 * 用于构建文档视图选择器组件的UI界面
 * @param _$$ 描述器包装对象，包含文档选择器的配置信息（此处未使用）
 */
@Builder
export function DocumentViewPickerBuilder(_$$: DescriptorWrapper) {
  // 创建文档视图选择器组件
  DocumentViewPickerComponent()
}

/**
 * 文档视图选择器组件
 * 提供文档选择功能，用户可以选择文档并显示选择结果
 */
@Component
struct DocumentViewPickerComponent {
  // 消息状态，用于存储选择结果信息
  @State message: string = '';
  // 标题状态，用于存储对话框标题
  @State title: string = '';

  /**
   * 读取文件方法
   * 启动文档选择器，允许用户选择文档并显示选择结果
   */
  readFile() {
    // 获取当前组件的应用上下文
    const context = getContext(this) as common.Context;
    try {
      // 创建文档选择选项配置
      const documentSelectOptions = new picker.DocumentSelectOptions();
      // 创建文档视图选择器实例
      const documentPicker = new picker.DocumentViewPicker(context);
      // 启动文档选择器并处理选择结果
      documentPicker.select(documentSelectOptions).then((documentSelectResult: string[]) => {
        // 检查是否有选择结果
        if (documentSelectResult.length === 0) {
          // 如果没有选择任何文档，直接返回
          return;
        }
        // 将选择结果转换为JSON字符串并存储
        this.message = JSON.stringify(documentSelectResult);
        // 显示警告对话框展示选择结果
        this.getUIContext().showAlertDialog(
          {
            // 设置对话框标题
            title: $r('app.string.File_path'),
            // 设置对话框消息内容
            message: this.message,
            // 启用自动取消功能
            autoCancel: true,
            // 设置对话框居中对齐
            alignment: DialogAlignment.Center,
            // 设置对话框偏移量
            offset: { dx: 0, dy: DetailPageConstant.ALERT_DIALOG_OFFSET_Y },
            // 设置网格列数
            gridCount: 3,
            // 设置对话框宽度
            width: DetailPageConstant.SELECT_RESULT_DIALOG_SIZE,
            // 设置对话框高度
            height: DetailPageConstant.SELECT_RESULT_DIALOG_SIZE,
            // 设置对话框圆角半径
            cornerRadius: $r('sys.float.corner_radius_level7'),
            // 设置边框宽度
            borderWidth: $r('app.float.border_width_normal'),
            // 设置边框样式为虚线
            borderStyle: BorderStyle.Dashed,
            // 设置边框颜色为蓝色
            borderColor: Color.Blue,
            // 设置背景颜色为白色
            backgroundColor: Color.White,
            // 设置文本样式，允许单词换行
            textStyle: { wordBreak: WordBreak.BREAK_ALL },
            // 设置确认按钮配置
            confirm: {
              // 设置按钮文本
              value: $r('app.string.dialog_confirm'),
              // 设置按钮点击回调
              action: () => {
                // 记录确认按钮点击日志
                Logger.info(TAG, 'Confirm button is clicked.');
              },
            },
            // 设置对话框即将关闭时的回调
            onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
              // 如果是按返回键关闭
              if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
                // 执行关闭操作
                dismissDialogAction.dismiss();
              }
              // 如果是点击外部区域关闭
              if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
                // 执行关闭操作
                dismissDialogAction.dismiss();
              }
            },
          }
        )
      }).catch((err: BusinessError) => {
        // 记录文档选择失败的错误日志
        Logger.error(TAG, `DocumentViewPicker.select failed with err: ${err.code}, ${err.message}`);
      });
    } catch (error) {
      // 捕获并处理异常
      const err: BusinessError = error as BusinessError;
      // 记录文档选择器创建失败的错误日志
      Logger.error(TAG, `DocumentViewPicker failed with err: ${err.code}, ${err.message}`);
    }
  }

  /**
   * 构建组件UI界面
   * 创建包含文档选择按钮的主界面布局
   */
  build() {
    // 创建垂直列布局
    Column() {
      // 创建文档选择按钮
      Button($r('app.string.select_document'))
        // 设置按钮背景颜色为次要背景色
        .backgroundColor($r('sys.color.background_secondary'))
        // 设置按钮宽度
        .width($r('app.float.document_select_button_width'))
        // 设置按钮高度
        .height($r('app.float.button_height_normal'))
        // 设置字体颜色为强调色
        .fontColor($r('sys.color.font_emphasize'))
        // 设置字体粗细为中等
        .fontWeight(FontWeight.Medium)
        // 设置字体大小为大号正文
        .fontSize($r('sys.float.Body_L'))
        // 设置按钮点击事件
        .onClick(() => {
          // 调用读取文件方法
          this.readFile();
        })
    }
    // 设置列布局宽度为100%
    .width('100%')
    // 设置列布局高度为100%
    .height('100%')
    // 设置内容居中对齐
    .justifyContent(FlexAlign.Center)
  }
}
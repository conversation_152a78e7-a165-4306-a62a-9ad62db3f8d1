// 导入方舟UI工具包中的提示操作模块，用于显示提示信息
import { promptAction } from '@kit.ArkUI';
// 导入基础服务工具包中的业务错误类型，用于错误处理
import { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器，用于记录日志信息
import { Logger } from '@ohos/common';
// 导入详情页面常量，包含各种配置参数
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入操作表描述器类型，用于描述操作表组件的属性
import type { ActionSheetDescriptor } from '../viewmodel/ActionSheetDescriptor';

// 定义日志标签常量，用于标识当前模块的日志输出
const TAG: string = '[ActionSheetBuilder]';

/**
 * 操作表构建器函数
 * 用于构建操作表组件的UI界面，包含触发按钮和操作表配置
 * @param $$ 描述器包装对象，包含操作表的配置信息
 */
@Builder
export function ActionSheetBuilder($$: DescriptorWrapper) {
  // 创建弹性布局容器，设置为垂直方向，居中对齐
  Flex({ direction: FlexDirection.Column, alignItems: ItemAlign.Center, justifyContent: FlexAlign.Center }) {
    // 创建触发操作表的按钮
    Button($r('app.string.action_sheet_tip'), { buttonStyle: ButtonStyleMode.NORMAL })
      // 设置按钮字体粗细为中等
      .fontWeight(FontWeight.Medium)
      // 设置按钮字体大小为系统大号字体
      .fontSize($r('sys.float.Body_L'))
      // 设置按钮字体颜色为系统强调色
      .fontColor($r('sys.color.font_emphasize'))
      // 设置按钮点击事件处理函数
      .onClick(() => {
        // 显示操作表对话框
        ActionSheet.show({
          // 设置操作表标题
          title: $r('app.string.title'),
          // 设置操作表副标题
          subtitle: $r('app.string.subtitle'),
          // 设置操作表消息内容
          message: $r('app.string.content'),
          // 设置是否自动取消，从描述器中获取配置
          autoCancel: ($$.descriptor as ActionSheetDescriptor).autoCancel,
          // 设置过渡动画效果，包含出现和消失动画
          transition: TransitionEffect.asymmetric(
            // 设置出现动画，从描述器中获取配置
            ($$.descriptor as ActionSheetDescriptor).transitionAppear,
            // 设置消失动画，从描述器中获取配置
            ($$.descriptor as ActionSheetDescriptor).transitionDisappear
          ),
          // 配置确认按钮
          confirm: {
            // 设置默认焦点为true
            defaultFocus: true,
            // 设置确认按钮文本
            value: $r('app.string.dialog_confirm'),
            // 设置确认按钮点击事件处理函数
            action: () => {
              // 使用try-catch处理可能的异常
              try {
                // 显示确认点击的提示信息
                promptAction.showToast({
                  // 设置提示消息内容
                  message: $r('app.string.confirm_click'),
                  // 设置提示显示时长
                  duration: DetailPageConstant.LONG_DURATION,
                });
              } catch (err) {
                // 捕获异常并转换为业务错误类型
                const error: BusinessError = err as BusinessError;
                // 记录错误日志，包含错误代码和错误消息
                Logger.error(TAG, `Show toast error, the code is ${error.code}}, the message is ${error.message}`);
              }
            },
          },
          // 设置对话框对齐方式为居中
          alignment: DialogAlignment.Center,
          // 设置对话框偏移量，水平偏移为0，垂直偏移从常量中获取
          offset: { dx: 0, dy: DetailPageConstant.ACTION_SHEET_OFFSET_Y },
          // 设置操作表选项列表，从描述器中获取配置
          sheets: ($$.descriptor as ActionSheetDescriptor).sheetInfo,
        });
      })
  }
  // 设置弹性布局容器宽度为100%
  .width('100%')
  // 设置弹性布局容器高度为100%
  .height('100%')
}
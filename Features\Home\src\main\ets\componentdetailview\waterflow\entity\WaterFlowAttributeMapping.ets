// 导入通用数字映射和通用字符串映射，用于瀑布流组件属性配置
import { CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

/**
 * 弹性方向映射类
 * 用于存储弹性布局方向的代码字符串和实际值的映射关系
 */
class FlexDirectionMapping {
  // 代码字符串属性，用于代码生成
  public code: string;
  // 弹性方向值属性，存储实际的弹性方向
  public value: FlexDirection;

  /**
   * 构造函数
   * @param code 代码字符串，用于生成代码
   * @param value 弹性方向值，实际的弹性方向
   */
  constructor(code: string, value: FlexDirection) {
    // 初始化代码字符串
    this.code = code;
    // 初始化弹性方向值
    this.value = value;
  }
}

export const layoutDirectionMapData: Map<string, FlexDirectionMapping> = new Map([
  ['Default', new FlexDirectionMapping('FlexDirection.Column', FlexDirection.Column)],
  ['Column', new FlexDirectionMapping('FlexDirection.Column', FlexDirection.Column)],
  ['Row', new FlexDirectionMapping('FlexDirection.Row', FlexDirection.Row)],
  ['RowReverse', new FlexDirectionMapping('FlexDirection.RowReverse', FlexDirection.RowReverse)],
  ['ColumnReverse', new FlexDirectionMapping('FlexDirection.ColumnReverse', FlexDirection.ColumnReverse)],
]);

export const frictionMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('0.75', 0.75)],
  ['0.1', new CommonNumberMapping('0.1', 0.1)],
  ['0.6', new CommonNumberMapping('0.6', 0.6)],
  ['0.75', new CommonNumberMapping('0.75', 0.75)],
  ['0.9', new CommonNumberMapping('0.9', 0.9)],
]);

export const columnsTemplateMapData: Map<string, CommonStringMapping> = new Map([
  ['Default', new CommonStringMapping('1fr', '1fr')],
]);

export const rowsTemplateMapData: Map<string, CommonStringMapping> = new Map([
  ['Default', new CommonStringMapping('1fr', '1fr')],
]);

export const columnsGapMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('0', 0)],
]);
// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用描述器基类，用于实现描述器功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入堆叠布局对齐映射数据
import { stackAlignMapData } from '../entity/StackAttributeMapping';

/**
 * 堆叠布局组件描述器类
 * 继承通用描述器，专门用于描述堆叠布局组件的配置
 * 包含对齐内容等配置信息
 */
@Observed
export class StackDescriptor extends CommonDescriptor {
  // 对齐内容方式，默认使用映射数据中的默认值
  public alignContent: Alignment = stackAlignMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性数组转换为堆叠布局组件的具体配置
   * @param attributes 原始属性数组，包含组件配置信息
   */
  public convert(attributes: OriginAttribute[]): void {
    // 查找对齐方向属性的索引位置
    const index: number = attributes.findIndex((item) => item.name === 'alignDirection');
    // 对齐方向值，默认为空字符串
    let alignDirection: string = '';
    // 如果找到了对齐方向属性
    if (index >= 0) {
      alignDirection = attributes[index].currentValue;
    }
    // 遍历所有属性，根据对齐方向设置对应的对齐内容
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'alignContentTop':
          // 如果对齐方向为顶部，设置顶部对齐内容
          if (alignDirection === 'Top') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.value ?? this.alignContent;
          }
          break;
        case 'alignContentCenter':
          // 如果对齐方向为居中，设置居中对齐内容
          if (alignDirection === 'Center') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.value ?? this.alignContent;
          }
          break;
        case 'alignContentBottom':
          // 如果对齐方向为底部，设置底部对齐内容
          if (alignDirection === 'Bottom') {
            this.alignContent = stackAlignMapData.get(attribute.currentValue)?.value ?? this.alignContent;
          }
          break;
        default:
          // 未知属性，不做处理
          break;
      }
    });
  }
}
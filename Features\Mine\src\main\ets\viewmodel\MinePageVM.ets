// 导入通用模块中的基础视图模型和基础视图模型事件
import { BaseVM, BaseVMEvent } from '@ohos/common';
// 导入我的页面状态
import { MinePageState } from './MinePageState';

// 定义我的页面视图模型类
export class MinePageVM extends BaseVM<MinePageState> {
  // 定义静态实例
  private static instance: MinePageVM;

  // 定义获取实例的静态方法
  public static getInstance() {
    // 如果实例不存在则创建新实例
    if (!MinePageVM.instance) {
      MinePageVM.instance = new MinePageVM();
    }
    // 返回实例
    return MinePageVM.instance;
  }

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数并传入我的页面状态
    super(new MinePageState());
  }

  // 定义发送事件的公共方法
  public sendEvent(event: BaseVMEvent): void {
    // 如果是关于页面绑定弹窗事件
    if (event instanceof AboutBindSheetEvent) {
      // 设置关于视图显示状态
      this.state.aboutViewShow = event.dataValue;
    } else if (event instanceof FeedbackBindSheetEvent) {
      // 如果是反馈绑定弹窗事件则设置反馈视图显示状态
      this.state.feedbackViewShow = event.dataValue;
    }
  }
}

// 定义关于页面绑定弹窗事件类
export class AboutBindSheetEvent implements BaseVMEvent {
  // 定义只读数据值
  readonly dataValue: boolean;

  // 定义构造函数
  constructor(dataValue: boolean) {
    // 设置数据值
    this.dataValue = dataValue;
  }
}

// 定义反馈绑定弹窗事件类
export class FeedbackBindSheetEvent implements BaseVMEvent {
  // 定义只读数据值
  readonly dataValue: boolean;

  // 定义构造函数
  constructor(dataValue: boolean) {
    // 设置数据值
    this.dataValue = dataValue;
  }
}
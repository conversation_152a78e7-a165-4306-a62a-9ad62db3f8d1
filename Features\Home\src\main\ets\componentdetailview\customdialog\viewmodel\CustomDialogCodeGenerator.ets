// 导入原始属性类型，用于处理组件属性
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入通用代码生成器基类，用于实现代码生成接口
import { CommonCodeGenerator } from '../../../viewmodel/CommonCodeGenerator';
// 导入自定义对话框相关的映射数据和样式枚举
import {
  CustomDialogStyle,
  dialogBuilderCodeMapData,
  dialogImportCodeMapData,
  dialogStyleMapData,
} from '../entity/CustomDialogAttributeMapping';

/**
 * 自定义对话框代码生成器类
 * 实现通用代码生成器接口，用于生成自定义对话框相关的代码
 */
export class CustomDialogCodeGenerator implements CommonCodeGenerator {
  // 对话框样式属性，默认使用图文弹窗样式
  private style: CustomDialogStyle = dialogStyleMapData.get('Default')!;

  /**
   * 生成自定义对话框代码的方法
   * 根据传入的属性数组生成完整的自定义对话框组件代码字符串
   * @param attributes 原始属性数组，包含对话框的各种配置属性
   * @returns 生成的自定义对话框组件代码字符串
   */
  public generate(attributes: OriginAttribute[]): string {
    // 初始化构建器参数代码，默认使用提示对话框构建器
    let builderParamCode: string = 'this.tipDialogBuilder';
    // 初始化复选框状态参数代码，默认为空
    let isCheckedParamCode: string = '';
    // 遍历所有属性，根据属性名称更新对应的代码字符串
    attributes.forEach((attribute) => {
      // 使用switch语句处理不同的属性类型
      switch (attribute.name) {
        // 处理样式属性
        case 'style':
          // 从映射数据中获取对应的对话框样式，如果不存在则保持原值
          this.style = dialogStyleMapData.get(attribute.currentValue) ?? this.style;
          // 根据对话框样式设置不同的构建器参数和状态参数
          if (this.style === CustomDialogStyle.StyleProgress) {
            // 进度弹窗使用进度对话框构建器
            builderParamCode = 'this.progressDialogBuilder';
            // 进度弹窗不需要复选框状态
            isCheckedParamCode = '';
          } else {
            // 图文弹窗使用提示对话框构建器
            builderParamCode = 'this.tipDialogBuilder';
            // 图文弹窗需要复选框状态参数
            isCheckedParamCode = `
  @State isChecked: boolean = true;`;
          }
          break;
        // 默认情况，不做任何处理
        default:
          break;
      }
    });
    // 返回生成的完整自定义对话框组件代码字符串
    return `${dialogImportCodeMapData.get(this.style)}

@Component
export struct CustomDialogComponent {${isCheckedParamCode}
  private dialogController: CustomDialogController | undefined = new CustomDialogController({
    builder: ${builderParamCode},
    cancel: this.existApp,
    onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      if (dismissDialogAction.reason === DismissReason.PRESS_BACK) {
        dismissDialogAction.dismiss();
      }
      if (dismissDialogAction.reason === DismissReason.TOUCH_OUTSIDE) {
        dismissDialogAction.dismiss();
      }
    },
    alignment: DialogAlignment.Center,
    autoCancel: true,
    cornerRadius: $r('sys.float.padding_level10')
  });

${dialogBuilderCodeMapData.get(this.style)}

  aboutToDisappear() {
    this.dialogController = undefined;
  }

  onCancel() {
    console.info('Callback when the first button is clicked');
  }

  onAccept() {
    console.info('Callback when the second button is clicked');
  }

  existApp() {
    console.info('Click the callback in the blank area');
  }

  build() {
    Column() {
      Button('自定义${this.style}')
        .buttonStyle(ButtonStyleMode.NORMAL)
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))
        .onClick(() => {
          this.dialogController?.open();
        })
    }
    .width('100%')
  }
}`
  }
}
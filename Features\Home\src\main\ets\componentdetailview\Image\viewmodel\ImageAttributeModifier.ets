// 导入通用属性修改器基类，用于实现属性修改功能
import { CommonAttributeModifier } from '../../../viewmodel/CommonAttributeModifier';
// 导入图片描述器类型，用于描述图片组件配置
import type { ImageDescriptor } from './ImageDescriptor';

/**
 * 图片属性修改器类
 * 继承自通用属性修改器，用于修改图片组件的属性
 * 支持响应式数据绑定和属性动态更新
 */
@Observed
export class ImageAttributeModifier extends CommonAttributeModifier<ImageDescriptor, ImageAttribute> {
  /**
   * 应用普通属性到图片组件实例
   * 将描述器中的属性值应用到实际的图片组件上
   * @param instance 图片属性实例，用于设置图片组件属性
   */
  public applyNormalAttribute(instance: ImageAttribute): void {
    // 分配对象适配属性，将描述器中的objectFit值应用到组件实例
    this.assignAttribute((descriptor => descriptor.objectFit), (val) => instance.objectFit(val));
  }
}
// 导入方舟UI工具包中的曲线模块
import { curves } from '@kit.ArkUI';
// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举、通用常量和产品系列枚举
import { BreakpointType, BreakpointTypeEnum, CommonConstants, ProductSeriesEnum } from '@ohos/common';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';
// 导入体验项目组件
import { ExperienceItem } from './ExperienceItem';

// 定义行内边距常量
const ROW_PADDING = 64;
// 定义显示数量常量
const SHOW_COUNT = 3;
// 定义放大系数常量
const ENLARGE_COEFFICIENTS = 2;

// 使用Component装饰器定义体验卡片组件
@Component
export struct ExperienceCard {
  // 使用StorageProp和Watch装饰器获取全局信息模型并监听变化
  @StorageProp('GlobalInfoModel') @Watch('calculateItemWidth') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用Prop和Require装饰器定义发现内容数组属性
  @Prop @Require discoverContents: DiscoverContent[];
  // 定义处理项目点击函数
  handleItemClick?: Function;
  // 定义组件宽度
  componentWidth: number = this.globalInfoModel.deviceWidth;
  // 创建滚动控制器
  scroller: Scroller = new Scroller()
  // 使用State装饰器定义放大索引状态
  @State enlargeIndex: number = -1;
  // 使用State装饰器定义项目宽度状态
  @State itemWidth: number = 0;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 计算项目宽度
    this.calculateItemWidth();
  }

  // 定义计算项目宽度的方法
  calculateItemWidth() {
    // 如果当前断点为XL
    if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 计算组件宽度
      this.componentWidth = this.globalInfoModel.deviceWidth - CommonConstants.SIDE_BAR_WIDTH - ROW_PADDING;
      // 计算项目宽度
      this.itemWidth = (this.componentWidth - CommonConstants.SPACE_16 * (SHOW_COUNT - 1)) / SHOW_COUNT;
    }
  }

  // 定义悬停操作方法
  onHoverAction(isHover: boolean, index: number) {
    // 执行动画
    animateTo({ curve: curves.interpolatingSpring(0, 1, 288, 30) }, () => {
      // 如果悬停且放大索引不等于当前索引
      if (isHover && this.enlargeIndex !== index) {
        // 获取当前X轴偏移量
        const currentOffsetX: number = this.scroller.currentOffset().xOffset;
        // 屏幕边缘的项目不处理
        if (((index * (this.itemWidth + CommonConstants.SPACE_16) - currentOffsetX) < -CommonConstants.SPACE_16) ||
          ((index + 1) * this.itemWidth - currentOffsetX) > this.componentWidth) {
          return;
        }
        // 计算当前项目在屏幕上的位置
        const currentItemEdgePosition: number =
          (index + ENLARGE_COEFFICIENTS) * this.itemWidth - currentOffsetX;
        // 设置放大索引
        this.enlargeIndex = index;
        // 如果当前项目边缘位置超出组件宽度
        if (currentItemEdgePosition > this.componentWidth) {
          // 滚动到指定位置
          this.scroller.scrollTo({
            xOffset: (currentOffsetX + this.itemWidth +
            CommonConstants.SPACE_16),
            yOffset: 0,
          });
        }
      } else if (!isHover) {
        // 如果不悬停则重置放大索引
        this.enlargeIndex = -1;
      }
    });
  }

  // 定义构建方法
  build() {
    // 如果当前断点为XL
    if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 创建滚动容器
      Scroll(this.scroller) {
        // 创建行布局
        Row({ space: CommonConstants.SPACE_16 }) {
          // 遍历发现内容数组
          ForEach(this.discoverContents, (discoverContent: DiscoverContent, index: number) => {
            // 创建体验项目组件
            ExperienceItem({ discoverContent: discoverContent })
              // 设置高度为100%
              .height('100%')
              // 根据是否为放大索引设置宽度
              .width(index === this.enlargeIndex ?
                (this.itemWidth * ENLARGE_COEFFICIENTS + CommonConstants.SPACE_16) : this.itemWidth)
              // 设置悬停事件
              .onHover((isHover: boolean) => this.onHoverAction(isHover, index))
              // 设置点击事件
              .onClick(() => {
                // 调用处理项目点击函数
                this.handleItemClick?.(discoverContent);
              })
          }, (discoverContent: DiscoverContent) => discoverContent.id.toString())
        }
        // 设置内边距
        .padding({
          left: $r('sys.float.padding_level16'),
          right: $r('sys.float.padding_level16'),
        })
        // 设置高度为100%
        .height('100%')
      }
      // 设置滚动吸附
      .scrollSnap({ snapAlign: ScrollSnapAlign.START, snapPagination: this.itemWidth + CommonConstants.SPACE_16 })
      // 隐藏滚动条
      .scrollBar(BarState.Off)
      // 设置边缘效果为无
      .edgeEffect(EdgeEffect.None)
      // 设置可滚动方向为水平
      .scrollable(ScrollDirection.Horizontal)
      // 设置高度
      .height($r('app.float.img_card_height'))
      // 设置宽度为100%
      .width('100%')
    } else {
      // 创建滑块组件
      Swiper() {
        // 遍历发现内容数组
        ForEach(this.discoverContents, (discoverContent: DiscoverContent) => {
          // 创建体验项目组件
          ExperienceItem({ discoverContent: discoverContent })
            // 设置点击事件
            .onClick(() => {
              // 调用处理项目点击函数
              this.handleItemClick?.(discoverContent);
            })
        }, (discoverContent: DiscoverContent) => discoverContent.id.toString())
      }
      // 设置前边距
      .prevMargin(new BreakpointType<Length>({
        sm: 0,
        md: $r('sys.float.padding_level6'),
        lg: CommonConstants.SPACE_16 + CommonConstants.TAB_BAR_WIDTH,
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 设置后边距
      .nextMargin(new BreakpointType<Length>({
        sm: 0,
        md: $r('sys.float.padding_level6'),
        lg: $r('sys.float.padding_level8'),
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 根据断点类型设置循环
      .loop(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM)
      // 设置项目间距
      .itemSpace(new BreakpointType({
        sm: CommonConstants.SPACE_8,
        md: CommonConstants.SPACE_12,
        lg: CommonConstants.SPACE_16,
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 设置显示数量
      .displayCount(new BreakpointType({
        sm: CommonConstants.LANE_SM,
        md: CommonConstants.LANE_MD,
        lg: CommonConstants.LANE_LG,
      }).getValue(this.globalInfoModel.currentBreakpoint))
      // 设置边缘效果为无
      .effectMode(EdgeEffect.None)
      // 设置指示器
      .indicator((this.discoverContents.length > 1 &&
        this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM) ?
      new DotIndicator()
        .color($r('sys.color.icon_on_tertiary'))
        .selectedColor($r('sys.color.icon_on_primary')) :
        false)
      // 设置宽度为100%
      .width('100%')
      // 根据产品系列设置高度
      .height(deviceInfo.productSeries === ProductSeriesEnum.VDE ? $r('app.float.img_card_height_verde') :
      $r('app.float.img_card_height'))
    }
  }
}
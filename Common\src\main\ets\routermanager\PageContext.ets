// 导入日志工具
import Logger from '../util/Logger';

/**
 * 路由参数接口
 * 定义页面路由跳转时需要的参数结构
 */
export interface RouterParam {
  // 路由名称，用于标识目标页面
  routerName: string;
  // 路由参数，可选的传递给目标页面的数据对象
  param?: object;
}

/**
 * 页面上下文接口
 * 定义页面导航操作的标准接口
 * 包含打开页面、返回页面和替换页面等基本操作
 */
export interface IPageContext {
  /**
   * 打开新页面方法
   * @param data 路由参数
   * @param animated 是否使用动画，可选参数
   */
  openPage(data: RouterParam, animated?: boolean): void;

  /**
   * 返回上一页方法
   * @param animated 是否使用动画，可选参数
   */
  popPage(animated?: boolean): void;

  /**
   * 替换当前页面方法
   * @param data 路由参数
   * @param animated 是否使用动画，可选参数
   */
  replacePage(data: RouterParam, animated?: boolean): void;
}

// 日志标签常量
const TAG = '[PageContext]';

/**
 * 页面上下文实现类
 * 实现IPageContext接口，提供页面导航的具体功能
 * 基于NavPathStack实现页面栈管理
 */
export class PageContext implements IPageContext {
  // 导航路径栈，用于管理页面导航历史
  private readonly pathStack: NavPathStack;

  /**
   * 构造函数
   * 初始化页面上下文，创建导航路径栈实例
   */
  constructor() {
    // 创建新的导航路径栈实例
    this.pathStack = new NavPathStack();
  }

  /**
   * 替换当前页面方法
   * 用新页面替换当前页面，不会增加页面栈深度
   * @param data 路由参数，包含目标页面信息
   * @param animated 是否使用动画效果，默认为true
   */
  public replacePage(data: RouterParam, animated: boolean = true): void {
    try {
      // 调用路径栈的替换方法
      this.pathStack.replacePath({
        name: data.routerName,
        param: data.param,
      }, animated);
    } catch (err) {
      // 记录替换页面失败的错误日志
      Logger.error(TAG, `Open Page ${data.routerName} failed. ${err.code} ${err.message}.`);
    }
  }

  /**
   * 打开新页面方法
   * 在当前页面栈顶部推入新页面
   * @param data 路由参数，包含目标页面信息
   * @param animated 是否使用动画效果，默认为true
   */
  public openPage(data: RouterParam, animated: boolean = true): void {
    try {
      // 调用路径栈的推入方法
      this.pathStack.pushPath({
        name: data.routerName,
        param: data.param,
      }, animated);
    } catch (err) {
      // 记录打开页面失败的错误日志
      Logger.error(TAG, `Open Page ${data.routerName} failed. ${err.code} ${err.message}.`);
    }
  }

  /**
   * 返回上一页方法
   * 从页面栈中弹出当前页面，返回到上一个页面
   * @param animated 是否使用动画效果，默认为true
   */
  public popPage(animated: boolean = true): void {
    try {
      // 调用路径栈的弹出方法
      this.pathStack.pop(animated);
    } catch (err) {
      // 记录返回页面失败的错误日志
      Logger.error(TAG, `Pop Page failed. ${err.code} ${err.message}.`);
    }
  }

  /**
   * 获取导航路径栈的访问器方法
   * 提供对内部导航路径栈的只读访问
   * @returns 导航路径栈实例
   */
  public get navPathStack(): NavPathStack {
    return this.pathStack;
  }
}
// 导入单个示例数据类型
import type { SingleSampleData } from '../model/SampleDetailData';
// 导入示例服务
import { SampleService } from '../service/SampleService';

// 导出示例详情模型类
export class SampleDetailModel {
  // 定义示例服务私有属性
  private service: SampleService = new SampleService();
  // 定义静态实例私有属性
  private static instance: SampleDetailModel;

  // 定义私有构造函数
  private constructor() {
  }

  // 定义获取实例的静态方法
  public static getInstance(): SampleDetailModel {
    // 如果实例不存在则创建新实例
    if (!SampleDetailModel.instance) {
      SampleDetailModel.instance = new SampleDetailModel();
    }
    // 返回实例
    return SampleDetailModel.instance;
  }

  // 定义获取示例卡片详情的方法
  public getSampleCardDetails(sampleCardId: number): Promise<SingleSampleData[]> {
    // 首先尝试从偏好设置获取示例详情
    return this.service.getSampleDetailsByPreference(sampleCardId)
      .then((data: SingleSampleData[]) => {
        // 返回获取到的数据
        return data;
      })
      .catch(() => {
        // 如果偏好设置获取失败则从服务获取示例详情
        return this.service.getSampleDetails(sampleCardId)
          .then((data: SingleSampleData[]) => {
            // 将获取到的数据设置到偏好设置
            this.service.setSampleDetailToPreference(sampleCardId, data);
            // 返回获取到的数据
            return data;
          });
      });
  }
}
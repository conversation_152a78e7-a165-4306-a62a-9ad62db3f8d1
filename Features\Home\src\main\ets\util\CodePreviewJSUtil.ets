// 导入基础服务工具包中的业务错误类型定义
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的日志记录器和Web工具
import { Logger, WebUtil } from '@ohos/common';

// 定义标签常量用于日志输出
const TAG: string = '[CodePreviewJSUtil]';

// 导出代码预览JavaScript工具类
export class CodePreviewJSUtil {
  // 定义静态方法用于在代码视图中运行JavaScript
  public static codeViewRunJS(jsMethod: string, params?: string, callback?: Function): void {
    // 检查JavaScript方法是否在白名单中
    if (WHITE_JS_METHODS.indexOf(jsMethod) >= 0) {
      // 初始化运行方法变量
      let runMethod = jsMethod;
      // 如果参数存在且不为空
      if (params && params.trim() !== '') {
        // 替换方法中的参数占位符
        runMethod = runMethod.replace('%param', params);
      }
      // 使用try-catch处理可能的异常
      try {
        // 获取Web控制器并运行JavaScript方法
        const promise = WebUtil.getWebController(WebUtil.getComponentCodeUrl())?.runJavaScript(runMethod);
        // 如果回调函数存在且Promise有效，则在Promise完成后执行回调
        callback && promise?.then(() => {
          callback();
        })
      } catch (err) {
        // 将错误转换为业务错误类型
        const error: BusinessError = err as BusinessError;
        // 记录错误日志
        Logger.error(TAG, `RunJavaScript error, the code is ${error.code}, the message is ${error.message}`);
      }
    } else {
      // 记录方法不在白名单中的错误日志
      Logger.error(TAG, `Input method ${jsMethod} not in whitelist`);
    }
  }
}

// 定义JavaScript方法白名单常量
const WHITE_JS_METHODS = [
  // 改变颜色模式方法
  'changeColorMode(%param)',
  // 显示竖屏视图方法
  'showPortraitView()',
  // 代码转HTML方法
  'codeToHtml(%param)',
  // 切换到全屏方法
  'toFullScreen()',
  // 切换到小屏方法
  'toSmallScreen()',
  // 显示横屏视图方法
  'showLandscapeView(%param)',
];
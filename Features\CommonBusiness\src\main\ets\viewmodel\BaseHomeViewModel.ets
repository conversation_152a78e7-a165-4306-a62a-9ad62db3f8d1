// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入ArkUI工具包中的曲线动画
import { curves } from '@kit.ArkUI';
// 导入基础服务工具包中的设备信息
import { deviceInfo } from '@kit.BasicServicesKit';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的基础视图模型、断点类型、断点类型枚举、通用常量、日志工具、页面上下文、产品系列枚举、状态栏颜色类型、窗口工具
import {
  BaseVM,
  BreakpointType,
  BreakpointTypeEnum,
  CommonConstants,
  Logger,
  PageContext,
  ProductSeriesEnum,
  StatusBarColorType,
  WindowUtil,
} from '@ohos/common';
// 导入横幅数据模型类型
import type { BannerData } from '../model/BannerData';
// 导入横幅数据模型中的横幅缩放因子、横幅类型枚举、标题缩放因子
import { BANNER_SCALE_FACTOR, BannerTypeEnum, TITLE_SCALE_FACTOR } from '../model/BannerData';
// 导入路由参数接口类型
import type { ArticleDetailParams, ComponentDetailParams, SampleDetailParams } from '../model/RouterParams';
// 导入标签状态栏模型中的标签内容状态数组和标签栏类型
import { TAB_CONTENT_STATUSES, TabBarType } from '../model/TabStatusBarModel';
// 导入基础首页状态类型
import type { BaseHomeState } from './BaseHomeState';

// 日志标签常量
const TAG = '[BaseHomeViewModel]';
// 大屏横幅高度常量
const BANNER_HEIGHT_LG = 242;
// 标题最大缩放比例常量
const TITLE_MAX_SCALE = 1.1;
// 标题最小缩放比例常量
const TITLE_MIN_SCALE = 1;
// 标题偏移因子常量
const TITLE_OFFSET_FACTOR = 0.01;

/**
 * 基础首页视图模型类
 * 继承自BaseVM，管理首页相关的业务逻辑和状态
 * 支持泛型，可以扩展不同类型的首页状态
 */
@Observed
export class BaseHomeViewModel<T extends BaseHomeState> extends BaseVM<BaseHomeState> {
  // 状态对象，受保护属性
  protected state: T;
  // 分页大小，只读属性，默认为30
  protected readonly pageSize: number = 30;
  // 原始横幅高度，私有属性，用于动画回弹
  private originBannerHeight: number = 0;
  // 回弹动画曲线，私有属性
  private springBackAnimation: curves.ICurve = curves.interpolatingSpring(0, 1, 288, 30);

  /**
   * 构造函数
   * 初始化视图模型，设置导航栏样式和横幅高度
   * @param initialState 初始状态对象
   */
  public constructor(initialState: T) {
    // 调用父类构造函数
    super(initialState);
    // 设置状态对象
    this.state = initialState;
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 判断是否为大屏宽度
    const isLargeWidth: boolean = (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL);
    // 根据屏幕大小设置导航栏背景颜色
    this.state.topNavigationData.bgColor = isLargeWidth ? '#FFF1F3F5' : '#00FFFFFF';
    // 根据屏幕大小设置导航栏标题颜色
    this.state.topNavigationData.titleColor = isLargeWidth ? StatusBarColorType.BLACK : StatusBarColorType.WHITE;
    // 计算导航标题高度
    const naviTitleHeight: number =
      globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8;
    // 根据断点类型设置横幅高度
    this.state.bannerState.bannerHeight = new BreakpointType({
      sm: deviceInfo.productSeries === ProductSeriesEnum.VDE ?
        CommonConstants.BANNER_ASPECT_VERDE * globalInfoModel.deviceWidth :
        CommonConstants.BANNER_ASPECT_SM * globalInfoModel.deviceWidth,
      md: CommonConstants.BANNER_ASPECT_MD * globalInfoModel.deviceWidth,
      lg: BANNER_HEIGHT_LG + naviTitleHeight,
      xl: BANNER_HEIGHT_LG + naviTitleHeight,
    }).getValue(globalInfoModel.currentBreakpoint);
    // 同步横幅高度到状态对象
    this.state.bannerHeight = this.state.bannerState.bannerHeight;
    // 保存原始横幅高度
    this.originBannerHeight = this.state.bannerHeight;
  }

  /**
   * 获取状态对象
   * 返回当前的状态实例
   * @returns 状态对象
   */
  public getState(): T {
    return this.state;
  }

  /**
   * 发送事件
   * 根据事件类型分发到对应的处理方法
   * @param eventParam 事件参数，包含事件类型和参数
   * @returns 处理结果，布尔值或void
   */
  public sendEvent<P>(eventParam: BaseHomeEventParam<P>): boolean | void {
    // 获取事件类型
    const eventType: BaseHomeEventType = eventParam.type;
    // 根据事件类型分发处理
    if (eventType === BaseHomeEventType.JUMP_BANNER_DETAIL) {
      // 跳转横幅详情
      return this.jumpBannerDetail(eventParam.param as BannerData);
    } else if (eventType === BaseHomeEventType.HANDLE_SCROLL_OFFSET) {
      // 处理滚动偏移
      return this.handleListOffset(eventParam.param as OffsetParam);
    } else if (eventType === BaseHomeEventType.CALCULATE_BANNER_HEIGHT) {
      // 计算横幅高度
      return this.calculateBannerHeight(eventParam.param as CalculateHeightParam);
    } else if (eventType === BaseHomeEventType.CHANGE_BANNER_HEIGHT) {
      // 改变横幅高度
      return this.changeBannerHeight();
    } else if (eventType === BaseHomeEventType.HANDLE_BREAKPOINT_CHANGE) {
      // 处理断点变化
      return this.handleBreakpointChange(eventParam.param as OffsetParam);
    } else if (eventType === BaseHomeEventType.HANDLE_COLOR_CHANGE) {
      // 处理颜色模式变化
      return this.handleColorModeChange(eventParam.param as OffsetParam);
    }
    // 未知事件类型返回false
    return false;
  }

  /**
   * 计算横幅高度
   * 根据滚动参数动态调整横幅高度和标题样式
   * @param param 计算高度参数
   * @returns 是否需要更新UI
   */
  protected calculateBannerHeight(param: CalculateHeightParam): boolean {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 根据Y轴偏移设置边缘效果状态
    if (param.yOffset === 0) {
      this.state.hasEdgeEffect = false;
    } else {
      this.state.hasEdgeEffect = param.offset > 0;
    }
    // 大屏或向下滚动时不处理横幅高度变化
    if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL || param.yOffset > 0) {
      return false;
    }
    // 处理滚动状态下的横幅高度变化
    if (param.state === ScrollState.Scroll && param.offset !== 0) {
      // 设置当前滚动状态为滚动中
      this.state.currentScrollState = ScrollState.Scroll;
      // 计算当前横幅高度
      const currentBannerHeight: number = this.state.bannerHeight - param.offset;
      // 如果当前高度小于原始高度，恢复原始高度
      if (currentBannerHeight < this.originBannerHeight) {
        this.state.currentScrollState = ScrollState.Fling;
        this.state.bannerState.bannerHeight = this.originBannerHeight;
        this.state.bannerHeight = this.state.bannerState.bannerHeight;
        return false;
      }
      // 计算横幅偏移量
      const bannerOffset: number = -(param.offset / BANNER_SCALE_FACTOR);
      // 更新横幅高度
      this.state.bannerState.bannerHeight += bannerOffset;
      this.state.bannerHeight = this.state.bannerState.bannerHeight;
      // 计算标题偏移量
      const titleOffset: number = bannerOffset / TITLE_SCALE_FACTOR;
      const titleOffsetY: number = this.state.topNavigationData.titleOffsetY + titleOffset;

      // 更新标题Y轴偏移，确保不小于0
      this.state.topNavigationData.titleOffsetY = titleOffsetY > 0 ? titleOffsetY : 0;
      // 计算标题缩放比例
      let titleScale = this.state.topNavigationData.titleScale + TITLE_OFFSET_FACTOR * titleOffset;
      // 限制标题缩放比例在最大最小值之间
      if (titleScale > TITLE_MAX_SCALE) {
        titleScale = TITLE_MAX_SCALE;
      } else if (titleScale < TITLE_MIN_SCALE) {
        titleScale = TITLE_MIN_SCALE;
      }
      // 更新标题缩放比例
      this.state.topNavigationData.titleScale = titleScale;
      return true;
    } else if (this.state.currentScrollState === ScrollState.Scroll && param.state === ScrollState.Fling) {
      // 从滚动状态切换到惯性滚动时，执行横幅回弹动画
      this.bannerSpringBack();
      return true;
    }
    return false;
  }

  /**
   * 处理断点变化
   * 当屏幕断点发生变化时，重新计算横幅高度并处理列表偏移
   * @param offsetParam 偏移参数
   */
  protected handleBreakpointChange(offsetParam: OffsetParam) {
    // 重新计算横幅高度
    this.changeBannerHeight();
    // 标记为断点变化
    offsetParam.breakpointChange = true;
    // 处理列表偏移
    this.handleListOffset(offsetParam);
  }

  /**
   * 处理颜色模式变化
   * 当系统颜色模式发生变化时，更新导航栏标题颜色
   * @param offsetParam 偏移参数
   */
  protected handleColorModeChange(offsetParam: OffsetParam) {
    // 获取页面上下文
    const pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
    // 如果不在根页面，直接返回
    if (pageContext.navPathStack.size() > 1) {
      return;
    }
    // 判断是否为深色模式
    const isDark: boolean = AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    if (isDark) {
      // 深色模式下设置白色标题
      this.state.topNavigationData.titleColor = `rgba(255,255,255, 1)`;
    } else {
      // 浅色模式下标记断点变化并处理列表偏移
      offsetParam.breakpointChange = true;
      this.handleListOffset(offsetParam);
    }
  }

  /**
   * 改变横幅高度
   * 根据当前断点类型重新计算横幅高度
   */
  protected changeBannerHeight(): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 计算导航标题高度
    const naviTitleHeight: number =
      globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8;
    // 根据断点类型设置横幅高度
    this.state.bannerState.bannerHeight = new BreakpointType({
      sm: (deviceInfo.productSeries === ProductSeriesEnum.VDE) ?
        CommonConstants.BANNER_ASPECT_VERDE * globalInfoModel.deviceWidth :
        CommonConstants.BANNER_ASPECT_SM * globalInfoModel.deviceWidth,
      md: CommonConstants.BANNER_ASPECT_MD * globalInfoModel.deviceWidth,
      lg: BANNER_HEIGHT_LG + naviTitleHeight,
      xl: CommonConstants.BANNER_ASPECT_XL *
      Math.floor((globalInfoModel.deviceWidth - CommonConstants.SIDE_BAR_WIDTH) / 2.7) + naviTitleHeight,
    }).getValue(globalInfoModel.currentBreakpoint);
    // 同步横幅高度到状态对象
    this.state.bannerHeight = this.state.bannerState.bannerHeight;
    // 更新原始横幅高度
    this.originBannerHeight = this.state.bannerHeight;
  }

  /**
   * 处理列表偏移
   * 根据滚动偏移量动态调整导航栏样式和状态栏颜色
   * @param offsetParam 偏移参数
   */
  protected handleListOffset(offsetParam: OffsetParam): void {
    // 输出调试日志
    Logger.debug(TAG, `onDidScroll: ${JSON.stringify(offsetParam)}`);
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 判断是否为深色模式
    const isDark: boolean = AppStorage.get('systemColorMode') === ConfigurationConstant.ColorMode.COLOR_MODE_DARK;
    // 获取页面上下文
    const pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
    // 计算是否显示示例分类标签栏
    if (offsetParam.tabIndex === TabBarType.SAMPLE) {
      // 计算顶部边距
      const marginTop: number = globalInfoModel.statusBarHeight + CommonConstants.NAVIGATION_HEIGHT;
      // 计算显示标签的偏移量
      const showTabOffset: number = this.state.bannerState.bannerHeight - marginTop;
      // 设置是否显示标签
      this.state.topNavigationData.showTab = (offsetParam.yOffset >= showTabOffset);
    }

    // 如果滚动偏移超过横幅高度或在大屏设备上
    if (offsetParam.yOffset > this.state.bannerState.bannerHeight ||
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 如果是断点变化触发的
      if (offsetParam.breakpointChange) {
        // 启用模糊效果
        this.state.topNavigationData.isBlur = true;
        // 如果是当前标签且在根页面，更新状态栏颜色
        if (offsetParam.tabIndex === AppStorage.get('currentTabIndex') && pageContext.navPathStack.size() === 1) {
          WindowUtil.updateStatusBarColor(getContext(), isDark);
        }
        // 设置标签内容状态为非深色
        TAB_CONTENT_STATUSES[offsetParam.tabIndex] = false;
        // 根据颜色模式设置标题颜色
        let colorData: number = 255;
        if (!isDark) {
          colorData = 0;
        }
        this.state.topNavigationData.titleColor = `rgba(${colorData},${colorData},${colorData}, 1)`;
      }
      // 根据Y轴偏移量控制模糊效果
      if (offsetParam.yOffset >= CommonConstants.SPACE_8) {
        this.state.topNavigationData.isBlur = true;
      } else {
        this.state.topNavigationData.isBlur = false;
      }
      return;
    }

    // 如果Y轴偏移为0且当前滚动状态为1，执行横幅回弹
    if (offsetParam.yOffset === 0 && this.state.currentScrollState === 1) {
      this.bannerSpringBack();
    }
    // 计算横幅有效高度
    const bannerHeight: number = this.state.bannerHeight -
      (CommonConstants.NAVIGATION_HEIGHT + globalInfoModel.statusBarHeight) * 2;
    // 获取Y轴偏移的绝对值
    const yOffset: number = Math.abs(offsetParam.yOffset || 0);
    // 计算透明度
    let opacity: number = yOffset >= bannerHeight ? (yOffset - bannerHeight) / CommonConstants.NAVIGATION_HEIGHT : 0;
    // 限制透明度最大值为1
    if (opacity >= 1) {
      opacity = 1;
    }
    // 根据透明度设置模糊效果和标签内容状态
    if (opacity > 0) {
      this.state.topNavigationData.isBlur = true;
      TAB_CONTENT_STATUSES[offsetParam.tabIndex] = false;
    } else {
      this.state.topNavigationData.isBlur = false;
      TAB_CONTENT_STATUSES[offsetParam.tabIndex] = true;
    }
    // 如果是当前标签且在根页面，更新状态栏颜色
    if (offsetParam.tabIndex === AppStorage.get('currentTabIndex') && pageContext.navPathStack.size() === 1) {
      WindowUtil.updateStatusBarColor(getContext(), opacity > 0 ? isDark : true);
    }
    // 根据透明度和颜色模式计算标题颜色
    let colorData: number = 255;
    if (!isDark) {
      colorData = 255 - opacity * 255;
    }
    this.state.topNavigationData.titleColor = `rgba(${colorData},${colorData},${colorData}, 1)`;
  }

  /**
   * 跳转横幅详情
   * 根据横幅类型跳转到对应的详情页面
   * @param banner 横幅数据
   */
  protected jumpBannerDetail(banner: BannerData): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 获取页面上下文
    const pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
    // 根据横幅类型进行不同的跳转处理
    if (banner.bannerType === BannerTypeEnum.COMPONENT) {
      // 组件类型横幅
      // 根据断点类型选择页面上下文
      const componentPageContext: PageContext =
        globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('componentListPageContext')! :
          pageContext;
      // 构建组件详情参数
      const componentDetailParam: ComponentDetailParams = {
        componentName: banner.bannerTitle,
        componentId: Number(banner.bannerValue),
      };
      // 打开组件详情页面
      componentPageContext.openPage({
        routerName: 'ComponentDetailView',
        param: componentDetailParam,
      }, true);
    } else if (banner.bannerType === BannerTypeEnum.SAMPLE) {
      // 示例类型横幅
      // 根据断点类型选择页面上下文
      const samplePageContext: PageContext =
        globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? AppStorage.get('samplePageContext')! :
          pageContext;
      // 构建示例详情参数
      const sampleParam: SampleDetailParams = {
        currentIndex: 0,
        sampleCardId: Number(banner.bannerValue),
      };
      // 打开示例详情页面
      samplePageContext.openPage({
        routerName: 'SampleDetailView',
        param: sampleParam,
      }, true);

    } else if (banner.bannerType === BannerTypeEnum.ARTICLE) {
      // 文章类型横幅
      // 获取当前页面路径栈
      let currentPageContext: PageContext = pageContext;
      // 如果是超大屏，根据标签类型选择对应的页面上下文
      if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
        if (banner.tabViewType === TabBarType.HOME) {
          currentPageContext = AppStorage.get('componentListPageContext')!;
        } else if (banner.tabViewType === TabBarType.SAMPLE) {
          currentPageContext = AppStorage.get('samplePageContext')!;
        } else {
          currentPageContext = AppStorage.get('explorationPageContext')!;
        }
      }
      // 构建文章详情参数
      const articleParam: ArticleDetailParams = {
        id: Number(banner.bannerValue),
        title: banner.bannerTitle,
        detailsUrl: banner.detailsUrl,
        isArticle: false,
        tabViewType: globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? banner.id : banner.tabViewType,
      };
      // 使用动画打开横幅详情页面
      animateTo({ curve: curves.interpolatingSpring(0, 1, 273, 33) }, () => {
        currentPageContext.openPage({
          routerName: 'BannerDetailView',
          param: articleParam,
        }, false);
      });
    }
  }

  /**
   * 横幅回弹动画
   * 当滚动结束时，将横幅恢复到原始状态
   * 私有方法，用于内部调用
   */
  private bannerSpringBack() {
    // 设置当前滚动状态为2
    this.state.currentScrollState = 2;
    // 执行回弹动画
    animateTo({ curve: this.springBackAnimation, duration: 250 }, () => {
      // 恢复横幅高度
      this.changeBannerHeight();
      // 重置标题Y轴偏移
      this.state.topNavigationData.titleOffsetY = 0;
      // 重置标题缩放比例
      this.state.topNavigationData.titleScale = 1;
    });
  }
}

/**
 * 计算高度参数接口
 * 用于传递计算横幅高度所需的参数
 */
export interface CalculateHeightParam {
  // Y轴偏移量
  yOffset: number;
  // 偏移量
  offset: number;
  // 滚动状态
  state: ScrollState;
}

/**
 * 偏移参数接口
 * 用于传递处理列表偏移所需的参数
 */
export interface OffsetParam {
  // Y轴偏移量
  yOffset: number;
  // 标签索引
  tabIndex: number;
  // 是否为断点变化，可选参数，用于指示是否处理断点变化
  breakpointChange?: boolean;
}

/**
 * 基础首页事件类型枚举
 * 定义首页视图模型支持的事件类型
 */
export enum BaseHomeEventType {
  // 跳转横幅详情事件
  JUMP_BANNER_DETAIL = 'jumpBannerDetail',
  // 处理滚动偏移事件
  HANDLE_SCROLL_OFFSET = 'handleScrollOffset',
  // 计算横幅高度事件
  CALCULATE_BANNER_HEIGHT = 'calculateBannerHeight',
  // 改变横幅高度事件
  CHANGE_BANNER_HEIGHT = 'changeBannerHeight',
  // 横幅回弹事件
  BANNER_SPRING_BACK = 'bannerSpringBack',
  // 处理断点变化事件
  HANDLE_BREAKPOINT_CHANGE = 'handleBreakpointChange',
  // 处理颜色模式变化事件
  HANDLE_COLOR_CHANGE = 'handleColorModeChange',
}

/**
 * 基础首页事件参数接口
 * 用于封装事件类型和参数的泛型接口
 */
export interface BaseHomeEventParam<T> {
  // 事件类型
  type: BaseHomeEventType;
  // 事件参数
  param: T;
}
// 导入基础服务工具包中的业务错误类型定义
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的响应数据类型定义
import type { ResponseData } from '@ohos/common';
// 导入通用模块中的日志记录器、模拟请求和偏好设置管理器
import { Logger, MockRequest, PreferenceManager } from '@ohos/common';
// 导入组件数据类型定义
import type { ComponentData } from '../model/ComponentData';
// 导入组件详情数据类型定义
import type { ComponentDetailData } from '../model/ComponentDetailData';

// 定义日志标签常量用于日志输出标识
const TAG = '[ComponentLibraryService]';

// 导出组件库服务类
export class ComponentLibraryService {
  // 定义主页面数据缓存键的静态只读属性
  private static readonly MAIN_PAGE_DATA = 'COMPONENT_LIBRARY_MAIN_PAGE_DATA';
  // 定义详情页面数据缓存键的静态只读属性
  private static readonly DETAIL_PAGE_DATA = 'COMPONENT_LIBRARY_DETAIL_PAGE_DATA';

  // 构造函数，创建服务实例
  constructor() {
    // 空构造函数实现
  }

  // 定义公共方法用于通过模拟请求获取组件列表
  public getComponentListByMock(): Promise<ResponseData<ComponentData>> {
    // 返回新的Promise对象处理异步请求
    return new Promise((resolve: (value: ResponseData<ComponentData>) => void,
      reject: (reason?: Object) => void) => {
      // 调用模拟请求方法获取组件页面数据
      MockRequest.call<ResponseData<ComponentData>>(ComponentLibraryTrigger.COMPONENT_PAGE)
        // 处理请求成功的情况
        .then((result: Object) => {
          // 将结果转换为指定类型并通过resolve返回
          resolve(result as ResponseData<ComponentData>);
        })
        // 处理请求失败的情况
        .catch((error: BusinessError) => {
          // 通过reject返回错误信息
          reject(error);
        });
    });
  }

  // 定义公共方法用于获取组件列表
  public getComponentList(currentPage?: number, pageSize?: number): Promise<ResponseData<ComponentData>> {
    // 使用日志记录器输出请求参数信息
    Logger.info(TAG, `getComponentList param: currentPage ${currentPage}, pageSize: ${pageSize} `);
    // 调用模拟请求方法并返回结果
    return this.getComponentListByMock();
  }

  // 定义公共方法用于从偏好设置获取组件列表
  public getComponentListByPreference(currentPage: number, pageSize: number): Promise<ResponseData<ComponentData>> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: (value: ResponseData<ComponentData>) => void,
      reject: (reason?: string) => void) => {
      // 获取偏好设置管理器实例
      PreferenceManager.getInstance()
        // 从偏好设置中获取主页面数据
        .getValue<Record<string, ResponseData<ComponentData>>>(ComponentLibraryService.MAIN_PAGE_DATA)
        // 处理获取数据成功的情况
        .then((resp) => {
          // 检查响应数据是否存在
          if (!resp) {
            // 如果没有数据则拒绝Promise
            reject('There is no data in the Preference');
          }
          // 将响应数据转换为指定类型
          resp = (resp as Record<string, ResponseData<ComponentData>>);
          // 根据当前页码和页面大小构建键名获取对应数据
          const ret = resp[`${currentPage}_${pageSize}`];
          // 检查获取的数据是否存在
          if (!ret) {
            // 如果没有对应数据则拒绝Promise
            reject('There is no data in the Preference');
          }
          // 通过resolve返回获取的数据
          resolve(ret);
        })
        // 处理获取数据失败的情况
        .catch((error: BusinessError) => {
          // 使用日志记录器输出错误信息
          Logger.error(TAG, `get getComponentListByPreference failed, error: ${error.code}, ${error.message}`);
          // 通过reject返回错误消息
          reject(error.message);
        });
    });
  }

  // 定义公共方法用于将组件列表数据保存到偏好设置
  public setComponentListToPreference(data: ResponseData<ComponentData>): Promise<void> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: () => void) => {
      // 获取偏好设置管理器实例并检查是否已有主页面数据
      PreferenceManager.getInstance().hasValue(ComponentLibraryService.MAIN_PAGE_DATA)
        // 处理检查结果
        .then((result) => {
          // 判断是否已存在数据
          if (result) {
            // 获取偏好设置管理器实例
            PreferenceManager.getInstance()
              // 从偏好设置中获取现有的主页面数据
              .getValue<Record<string, ResponseData<ComponentData>>>(ComponentLibraryService.MAIN_PAGE_DATA)
              // 处理获取数据成功的情况
              .then((resp) => {
                // 将响应数据转换为指定类型
                resp = (resp as Record<string, ResponseData<ComponentData>>);
                // 根据当前页码和页面大小构建键名并添加新数据
                resp[`${data.currentPage}_${data.pageSize}`] = data;
                // 获取偏好设置管理器实例并保存更新后的数据
                PreferenceManager.getInstance().setValue(ComponentLibraryService.MAIN_PAGE_DATA, resp);
                // 通过resolve表示操作完成
                resolve();
              });
          } else {
            // 创建新的记录对象用于存储数据
            const record: Record<string, ResponseData<ComponentData>> = {};
            // 根据当前页码和页面大小构建键名并设置数据
            record[`${data.currentPage}_${data.pageSize}`] = data;
            // 获取偏好设置管理器实例并保存新创建的记录
            PreferenceManager.getInstance().setValue(ComponentLibraryService.MAIN_PAGE_DATA, record);
          }
        });
    });
  }

  // 定义公共方法用于获取组件详情数据
  public getComponentDetail(componentId: number): Promise<ComponentDetailData> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: (value: ComponentDetailData) => void, reject: (reason?: Object) => void) => {
      // 调用方法通过模拟请求获取组件详情列表
      this.getComponentDetailListByMock().then((result: ComponentDetailData[]) => {
        // 在结果数组中查找指定ID的组件详情数据
        const item = result.find((item: ComponentDetailData) => item.id === componentId);
        // 判断是否找到对应的数据项
        if (item !== undefined) {
          // 通过resolve返回找到的数据项
          resolve(item);
        } else {
          // 使用日志记录器输出错误信息
          Logger.error(TAG, `getComponentDetailListByMock failed, error: can't find detail data by id ${componentId}`);
          // 通过reject表示操作失败
          reject();
        }
      });
    });
  }

  // 定义公共方法用于从偏好设置获取组件详情数据
  public getComponentDetailByPreference(componentId: number): Promise<ComponentDetailData> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: (value: ComponentDetailData) => void,
      reject: (reason?: Object) => void) => {
      // 获取偏好设置管理器实例
      PreferenceManager.getInstance()
        // 从偏好设置中获取详情页面数据
        .getValue<Record<string, ComponentDetailData>>(ComponentLibraryService.DETAIL_PAGE_DATA)
        // 处理获取数据成功的情况
        .then((resp) => {
          // 检查响应数据是否存在
          if (!resp) {
            // 如果没有数据则拒绝Promise
            reject('There is no data in the Preference');
          }
          // 将响应数据转换为指定类型
          resp = (resp as Record<string, ComponentDetailData>)
          // 根据组件ID转换为字符串键名获取对应数据
          const ret = resp[String(componentId)];
          // 检查获取的数据是否存在
          if (!ret) {
            // 如果没有对应数据则拒绝Promise
            reject('There is no data in the Preference');
          }
          // 通过resolve返回获取的数据
          resolve(ret);
        });
    });
  }

  // 定义公共方法用于将组件详情数据保存到偏好设置
  public setComponentDetailToPreference(componentId: number, data: ComponentDetailData): Promise<void> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: () => void) => {
      // 获取偏好设置管理器实例并检查是否已有详情页面数据
      PreferenceManager.getInstance().hasValue(ComponentLibraryService.DETAIL_PAGE_DATA)
        // 处理检查结果
        .then((result) => {
          // 判断是否已存在数据
          if (result) {
            // 获取偏好设置管理器实例
            PreferenceManager.getInstance()
              // 从偏好设置中获取现有的详情页面数据
              .getValue<Record<string, ComponentDetailData>>(ComponentLibraryService.DETAIL_PAGE_DATA)
              // 处理获取数据成功的情况
              .then((resp) => {
                // 将响应数据转换为指定类型
                resp = (resp as Record<string, ComponentDetailData>);
                // 根据组件ID转换为字符串键名并添加新数据
                resp[String(componentId)] = data;
                // 获取偏好设置管理器实例并保存更新后的数据
                PreferenceManager.getInstance().setValue(ComponentLibraryService.DETAIL_PAGE_DATA, resp);
                // 通过resolve表示操作完成
                resolve();
              })
          } else {
            // 创建新的记录对象用于存储数据
            const record: Record<string, ComponentDetailData> = {};
            // 根据组件ID转换为字符串键名并设置数据
            record[String(componentId)] = data;
            // 获取偏好设置管理器实例并保存新创建的记录
            PreferenceManager.getInstance().setValue(ComponentLibraryService.DETAIL_PAGE_DATA, record);
          }
        });
    });
  }

  // 定义公共方法用于批量将组件详情数据保存到偏好设置
  public setDetailsToPreference(details: ComponentDetailData[]): Promise<void> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: () => void) => {
      // 获取偏好设置管理器实例并检查是否已有详情页面数据
      PreferenceManager.getInstance().hasValue(ComponentLibraryService.DETAIL_PAGE_DATA)
        // 处理检查结果
        .then((result) => {
          // 判断是否已存在数据
          if (result) {
            // 获取偏好设置管理器实例
            PreferenceManager.getInstance()
              // 从偏好设置中获取现有的详情页面数据
              .getValue<Record<string, ComponentDetailData>>(ComponentLibraryService.DETAIL_PAGE_DATA)
              // 处理获取数据成功的情况
              .then((resp: Record<string, ComponentDetailData> | null) => {
                // 创建记录对象，如果响应为空则使用空对象
                const record: Record<string, ComponentDetailData> = resp || {};
                // 遍历详情数据数组，逐个添加到记录中
                details.forEach((detail: ComponentDetailData) => {
                  // 根据详情数据的ID转换为字符串键名并设置数据
                  record[String(detail.id)] = detail;
                })
                // 获取偏好设置管理器实例并保存更新后的数据
                PreferenceManager.getInstance().setValue(ComponentLibraryService.DETAIL_PAGE_DATA, record);
                // 通过resolve表示操作完成
                resolve();
              })
          } else {
            // 创建新的记录对象用于存储数据
            const record: Record<string, ComponentDetailData> = {};
            // 遍历详情数据数组，逐个添加到记录中
            details.forEach((detail: ComponentDetailData) => {
              // 根据详情数据的ID转换为字符串键名并设置数据
              record[String(detail.id)] = detail;
            });
            // 获取偏好设置管理器实例并保存新创建的数据
            PreferenceManager.getInstance().setValue(ComponentLibraryService.DETAIL_PAGE_DATA, record);
          }
        });
    });
  }

  // 定义公共方法用于通过模拟请求获取组件详情列表
  public getComponentDetailListByMock(): Promise<ComponentDetailData[]> {
    // 返回新的Promise对象处理异步操作
    return new Promise((resolve: (value: ComponentDetailData[]) => void,
      reject: (reason?: Object) => void) => {
      // 调用模拟请求方法获取组件详情列表数据
      MockRequest.call<ComponentDetailData[]>(ComponentLibraryTrigger.COMPONENT_DETAIL_LIST)
        // 处理请求成功的情况
        .then((result: ComponentDetailData[]) => {
          // 将结果转换为组件详情数据数组类型
          const details: ComponentDetailData[] = result as ComponentDetailData[];
          // 调用方法将获取的详情数据保存到偏好设置
          this.setDetailsToPreference(details);
          // 通过resolve返回转换后的结果
          resolve(result as ComponentDetailData[]);
        })
        // 处理请求失败的情况
        .catch((error: BusinessError) => {
          // 通过reject返回错误信息
          reject(error);
        });
    });
  }

  // 定义公共方法用于获取组件详情列表
  public getComponentDetailList(): Promise<ComponentDetailData[]> {
    // 调用模拟请求方法并返回结果
    return this.getComponentDetailListByMock();
  }
}

// 定义组件库触发器枚举
enum ComponentLibraryTrigger {
  // 组件页面触发器，用于获取组件页面数据
  COMPONENT_PAGE = 'component-page',
  // 组件详情触发器，用于获取单个组件详情
  COMPONENT_DETAIL = 'component-details',
  // 组件详情列表触发器，用于获取组件详情列表
  COMPONENT_DETAIL_LIST = 'file-data',
  // 代码实验室详情触发器，用于获取代码实验室详情
  CODELAB_DETAIL = 'codelab-details',
}
// 导入详情页常量，包含容器边框等配置
import { DetailPageConstant } from '../../../constant/DetailPageConstant';
// 导入描述器包装类型，用于包装组件描述器
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
// 导入图片属性修改器，用于修改组件属性
import { ImageAttributeModifier } from '../viewmodel/ImageAttributeModifier';
// 导入图片描述器类型，用于描述组件配置
import type { ImageDescriptor } from '../viewmodel/ImageDescriptor';

/**
 * 图片组件构建器函数
 * 用于构建图片组件，包含背景层和前景层的双层结构
 * @param $$ 描述器包装对象，包含组件配置信息
 */
@Builder
export function ImageBuilder($$: DescriptorWrapper) {
  // 创建堆叠布局容器
  Stack() {
    // 创建背景图片组件
    Image($r('app.media.image_src340'))
      // 设置图片宽度
      .width($r('app.float.image_component_width'))
      // 根据对象适配方式动态设置高度
      .height((($$.descriptor as ImageDescriptor).objectFit === ImageFit.Cover ||
        ($$.descriptor as ImageDescriptor).objectFit === ImageFit.Auto) ? 'auto' :
      $r('app.float.image_component_height'))
      // 设置颜色滤镜
      .colorFilter(new ColorFilter(($$.descriptor as ImageDescriptor).colorFilterMatrix))
      // 设置属性修改器，用于动态修改组件属性
      .attributeModifier(new ImageAttributeModifier($$.descriptor as ImageDescriptor))
      // 设置透明度
      .opacity(DetailPageConstant.IMAGE_OPACITY)
    // 创建前景堆叠布局
    Stack() {
      // 创建背景列组件
      Column()
        // 设置宽度为100%
        .width('100%')
        // 设置高度为100%
        .height('100%')
        // 设置背景透明度
        .opacity(DetailPageConstant.IMAGE_OPACITY_BG)
        // 设置圆角半径
        .borderRadius($r('sys.float.corner_radius_level8'))
        // 设置背景颜色
        .backgroundColor($r('sys.color.multi_color_02'))

      // 创建前景列组件
      Column() {
        // 创建前景图片组件
        Image($r('app.media.image_src340'))
          // 设置颜色滤镜
          .colorFilter(new ColorFilter(($$.descriptor as ImageDescriptor).colorFilterMatrix))
          // 设置属性修改器
          .attributeModifier(new ImageAttributeModifier($$.descriptor as ImageDescriptor))
      }
      // 设置圆角半径
      .borderRadius($r('sys.float.corner_radius_level8'))
      // 设置裁剪属性
      .clip(($$.descriptor as ImageDescriptor).clip)
    }
    // 设置前景堆叠布局宽度
    .width($r('app.float.image_component_width'))
    // 设置前景堆叠布局高度
    .height($r('app.float.image_component_height'))
  }
}
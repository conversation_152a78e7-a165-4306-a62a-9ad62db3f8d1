// 导入原始属性类型，用于属性转换处理
import type { OriginAttribute } from '../../../viewmodel/Attribute';
// 导入高亮颜色映射数据，用于颜色属性处理
import { highlightColorMap } from '../../common/entity/CommonMapData';
// 导入通用描述器基类，用于继承通用功能
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
// 导入文本输入相关的属性映射数据
import { textInputFontMapData, textInputTypeMapData } from '../entity/TextInputAttributeMapping';

/**
 * 文本输入描述器类
 * 继承自通用描述器，用于描述文本输入组件的配置信息
 * 支持响应式数据绑定和属性转换
 */
@Observed
export class TextInputDescriptor extends CommonDescriptor {
  // 输入类型属性，默认使用默认值
  public type: InputType = textInputTypeMapData.get('Default')!.value;
  // 字体颜色属性，默认使用默认值
  public fontColor: string = highlightColorMap.get('Default')!.value;
  // 占位符字体属性，默认使用默认值
  public placeholderFont: Font = textInputFontMapData.get('Default')!.value;

  /**
   * 转换属性方法
   * 将原始属性转换为描述器属性
   * @param attributes 原始属性数组，包含需要转换的属性
   */
  public convert(attributes: OriginAttribute[]): void {
    // 遍历所有属性进行转换
    attributes.forEach((attribute) => {
      // 根据属性名称进行不同的转换处理
      switch (attribute.name) {
        // 处理输入类型属性
        case 'type':
          this.type =
            textInputTypeMapData.get(attribute.currentValue)?.value ?? textInputTypeMapData.get('Default')!.value;
          break;
        case 'fontColor':
          this.fontColor = attribute.currentValue;
          break;
        case 'placeholderFont':
          this.placeholderFont =
            textInputFontMapData.get(attribute.currentValue)?.value ?? textInputFontMapData.get('Default')!.value;
          break;
        default:
          break;
      }
    });
  }
}